"use strict";(self["webpackChunkautojs_web_control"]=self["webpackChunkautojs_web_control"]||[]).push([[36],{2036:function(e,t,s){s.d(t,{A:function(){return d}});var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"device-info-component"},[t("el-card",{staticClass:"device-card",staticStyle:{"margin-bottom":"10px"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",{staticStyle:{"font-size":"14px","font-weight":"bold",color:"#409EFF"}},[t("i",{staticClass:"el-icon-mobile-phone"}),e._v(" 设备信息 ")]),t("div",{staticStyle:{float:"right"}},[e.hasDevices&&e.enableBatchSelect?t("el-button-group",{staticStyle:{"margin-right":"8px"}},[t("el-button",{attrs:{size:"mini",type:"primary",disabled:e.allDevicesSelected},on:{click:e.selectAllDevices}},[e._v(" 全选 ")]),t("el-button",{attrs:{size:"mini",type:"default",disabled:0===e.selectedDeviceIds.length},on:{click:e.clearAllDevices}},[e._v(" 清空 ")]),t("el-button",{attrs:{size:"mini",type:"success",disabled:0===e.availableDevices.filter(e=>this.canSelectDevice(e)).length},on:{click:e.showOnlineDevicesDialog}},[e._v(" 批量选择 ")])],1):e._e(),t("el-button",{staticStyle:{padding:"3px 8px","font-size":"12px"},attrs:{type:"text",loading:e.refreshing},on:{click:e.refreshDeviceInfo}},[e._v(" 刷新 ")])],1)]),t("div",{staticClass:"device-content"},[e.hasDevices?t("div",{staticClass:"device-list"},[e.enableBatchSelect?t("div",{staticClass:"batch-select-info"},[t("el-alert",{staticStyle:{"margin-bottom":"10px"},attrs:{title:`已选择 ${e.selectedDeviceIds.length} 个设备`,type:"info",closable:!1,"show-icon":""}},[t("template",{slot:"default"},[0===e.selectedDeviceIds.length?t("span",[e._v("请选择要执行脚本的设备")]):t("span",[e._v(" 已选择: "+e._s(e.getSelectedDeviceNames())+" ")])])],2)],1):e._e(),e._l(e.availableDevices,function(s){return t("div",{key:s.device_id,staticClass:"device-item",class:{selected:e.enableBatchSelect?e.selectedDeviceIds.includes(s.device_id):e.selectedDeviceId===s.device_id,"batch-mode":e.enableBatchSelect,busy:"busy"===s.status,disabled:"busy"===s.status&&!e.isDeviceExecutingCurrentFunction(s.device_id)},on:{click:function(t){e.canSelectDevice(s)&&e.selectDevice(s)}}},[t("div",{staticClass:"device-header"},[e.enableBatchSelect?t("el-checkbox",{staticStyle:{"margin-right":"8px"},attrs:{value:e.selectedDeviceIds.includes(s.device_id),disabled:"busy"===s.status&&!e.isDeviceExecutingCurrentFunction(s.device_id)},on:{change:t=>e.toggleDeviceSelection(s,t),click:function(e){e.stopPropagation()}}}):e._e(),t("span",{staticClass:"device-name"},[e._v(e._s(s.device_name))]),t("el-tag",{attrs:{type:e.getStatusTagType(s.status),size:"mini"}},[e._v(" "+e._s(e.getStatusText(s.status))+" ")])],1),t("div",{staticClass:"device-details"},[t("div",{staticClass:"device-id"},[e._v("ID: "+e._s(s.device_id))]),s.device_info?t("div",{staticClass:"device-specs"},[s.device_info.brand&&s.device_info.model?t("span",[e._v(" "+e._s(s.device_info.brand)+" "+e._s(s.device_info.model)+" ")]):e._e(),s.device_info.androidVersion?t("span",[e._v(" | Android "+e._s(s.device_info.androidVersion)+" ")]):e._e(),s.device_info.screenWidth&&s.device_info.screenHeight?t("span",[e._v(" | "+e._s(s.device_info.screenWidth)+"×"+e._s(s.device_info.screenHeight)+" ")]):e._e()]):e._e(),s.ip_address?t("div",{staticClass:"device-ip"},[e._v(" IP: "+e._s(s.ip_address)+" ")]):e._e()]),t("div",{staticClass:"device-actions"},[t("el-button",{staticStyle:{"margin-left":"auto"},attrs:{size:"mini",type:"warning",disabled:"online"!==s.status},on:{click:function(t){return t.stopPropagation(),e.disconnectDevice(s)}}},[e._v(" 断开 ")])],1)])})],2):t("div",{staticClass:"no-device"},[t("i",{staticClass:"el-icon-warning",staticStyle:{"font-size":"24px",color:"#E6A23C","margin-bottom":"8px"}}),t("p",{staticStyle:{color:"#909399",margin:"0"}},[e._v("暂无设备连接")]),t("p",{staticStyle:{color:"#C0C4CC","font-size":"12px",margin:"4px 0 0 0"}},[e._v("请先连接手机设备")])]),t("div",{staticClass:"connection-status"},[t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("连接状态:")]),t("span",{class:e.connectionStatusClass},[e._v(e._s(e.connectionStatusText))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("连接设备:")]),t("span",{staticClass:"status-value"},[e._v(e._s(e.connectedDevices.length)+" / "+e._s(e.allDevices.length))])])])])]),t("el-dialog",{attrs:{title:"批量选择设备",visible:e.batchSelectDialogVisible,width:"600px","close-on-click-modal":!1},on:{"update:visible":function(t){e.batchSelectDialogVisible=t}}},[t("div",{staticClass:"batch-select-dialog"},[t("div",{staticClass:"dialog-header"},[t("el-alert",{staticStyle:{"margin-bottom":"15px"},attrs:{title:"请选择要添加到执行列表的设备",type:"info",closable:!1,"show-icon":""}},[t("template",{slot:"default"},[t("span",[e._v("当前可选设备 "+e._s(e.availableDevices.filter(e=>this.canSelectDevice(e)).length)+" 个，已选择 "+e._s(e.tempSelectedDevices.length)+" 个")])])],2)],1),t("div",{staticClass:"device-selection-list"},[t("el-checkbox-group",{model:{value:e.tempSelectedDevices,callback:function(t){e.tempSelectedDevices=t},expression:"tempSelectedDevices"}},e._l(e.availableDevices.filter(e=>this.canSelectDevice(e)),function(s){return t("div",{key:s.device_id,staticClass:"batch-device-item",class:{"already-selected":e.selectedDeviceIds.includes(s.device_id)}},[t("el-checkbox",{attrs:{label:s.device_id,disabled:e.selectedDeviceIds.includes(s.device_id)}},[t("div",{staticClass:"device-info-row"},[t("div",{staticClass:"device-main-info"},[t("span",{staticClass:"device-name"},[e._v(e._s(s.device_name))]),t("el-tag",{attrs:{type:"success",size:"mini"}},[e._v("在线")]),e.selectedDeviceIds.includes(s.device_id)?t("el-tag",{attrs:{type:"warning",size:"mini"}},[e._v("已选择")]):e._e()],1),t("div",{staticClass:"device-sub-info"},[t("span",{staticClass:"device-id"},[e._v("ID: "+e._s(s.device_id))]),s.ip_address?t("span",{staticClass:"device-ip"},[e._v("IP: "+e._s(s.ip_address))]):e._e()]),s.device_info?t("div",{staticClass:"device-specs"},[s.device_info.brand&&s.device_info.model?t("span",[e._v(" "+e._s(s.device_info.brand)+" "+e._s(s.device_info.model)+" ")]):e._e(),s.device_info.androidVersion?t("span",[e._v(" | Android "+e._s(s.device_info.androidVersion)+" ")]):e._e()]):e._e()])])],1)}),0)],1)]),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("div",{staticClass:"footer-info"},[t("span",[e._v("将添加 "+e._s(e.tempSelectedDevices.length)+" 个设备到执行列表")])]),t("div",{staticClass:"footer-buttons"},[t("el-button",{on:{click:e.cancelBatchSelect}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",disabled:0===e.tempSelectedDevices.length},on:{click:e.confirmBatchSelect}},[e._v(" 确认选择 ("+e._s(e.tempSelectedDevices.length)+") ")])],1)])])],1)},c=[],n={name:"DeviceInfo",props:{selectedDeviceId:{type:String,default:""},selectedDeviceIds:{type:Array,default:()=>[]},enableBatchSelect:{type:Boolean,default:!1},currentFunction:{type:String,default:""}},data(){return{allDevices:[],refreshing:!1,wsManager:null,connectionType:"disconnected",refreshTimer:null,refreshInterval:6e3,isAutoRefreshEnabled:!0,batchSelectDialogVisible:!1,tempSelectedDevices:[]}},computed:{hasDevices(){return this.availableDevices.length>0},availableDevices(){return this.allDevices.filter(e=>"online"===e.status||"busy"===e.status)},selectableDevices(){return this.allDevices.filter(e=>"online"===e.status)},connectedDevices(){return this.allDevices.filter(e=>"online"===e.status||"busy"===e.status)},onlineDevices(){return this.allDevices.filter(e=>"online"===e.status)},connectionStatusText(){switch(this.connectionType){case"websocket":return"✅ WebSocket连接";case"long-polling":return"📡 长轮询连接";default:return"❌ 未连接"}},connectionStatusClass(){return{"status-connected":"websocket"===this.connectionType,"status-polling":"long-polling"===this.connectionType,"status-disconnected":"disconnected"===this.connectionType}},allDevicesSelected(){const e=this.availableDevices.filter(e=>this.canSelectDevice(e)).length;return this.enableBatchSelect&&e>0&&this.selectedDeviceIds.length===e},connectedDevicesCount(){return this.availableDevices.length},onlineDevicesCount(){return this.availableDevices.filter(e=>"online"===e.status).length},busyDevicesCount(){return this.availableDevices.filter(e=>"busy"===e.status).length}},async mounted(){await this.initWebSocketCommunication(),await this.loadDevices(),this.startAutoRefresh(),this.$root.$on("device-selection-changed",this.handleDeviceSelectionChanged)},beforeDestroy(){this.stopAutoRefresh(),this.cleanupEventListeners(),this.$root.$off("device-selection-changed",this.handleDeviceSelectionChanged)},methods:{async initWebSocketCommunication(){try{const{getWebSocketManager:e,ensureConnection:t}=await Promise.resolve().then(s.bind(s,6006));this.wsManager=e(),await t(),this.wsManager.on("connection_established",this.handleConnectionEstablished),this.wsManager.on("device_status_changed",this.handleDeviceStatusChanged),this.wsManager.on("devices_list",this.handleDevicesListUpdate),this.wsManager.on("device_status_update",this.handleDeviceStatusUpdate),this.wsManager.on("connection_established",()=>{console.log("🔧 [DeviceInfo] 收到连接建立事件，更新连接状态"),this.updateConnectionStatus()}),this.updateConnectionStatus(),setTimeout(()=>{this.updateConnectionStatus()},1e3)}catch(e){console.error("混合通信初始化失败:",e),this.$message.error("通信初始化失败: "+e.message)}},handleConnectionEstablished(e){console.log("设备信息组件: 连接建立",e.type),this.connectionType=e.type},handleDeviceStatusChanged(e){console.log("设备信息组件: 设备状态变化",e),e.deviceId&&e.status&&this.$store.dispatch("device/updateDeviceStatus",{deviceId:e.deviceId,status:e.status,...e}),this.loadDevices()},handleDevicesListUpdate(e){console.log("设备信息组件: 设备列表更新",e);const t=new Map;e.forEach(e=>{const s=e.deviceId;if(t.has(s)){const i=t.get(s),c=e.lastSeen>i.lastSeen?e:i;t.set(s,c),console.log(`设备去重: ${s}, 保留较新的设备信息`)}else t.set(s,e)});const s=Array.from(t.values());console.log(`设备去重完成: 原始${e.length}个, 去重后${s.length}个`);const i=s.map(e=>({device_id:e.deviceId,device_name:e.deviceName,device_info:e.deviceInfo,status:e.status,last_seen:e.lastSeen,created_at:e.connectedAt,ip_address:e.ipAddress||"未知"}));this.$store.commit("device/SET_DEVICES",i),this.allDevices=i},handleDeviceOnline(e){console.log("设备信息组件: 设备上线",e),this.$store.dispatch("device/deviceOnline",e),this.loadDevices()},handleDeviceOffline(e){console.log("设备信息组件: 设备离线",e),this.$store.dispatch("device/deviceOffline",e.deviceId||e),this.loadDevices()},handleDeviceStatusUpdate(e){console.log("设备信息组件: 设备状态更新",e),this.$store.commit("device/UPDATE_DEVICE",{deviceId:e.deviceId,updates:{status:e.status,last_seen:e.lastSeen||(new Date).toISOString()}}),this.loadDevices()},handleDeviceSelectionChanged(e){console.log("🔄 [DeviceInfo] 收到设备选择状态变化事件:",e),"device_disconnected"===e.reason&&e.removedDevice&&(console.log(`🔄 [DeviceInfo] 设备 ${e.removedDevice} 因断开连接被移除，强制刷新界面`),this.loadDevices(),this.$forceUpdate(),this.$nextTick(()=>{this.$forceUpdate()}))},updateConnectionStatus(){if(this.wsManager){const e=this.wsManager.getConnectionStatus();this.connectionType=e.isConnected?"websocket":"disconnected",console.log("🔧 [DeviceInfo] 连接状态更新:",{isConnected:e.isConnected,type:this.connectionType})}else this.connectionType="disconnected",console.log("🔧 [DeviceInfo] WebSocket管理器未初始化，设置为未连接")},async loadDevices(){try{await this.$store.dispatch("device/fetchDevices"),this.allDevices=this.$store.getters["device/devices"],console.log("[DeviceInfo] 设备列表已从store加载:",this.allDevices.length)}catch(e){console.error("加载设备列表失败:",e)}},async refreshDeviceInfo(){this.refreshing=!0;try{await this.loadDevices(),this.updateConnectionStatus(),this.$message.success("设备信息已刷新")}catch(e){this.$message.error("刷新失败: "+e.message)}finally{this.refreshing=!1}},selectDevice(e){this.enableBatchSelect?this.toggleDeviceSelection(e,!this.selectedDeviceIds.includes(e.device_id)):this.$emit("device-selected",e)},toggleDeviceSelection(e,t){if("busy"===e.status){const t=this.isDeviceExecutingCurrentFunction(e.device_id);if(!t)return void this.$message.warning(`设备 ${e.device_name} 正在执行其他脚本，无法选择`);console.log(`设备 ${e.device_name} 正在执行当前功能，允许选择`)}const s=[...this.selectedDeviceIds],i=s.indexOf(e.device_id);t&&-1===i?(s.push(e.device_id),this.$emit("device-selected",e)):!t&&i>-1&&(s.splice(i,1),this.$emit("device-removed",e)),this.$emit("devices-selection-changed",s)},selectAllDevices(){const e=this.availableDevices.filter(e=>this.canSelectDevice(e)).map(e=>e.device_id);0!==e.length?(this.availableDevices.forEach(e=>{this.canSelectDevice(e)&&!this.selectedDeviceIds.includes(e.device_id)&&this.$emit("device-selected",e)}),this.$emit("devices-selection-changed",e),this.$message.success(`已选择所有 ${e.length} 个可用设备`)):this.$message.warning("没有可选择的设备（所有设备都在忙碌中）")},clearAllDevices(){this.selectedDeviceIds.forEach(e=>{const t=this.availableDevices.find(t=>t.device_id===e);t&&this.$emit("device-removed",t)}),this.$emit("devices-selection-changed",[]),this.$message.success("已清空所有选择")},showOnlineDevicesDialog(){this.tempSelectedDevices=[],this.batchSelectDialogVisible=!0},confirmBatchSelect(){if(0===this.tempSelectedDevices.length)return void this.$message.warning("请至少选择一个设备");const e=this.tempSelectedDevices.filter(e=>{const t=this.availableDevices.find(t=>t.device_id===e);return t&&this.canSelectDevice(t)});if(0===e.length)return void this.$message.warning("没有有效的设备可添加");e.forEach(e=>{const t=this.availableDevices.find(t=>t.device_id===e);t&&!this.selectedDeviceIds.includes(t.device_id)&&this.$emit("device-selected",t)});const t=[...new Set([...this.selectedDeviceIds,...e])];this.$emit("devices-selection-changed",t),this.batchSelectDialogVisible=!1;const s=this.tempSelectedDevices.length-e.length;let i=`已添加 ${e.length} 个设备到执行列表`;s>0&&(i+=`（跳过 ${s} 个无效设备）`),this.$message.success(i),this.tempSelectedDevices=[]},cancelBatchSelect(){this.batchSelectDialogVisible=!1,this.tempSelectedDevices=[]},getSelectedDeviceNames(){const e=this.availableDevices.filter(e=>this.selectedDeviceIds.includes(e.device_id));return e.map(e=>e.device_name).join(", ")},getStatusTagType(e){switch(e){case"online":return"success";case"busy":return"warning";case"offline":return"danger";default:return"info"}},getStatusText(e){switch(e){case"online":return"在线";case"busy":return"忙碌";case"offline":return"离线";default:return"未知"}},isDeviceExecutingCurrentFunction(e){if(!this.currentFunction)return!1;let t=[];return this.currentFunction.startsWith("xiaohongshu")||["profile","searchGroupChat","groupMessage","articleComment","uidMessage","uidFileMessage"].includes(this.currentFunction)?t=this.$store.getters["xiaohongshu/getDeviceTasks"](e):(this.currentFunction.startsWith("xianyu")||["keywordMessage"].includes(this.currentFunction))&&(t=this.$store.getters["xianyu/getDeviceTasks"](e)),!(!t||0===t.length)&&t.some(e=>e.functionType===this.currentFunction&&"running"===e.status)},canSelectDevice(e){return"online"===e.status||"busy"===e.status&&this.isDeviceExecutingCurrentFunction(e.device_id)},startAutoRefresh(){this.isAutoRefreshEnabled&&(console.log(`[DeviceInfo] 开始自动刷新，间隔: ${this.refreshInterval}ms`),this.refreshTimer=setInterval(()=>{this.autoRefreshDevices()},this.refreshInterval))},stopAutoRefresh(){this.refreshTimer&&(console.log("[DeviceInfo] 停止自动刷新"),clearInterval(this.refreshTimer),this.refreshTimer=null)},async autoRefreshDevices(){try{await this.$store.dispatch("device/fetchDevices"),this.allDevices=this.$store.getters["device/devices"],this.updateConnectionStatus(),console.log("[DeviceInfo] 设备信息自动刷新完成")}catch(e){console.error("[DeviceInfo] 自动刷新失败:",e)}},toggleAutoRefresh(){this.isAutoRefreshEnabled=!this.isAutoRefreshEnabled,this.isAutoRefreshEnabled?(this.startAutoRefresh(),this.$message.success("已开启自动刷新")):(this.stopAutoRefresh(),this.$message.info("已关闭自动刷新"))},async disconnectDevice(e){try{await this.$confirm(`确定要断开设备 "${e.device_name}" 的连接吗？\n\n断开后设备记录将保留为离线状态，可重新连接。`,"确认断开连接",{type:"warning",dangerouslyUseHTMLString:!1}),console.log("断开设备:",e.device_name,e.device_id),await this.$store.dispatch("device/deleteDevice",e.device_id),this.$message.success("设备连接已断开，记录已保留为离线状态"),this.selectedDeviceId===e.device_id&&this.$emit("device-selected",null),setTimeout(()=>{this.loadDevices()},1e3)}catch(t){"cancel"!==t&&this.$message.error("断开失败: "+t.message)}},cleanupEventListeners(){this.wsManager&&(this.wsManager.off("connection_established"),this.wsManager.off("device_status_changed"),this.wsManager.off("devices_list"),this.wsManager.off("device_status_update"))}}},a=n,l=s(1656),o=(0,l.A)(a,i,c,!1,null,"3455ab73",null),d=o.exports}}]);