import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import axios from 'axios'
import moment from 'moment'
// 注释掉旧的socket导入，改用WebSocketManager
// import './utils/socket'
import './utils/stateManager' // 导入状态管理工具，自动清理过期备份
import { getApiBaseUrl } from './utils/serverConfig'
import { initWebSocket } from './utils/websocketManager'

Vue.config.productionTip = false

// 配置Element UI
Vue.use(ElementUI)

// 配置axios - 使用动态服务器地址
// 确保在DOM加载后执行，以便window.location可用
const configureAxios = () => {
  const baseURL = getApiBaseUrl()
  axios.defaults.baseURL = baseURL
  axios.defaults.timeout = 2000000 // 20分钟超时，适合大文件上传

  console.log('Axios baseURL configured to:', baseURL)
  console.log('Current location:', {
    hostname: window.location.hostname,
    port: window.location.port,
    protocol: window.location.protocol
  })
}

// 立即配置axios
configureAxios()

// 请求拦截器
axios.interceptors.request.use(
  config => {
    const token = store.getters['auth/token']
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axios.interceptors.response.use(
  response => {
    return response
  },
  error => {
    if (error.response) {
      const { status, data } = error.response
      if (status === 401) {
        store.dispatch('auth/logout')
        router.push('/login')
        Vue.prototype.$message.error('登录已过期，请重新登录')
      } else {
        Vue.prototype.$message.error(data.message || '请求失败')
      }
    } else {
      Vue.prototype.$message.error('网络错误')
    }
    return Promise.reject(error)
  }
)

Vue.prototype.$http = axios

// 配置moment
moment.locale('zh-cn')
Vue.prototype.$moment = moment

// 全局过滤器
Vue.filter('formatTime', function (value) {
  if (!value) return ''
  return moment(value).format('YYYY-MM-DD HH:mm:ss')
})

Vue.filter('fromNow', function (value) {
  if (!value) return ''
  return moment(value).fromNow()
})

// 创建Vue实例
const app = new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')

// 在应用启动后初始化WebSocket连接
setTimeout(async () => {
  try {
    console.log('🔧 [Main] 应用启动完成，初始化WebSocket连接...')
    await initWebSocket()
    console.log('✅ [Main] WebSocket连接初始化完成')
  } catch (error) {
    console.error('❌ [Main] WebSocket连接初始化失败:', error)
  }
}, 1000) // 延迟1秒，确保应用完全加载

// 监听token变化，重新连接WebSocket以更新认证状态
store.watch(
  (state) => state.auth.token,
  async (token) => {
    console.log('🔧 [Main] Token变化，重新初始化WebSocket连接:', !!token)
    try {
      // 导入WebSocket管理器
      const { getWebSocketManager } = await import('./utils/websocketManager')
      const wsManager = getWebSocketManager()

      // 先断开现有连接
      wsManager.disconnect()

      // 延迟重新连接
      setTimeout(async () => {
        try {
          await wsManager.init()
          console.log('✅ [Main] WebSocket重连成功')
        } catch (error) {
          console.error('❌ [Main] WebSocket重连失败:', error)
        }
      }, 500)
    } catch (error) {
      console.error('❌ [Main] WebSocket重连过程出错:', error)
    }
  }
)
