<template>
  <div class="profile-config">
    <el-form :model="config" label-width="120px">
      <el-form-item label="新昵称">
        <el-input
          v-model="config.nickname"
          placeholder="请输入新的昵称（2-24字符）"
          maxlength="24"
          show-word-limit
          @input="onInputChange"
          @blur="onInputChange"
        />
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          昵称长度限制：2-24个字符
        </div>
      </el-form-item>

      <el-form-item label="新简介">
        <el-input
          v-model="config.profile"
          type="textarea"
          :rows="4"
          placeholder="请输入新的个人简介"
          maxlength="200"
          show-word-limit
          @input="onInputChange"
          @blur="onInputChange"
        />
      </el-form-item>

      <el-form-item label="修改选项">
        <el-checkbox-group v-model="config.modifyOptions" @change="onModifyOptionsChange">
          <el-checkbox
            label="onlyNickname"
            :disabled="isOnlyProfileSelected">
            只修改昵称
          </el-checkbox>
          <el-checkbox
            label="onlyProfile"
            :disabled="isOnlyNicknameSelected">
            只修改简介
          </el-checkbox>
          <el-checkbox label="autoSave">自动保存设置</el-checkbox>
        </el-checkbox-group>
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          • 只修改昵称：仅更新昵称，不修改简介（与"只修改简介"互斥）<br>
          • 只修改简介：仅更新简介，不修改昵称（与"只修改昵称"互斥）<br>
          • 自动保存设置：保存配置以便下次使用
        </div>
      </el-form-item>

      <el-form-item label="小红书应用">
        <el-select
          v-model="config.selectedApp"
          placeholder="请选择要使用的小红书应用"
          @change="onAppSelectionChange"
          style="width: 100%"
        >
          <el-option
            v-for="app in xiaohongshuApps"
            :key="app.text"
            :label="app.text"
            :value="app.text"
          >
            <span style="float: left">{{ app.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ app.method === 'keyword' ? '关键词' : '正则' }}
            </span>
          </el-option>
        </el-select>
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          选择设备上要使用的小红书应用版本
        </div>
      </el-form-item>

      <el-form-item label="操作间隔">
        <el-input-number
          v-model="config.operationDelay"
          :min="1"
          :max="10"
          placeholder="秒"
          @change="onInputChange"
        />
        <span style="margin-left: 10px; color: #909399;">每个操作步骤间隔时间（秒）</span>
      </el-form-item>

      <el-form-item label="安全设置">
        <el-checkbox-group v-model="config.safetyOptions">
          <el-checkbox label="backupOriginal">备份原有信息</el-checkbox>
          <el-checkbox label="confirmBeforeChange">修改完成后确认保存</el-checkbox>
          <el-checkbox label="validateInput">验证输入格式</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-alert
        title="使用提醒"
        type="info"
        :closable="false"
        show-icon
      >
        <div>
          • 请确保小红书应用已安装并可正常使用<br>
          • 建议在修改前备份原有昵称和简介信息<br>
          • 昵称修改可能有频率限制，请适度使用<br>
          • 修改过程中请勿手动操作手机
        </div>
      </el-alert>

      <!-- 实时状态显示区域 -->
      <div class="realtime-status-section" style="margin-bottom: 20px;">
        <el-divider content-position="left">
          <span style="color: #409EFF; font-weight: bold;">实时状态</span>
        </el-divider>

        <div class="status-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
          <div class="status-item">
            <span class="status-label">操作次数：</span>
            <span class="status-value" style="color: #67C23A; font-weight: bold;">{{ operationCount }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">已处理步骤：</span>
            <span class="status-value" style="color: #E6A23C; font-weight: bold;">{{ processedStepCount }}</span>
          </div>
        </div>

        <div class="current-status" style="margin-bottom: 15px;">
          <span class="status-label">当前状态：</span>
          <span class="status-value" style="color: #409EFF; font-weight: bold;">{{ currentStatus || '等待开始' }}</span>
        </div>
      </div>

      <!-- 脚本控制按钮 -->
      <el-form-item label="脚本控制">
        <el-button
          type="primary"
          size="small"
          @click="startScript"
          :disabled="isScriptRunning || !canExecute"
          :loading="isScriptRunning"
        >
          {{ isScriptRunning ? '脚本执行中...' : '开始执行' }}
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="stopScript"
          :disabled="!isScriptRunning"
          style="margin-left: 10px;"
        >
          停止脚本
        </el-button>
        <span v-if="isScriptRunning" style="margin-left: 10px; color: #67C23A; font-size: 12px;">
          脚本正在执行中...
        </span>
        <span v-else-if="isScriptCompleted" style="margin-left: 10px; color: #409EFF; font-size: 12px;">
          脚本执行完成，1分钟后可重新执行
        </span>
        <span v-else style="margin-left: 10px; color: #909399; font-size: 12px;">
          脚本未运行
        </span>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import io from 'socket.io-client'
import xiaohongshuAppSelector from '@/mixins/xiaohongshuAppSelector'

export default {
  name: 'ProfileConfig',
  mixins: [xiaohongshuAppSelector],
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      config: {
        nickname: '',
        profile: '',
        modifyOptions: [],
        operationDelay: 2,
        safetyOptions: [],
        selectedApp: '' // 选择的小红书应用
      },
      currentLogId: null, // 保存当前执行的日志ID
      currentTaskId: null, // 保存当前执行的任务ID

      // 实时状态变量
      operationCount: 0,
      processedStepCount: 0,
      currentStatus: '等待开始',

      // Socket连接
      socket: null,

      // 事件监听器清理标记
      eventListenersSetup: false
    }
  },
  computed: {
    // 从Vuex获取脚本运行状态
    isScriptRunning() {
      const functionState = this.$store.getters['xiaohongshu/getFunctionState']('profile')
      return functionState ? functionState.isScriptRunning : false
    },

    // 从Vuex获取脚本完成状态
    isScriptCompleted() {
      const functionState = this.$store.getters['xiaohongshu/getFunctionState']('profile')
      return functionState ? functionState.isScriptCompleted : false
    },

    // 检查是否可以执行
    canExecute() {
      return this.config.nickname && this.config.nickname.trim().length >= 2
    },

    // 检查是否选择了"只修改昵称"
    isOnlyNicknameSelected() {
      return this.config.modifyOptions.includes('onlyNickname')
    },

    // 检查是否选择了"只修改简介"
    isOnlyProfileSelected() {
      return this.config.modifyOptions.includes('onlyProfile')
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && typeof newVal === 'object') {
          this.config = { ...this.config, ...newVal }
        }
      },
      immediate: true
    },
    config: {
      handler(newVal, oldVal) {
        // 避免初始化时的无限循环
        if (oldVal && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          console.log('配置发生变化，准备更新:', newVal)
          this.$nextTick(() => {
            this.updateConfig()
          })
        }
      },
      deep: true
    }
  },

  methods: {
    updateConfig() {
      console.log('ProfileConfig updateConfig 被调用')
      console.log('当前 config:', this.config)

      // 验证配置
      const errors = this.validateConfig()
      if (errors.length > 0) {
        console.log('配置验证失败:', errors)
        this.$emit('validation-error', errors)
      } else {
        console.log('配置验证成功')
        this.$emit('validation-success')
      }

      console.log('发送 input 事件:', this.config)
      console.log('发送 update 事件:', this.config)

      this.$emit('input', this.config)
      this.$emit('update', this.config)
    },

    // 手动触发配置更新（当用户输入时调用）
    onInputChange() {
      console.log('用户输入发生变化，触发配置更新')
      this.updateConfig()
    },

    // 处理修改选项变化（确保互斥逻辑）
    onModifyOptionsChange(newOptions) {
      console.log('修改选项发生变化:', newOptions)

      // 检查是否同时选择了互斥选项
      const hasOnlyNickname = newOptions.includes('onlyNickname')
      const hasOnlyProfile = newOptions.includes('onlyProfile')

      if (hasOnlyNickname && hasOnlyProfile) {
        // 如果同时选择了两个互斥选项，移除之前选择的那个
        // 通过比较当前选项和之前的选项来确定哪个是新选择的
        const previousOptions = this.config.modifyOptions

        if (!previousOptions.includes('onlyNickname')) {
          // 新选择的是"只修改昵称"，移除"只修改简介"
          this.config.modifyOptions = newOptions.filter(option => option !== 'onlyProfile')
          console.log('移除"只修改简介"选项，保留"只修改昵称"')
        } else if (!previousOptions.includes('onlyProfile')) {
          // 新选择的是"只修改简介"，移除"只修改昵称"
          this.config.modifyOptions = newOptions.filter(option => option !== 'onlyNickname')
          console.log('移除"只修改昵称"选项，保留"只修改简介"')
        }
      }

      // 触发配置更新
      this.onInputChange()
    },

    validateConfig() {
      const errors = []

      // 验证昵称
      if (!this.config.modifyOptions.includes('onlyProfile')) {
        if (!this.config.nickname || this.config.nickname.trim().length < 2) {
          errors.push('昵称长度不能少于2个字符')
        }
        if (this.config.nickname && this.config.nickname.length > 24) {
          errors.push('昵称长度不能超过24个字符')
        }
      }

      // 验证简介
      if (!this.config.modifyOptions.includes('onlyNickname')) {
        if (!this.config.profile || this.config.profile.trim().length === 0) {
          errors.push('请输入个人简介内容')
        }
      }

      // 验证修改选项冲突
      if (this.config.modifyOptions.includes('onlyNickname') &&
          this.config.modifyOptions.includes('onlyProfile')) {
        errors.push('不能同时选择"只修改昵称"和"只修改简介"')
      }

      return errors
    },

    // 开始执行脚本
    async startScript() {
      if (!this.canExecute) {
        this.$message.warning('请输入有效的昵称（至少2个字符）')
        return
      }

      try {
        console.log('[ProfileConfig] 开始执行修改资料脚本')

        // 通过父组件的方法执行脚本
        this.$emit('execute-script', {
          functionType: 'profile',
          config: this.config,
          deviceId: this.deviceId
        })

        console.log('[ProfileConfig] 脚本执行请求已发送')
        // 注意：不在这里更新状态，等待任务开始事件来更新状态
      } catch (error) {
        console.error('[ProfileConfig] 启动脚本失败:', error)
        this.$message.error('启动脚本失败: ' + error.message)
      }
    },

    // 停止脚本执行
    async stopScript() {
      try {
        console.log('[ProfileConfig] 停止修改资料脚本执行')

        // 如果有设备ID，停止特定设备；否则停止所有任务
        let stopUrl = '/api/xiaohongshu/stop'
        let stopData = {}

        if (this.deviceId) {
          // 单设备停止 - 使用保存的logId和taskId
          console.log('[ProfileConfig] 停止特定设备:', this.deviceId)
          console.log('[ProfileConfig] 使用保存的logId:', this.currentLogId)
          console.log('[ProfileConfig] 使用保存的taskId:', this.currentTaskId)
          stopData = {
            deviceId: this.deviceId,
            taskId: this.currentTaskId || `xiaohongshu_profile_${this.deviceId}`, // 优先使用保存的taskId
            logId: this.currentLogId // 使用保存的logId
          }
        } else {
          // 停止所有任务（兼容旧版本）
          console.log('[ProfileConfig] 停止所有修改资料任务')
        }

        // 发送停止请求到服务器
        const response = await this.$http.post(stopUrl, stopData)

        if (response.data.success) {
          this.$message.success('脚本停止成功')

          // 清空保存的ID
          this.currentLogId = null
          this.currentTaskId = null
          console.log('[ProfileConfig] 已清空保存的logId和taskId')

          // 更新Vuex状态
          await this.$store.dispatch('xiaohongshu/stopTask', {
            functionType: 'profile',
            reason: 'manual'
          })

          // 保存组件状态
          this.saveComponentState()

          // 发送停止事件
          this.$emit('task-stopped', {
            functionType: 'profile',
            reason: 'manual',
            deviceId: this.deviceId
          })

          // 发送全局停止事件
          this.$root.$emit('xiaohongshu-task-stopped', {
            functionType: 'profile',
            reason: 'manual',
            deviceId: this.deviceId
          })

          console.log('[ProfileConfig] 脚本停止完成，状态已重置')
        } else {
          this.$message.error('停止脚本失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('[ProfileConfig] 停止脚本请求失败:', error)
        this.$message.error('停止脚本请求失败')

        // 即使请求失败，也通过Vuex重置状态
        this.$store.dispatch('xiaohongshu/setFunctionState', {
          functionType: 'profile',
          stateData: {
            isScriptRunning: false,
            isScriptCompleted: false,
            config: this.config
          }
        })
      }
    },

    // 处理任务开始事件
    handleTaskStarted(data) {
      console.log('[ProfileConfig] 收到任务开始事件:', data)
      if (data.functionType === 'profile' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[ProfileConfig] 修改资料任务开始，更新状态')

        // 通过Vuex更新状态
        this.$store.dispatch('xiaohongshu/setFunctionState', {
          functionType: 'profile',
          stateData: {
            isScriptRunning: true,
            isScriptCompleted: false,
            config: this.config
          }
        })
      }
    },

    // 处理任务停止事件
    handleTaskStopped(data) {
      console.log('[ProfileConfig] 收到任务停止事件:', data)
      if (data.functionType === 'profile' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[ProfileConfig] 修改资料任务停止，更新状态')

        // 通过Vuex更新状态
        this.$store.dispatch('xiaohongshu/setFunctionState', {
          functionType: 'profile',
          stateData: {
            isScriptRunning: false,
            isScriptCompleted: false,
            config: this.config
          }
        })
      }
    },

    // 处理任务恢复事件
    async handleTaskRestored(data) {
      if (data.functionType === 'profile') {
        console.log('[ProfileConfig] 恢复任务状态:', data.state)

        // 恢复配置
        if (data.state.config && Object.keys(data.state.config).length > 0) {
          this.config = { ...this.config, ...data.state.config }
        }

        // 通过Vuex恢复状态
        await this.$store.dispatch('xiaohongshu/setFunctionState', {
          functionType: 'profile',
          stateData: {
            isScriptRunning: data.state.isScriptRunning,
            isScriptCompleted: data.state.isScriptCompleted,
            config: this.config
          }
        })

        console.log('[ProfileConfig] 状态已恢复:', {
          isScriptRunning: this.isScriptRunning,
          isScriptCompleted: this.isScriptCompleted,
          config: this.config
        })
      }
    },

    // 保存组件状态
    async saveComponentState() {
      try {
        await this.$store.dispatch('xiaohongshu/setFunctionState', {
          functionType: 'profile',
          stateData: {
            config: this.config,
            // 保存实时状态数据
            realtimeData: {
              operationCount: this.operationCount,
              processedStepCount: this.processedStepCount,
              currentStatus: this.currentStatus
            },
            // 保存任务信息
            taskInfo: {
              currentTaskId: this.currentTaskId,
              isScriptRunning: this.isScriptRunning,
              isScriptCompleted: this.isScriptCompleted
            }
          }
        })
        console.log('[ProfileConfig] 组件状态已保存')
      } catch (error) {
        console.error('[ProfileConfig] 保存组件状态失败:', error)
      }
    },

    // 恢复组件状态
    async restoreComponentState() {
      try {
        const functionState = this.$store.getters['xiaohongshu/getFunctionState']('profile')

        if (functionState && Object.keys(functionState).length > 0) {
          console.log('[ProfileConfig] 恢复组件状态:', functionState)

          // 恢复配置
          if (functionState.config && Object.keys(functionState.config).length > 0) {
            this.config = { ...this.config, ...functionState.config }
          }

          // 恢复实时状态数据
          if (functionState.realtimeData) {
            this.operationCount = functionState.realtimeData.operationCount || 0
            this.processedStepCount = functionState.realtimeData.processedStepCount || 0
            this.currentStatus = functionState.realtimeData.currentStatus || '等待开始'
            console.log('[ProfileConfig] 实时状态已恢复:', functionState.realtimeData)
          }

          // 恢复任务信息
          if (functionState.taskInfo) {
            this.currentTaskId = functionState.taskInfo.currentTaskId || null
            this.isScriptRunning = functionState.taskInfo.isScriptRunning || false
            this.isScriptCompleted = functionState.taskInfo.isScriptCompleted || false
            console.log('[ProfileConfig] 任务信息已恢复:', functionState.taskInfo)
          }

          console.log('[ProfileConfig] 组件状态已恢复:', {
            isScriptRunning: this.isScriptRunning,
            isScriptCompleted: this.isScriptCompleted,
            config: this.config,
            realtimeData: {
              operationCount: this.operationCount,
              processedStepCount: this.processedStepCount,
              currentStatus: this.currentStatus
            },
            currentTaskId: this.currentTaskId
          })
        }
      } catch (error) {
        console.error('[ProfileConfig] 恢复组件状态失败:', error)
      }
    },

    // 处理实时状态更新
    handleRealtimeStatus(data) {
      console.log('[ProfileConfig] 收到实时状态数据:', data)
      console.log('[ProfileConfig] 当前组件taskId:', this.currentTaskId)
      console.log('[ProfileConfig] 数据中的taskId:', data.taskId)
      console.log('[ProfileConfig] taskId匹配:', this.currentTaskId && data.taskId === this.currentTaskId)

      if (this.currentTaskId && data.taskId === this.currentTaskId) {
        console.log('[ProfileConfig] ✅ taskId匹配，更新实时状态:', data)

        // 更新统计数据
        if (data.operationCount !== undefined) {
          this.operationCount = data.operationCount
        }
        if (data.processedStepCount !== undefined) {
          this.processedStepCount = data.processedStepCount
        }
        if (data.currentStatus) {
          this.currentStatus = data.currentStatus
        }

        console.log('[ProfileConfig] 实时状态已更新:', {
          operationCount: this.operationCount,
          processedStepCount: this.processedStepCount,
          currentStatus: this.currentStatus
        })
      } else {
        console.log('[ProfileConfig] ❌ taskId不匹配或currentTaskId为空，忽略实时状态更新')
      }
    },

    // 重置实时状态
    resetRealtimeStatus() {
      this.operationCount = 0
      this.processedStepCount = 0
      this.currentStatus = '等待开始'
      console.log('[ProfileConfig] 实时状态已重置')
    },

    // 🔥 新增：处理脚本重置事件
    handleScriptReset(data) {
      console.log('🔄 [ProfileConfig] 收到脚本重置事件:', data)

      // 检查是否是当前设备和功能
      if (data.functionType === 'profile' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('✅ [ProfileConfig] 重置脚本状态')

        // 重置脚本执行状态
        this.isScriptRunning = false
        this.isScriptCompleted = false

        // 重置实时状态
        this.resetRealtimeStatus()

        // 清除任务ID
        this.currentTaskId = null
        this.currentLogId = null

        // 保存组件状态
        this.saveComponentState()

        console.log('✅ [ProfileConfig] 脚本状态重置完成')
      } else {
        console.log('⏭️ [ProfileConfig] 脚本重置事件不匹配当前组件，忽略')
      }
    },

    // 初始化Socket连接 - 使用统一的WebSocket管理器
    async initializeSocket() {
      try {
        console.log('🔧 [ProfileConfig] 使用统一WebSocket管理器')
        // 使用全局WebSocket管理器，不创建独立连接
        const { getWebSocketManager } = await import('@/utils/websocketManager')
        this.wsManager = getWebSocketManager()

        // 确保连接
        await this.wsManager.init()

        // 获取socket实例用于事件监听
        this.socket = this.wsManager.socket

        console.log('✅ [ProfileConfig] 已连接到统一WebSocket管理器')
      } catch (error) {
        console.error('❌ [ProfileConfig] WebSocket连接失败:', error)
      }

      this.socket.on('disconnect', () => {
        console.log('❌ [ProfileConfig] Socket连接断开')
      })

      // 监听实时状态更新
      this.socket.on('xiaohongshu_realtime_status', (data) => {
        console.log('🎯 [ProfileConfig] 收到WebSocket实时状态事件:', data)
        this.handleRealtimeStatus(data)
      })

      // 监听测试广播事件
      this.socket.on('test_realtime_broadcast', (data) => {
        console.log('🧪 [ProfileConfig] 收到测试广播:', data)
      })

      console.log('[ProfileConfig] Socket初始化完成')
    },

    // 设置WebSocket监听
    setupWebSocketListeners() {
      const socket = this.$store.getters['socket/socket']
      console.log('[ProfileConfig] 设置WebSocket监听，Socket状态:', socket ? '可用' : '不可用')
      console.log('[ProfileConfig] Socket连接状态:', this.$store.getters['socket/connected'])

      if (socket && this.$store.getters['socket/connected']) {
        // 移除之前的监听器，避免重复监听
        socket.off('xiaohongshu_execution_completed')
        socket.off('xiaohongshu_realtime_status')
        socket.off('test_realtime_broadcast')

        socket.on('xiaohongshu_execution_completed', (data) => {
          console.log('[ProfileConfig] 收到WebSocket脚本执行完成事件:', data)
          if (data.deviceId === this.deviceId || !this.deviceId) {
            console.log('[ProfileConfig] 脚本执行完成，更新状态')

            // 通过Vuex更新状态
            this.$store.dispatch('xiaohongshu/setFunctionState', {
              functionType: 'profile',
              stateData: {
                isScriptRunning: false,
                isScriptCompleted: data.status === 'success',
                config: this.config
              }
            })

            // 如果执行成功，1分钟后重置完成状态
            if (data.status === 'success') {
              setTimeout(() => {
                this.$store.dispatch('xiaohongshu/setFunctionState', {
                  functionType: 'profile',
                  stateData: {
                    isScriptRunning: false,
                    isScriptCompleted: false,
                    config: this.config
                  }
                })
              }, 60000)
            }
          }
        })

        // 监听实时状态更新
        socket.on('xiaohongshu_realtime_status', (data) => {
          console.log('🎯 [ProfileConfig] 直接收到WebSocket实时状态事件:', data)
          this.handleRealtimeStatus(data)
        })

        // 监听测试广播事件
        socket.on('test_realtime_broadcast', (data) => {
          console.log('🧪 [ProfileConfig] 收到测试广播:', data)
        })

        console.log('✅ [ProfileConfig] WebSocket事件监听已设置')
      } else {
        console.warn('⚠️ [ProfileConfig] Socket未连接或不可用，无法设置事件监听')
      }
    },

    // 设置事件监听器（避免重复设置）
    setupEventListeners() {
      console.log('[ProfileConfig] 设置事件监听器')

      // 监听任务恢复事件
      this.$root.$on('xiaohongshu-task-restored', this.handleTaskRestored)

      // 监听脚本重置事件
      this.$root.$on('xiaohongshu-script-reset', this.handleScriptReset)

      console.log('[ProfileConfig] 事件监听器设置完成')
    },

    // 处理设备离线事件
    handleDeviceOffline() {
      console.log('[ProfileConfig] 处理设备离线，重置状态')

      // 重置脚本执行状态
      this.$store.dispatch('xiaohongshu/setFunctionState', {
        functionType: 'profile',
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          config: this.config
        }
      })

      // 重置实时状态
      this.resetRealtimeStatus()

      // 清除任务ID
      this.currentTaskId = null
      this.currentLogId = null

      // 保存状态
      this.saveComponentState()

      console.log('[ProfileConfig] 设备离线处理完成')
    },

    // 处理服务器关闭事件
    handleServerShutdown() {
      console.log('[ProfileConfig] 处理服务器关闭，重置状态')

      // 重置脚本执行状态
      this.$store.dispatch('xiaohongshu/setFunctionState', {
        functionType: 'profile',
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          config: this.config
        }
      })

      // 重置实时状态
      this.resetRealtimeStatus()

      // 清除任务ID
      this.currentTaskId = null
      this.currentLogId = null

      // 保存状态
      this.saveComponentState()

      console.log('[ProfileConfig] 服务器关闭处理完成')
    }
  },

  // 监听任务状态变化
  mounted() {
    console.log('[ProfileConfig] 组件已挂载，设备ID:', this.deviceId)
    console.log('[ProfileConfig] 初始 config:', this.config)

    // 避免重复设置事件监听器
    if (!this.eventListenersSetup) {
      this.setupEventListeners()
      this.eventListenersSetup = true
    }

    // 监听任务开始事件
    this.$root.$on('xiaohongshu-task-started', (data) => {
      console.log('[ProfileConfig] 收到任务开始事件:', data)
      console.log('[ProfileConfig] 当前组件deviceId:', this.deviceId)
      console.log('[ProfileConfig] 事件deviceId:', data.deviceId)
      console.log('[ProfileConfig] 功能类型匹配:', data.functionType === 'profile')
      console.log('[ProfileConfig] 设备ID匹配:', !this.deviceId || data.deviceId === this.deviceId)

      if (data.functionType === 'profile' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[ProfileConfig] 修改资料任务开始，更新状态 (设备ID匹配)')

        // 通过Vuex更新状态
        this.$store.dispatch('xiaohongshu/setFunctionState', {
          functionType: 'profile',
          stateData: {
            isScriptRunning: true,
            isScriptCompleted: false,
            config: this.config
          }
        })

        // 保存当前执行的logId和taskId
        if (data.logId) {
          this.currentLogId = data.logId
          console.log('[ProfileConfig] 保存logId:', this.currentLogId)
        }
        if (data.taskId) {
          this.currentTaskId = data.taskId
          console.log('[ProfileConfig] 保存taskId:', this.currentTaskId)
        }

        // 重置实时状态
        this.resetRealtimeStatus()

        console.log('[ProfileConfig] 状态已更新:', {
          isScriptRunning: this.isScriptRunning,
          isScriptCompleted: this.isScriptCompleted,
          currentLogId: this.currentLogId,
          currentTaskId: this.currentTaskId
        })
      } else {
        console.log('[ProfileConfig] 任务开始事件不匹配，忽略')
      }
    })

    // 监听任务停止事件
    this.$root.$on('xiaohongshu-task-stopped', (data) => {
      console.log('[ProfileConfig] 收到任务停止事件:', data)
      const functionType = typeof data === 'string' ? data : data.functionType
      const reason = data.reason || 'manual'

      // 处理批量停止或单设备停止
      const shouldStop = functionType === 'profile' && (
        reason === 'batch_stop' || // 批量停止时停止所有设备
        !this.deviceId || // 没有设备ID时停止
        data.deviceId === this.deviceId // 设备ID匹配时停止
      )

      if (shouldStop) {
        console.log(`[ProfileConfig] 修改资料任务停止，原因: ${reason}`)

        // 通过Vuex重置状态
        this.$store.dispatch('xiaohongshu/setFunctionState', {
          functionType: 'profile',
          stateData: {
            isScriptRunning: false,
            isScriptCompleted: false,
            config: this.config
          }
        })

        // 清空保存的ID
        this.currentLogId = null
        this.currentTaskId = null

        // 重置实时状态
        this.resetRealtimeStatus()

        console.log('[ProfileConfig] 已清空保存的logId和taskId')
        console.log('[ProfileConfig] 组件状态已通过Vuex更新')

        // 如果是批量停止，显示提示信息
        if (reason === 'batch_stop') {
          this.$message.info('修改资料功能已被批量停止')
        }
      }
    })

    // 监听脚本状态更新事件
    this.$root.$on('xiaohongshu-script-completed', (data) => {
      console.log('[ProfileConfig] 收到脚本完成事件:', data)
      const functionType = typeof data === 'string' ? data : data.functionType
      if (functionType === 'profile' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[ProfileConfig] 修改资料脚本完成 (设备ID匹配)')

        // 通过Vuex更新状态
        this.$store.dispatch('xiaohongshu/setFunctionState', {
          functionType: 'profile',
          stateData: {
            isScriptRunning: false,
            isScriptCompleted: true,
            config: this.config
          }
        })

        // 1分钟后重置完成状态
        setTimeout(() => {
          this.$store.dispatch('xiaohongshu/setFunctionState', {
            functionType: 'profile',
            stateData: {
              isScriptRunning: false,
              isScriptCompleted: false,
              config: this.config
            }
          })
        }, 60000)
      }
    })

    // 监听设备离线事件
    this.$root.$on('device-offline', (data) => {
      console.log('[ProfileConfig] 收到设备离线事件:', data)
      if (data.deviceId === this.deviceId) {
        console.log('[ProfileConfig] 当前设备离线，重置状态')
        this.handleDeviceOffline()
      }
    })

    // 监听服务器关闭事件
    this.$root.$on('server-shutdown', (data) => {
      console.log('[ProfileConfig] 收到服务器关闭事件:', data)
      this.handleServerShutdown()
    })

    // 监听服务器断开事件
    this.$root.$on('server-disconnect', (data) => {
      console.log('[ProfileConfig] 收到服务器断开事件:', data)
      this.handleServerShutdown() // 复用服务器关闭处理逻辑
    })

    // 监听强制重置事件
    this.$root.$on('force-reset-all', (data) => {
      console.log('[ProfileConfig] 收到强制重置事件:', data)
      this.handleServerShutdown() // 复用服务器关闭处理逻辑
    })

    // 监听WebSocket事件
    if (this.$socket) {
      this.$socket.on('xiaohongshu_execution_completed', (data) => {
        console.log('[ProfileConfig] 收到WebSocket脚本执行完成事件:', data)
        if (data.deviceId === this.deviceId || !this.deviceId) {
          console.log('[ProfileConfig] 脚本执行完成，更新状态')

          // 通过Vuex更新状态
          this.$store.dispatch('xiaohongshu/setFunctionState', {
            functionType: 'profile',
            stateData: {
              isScriptRunning: false,
              isScriptCompleted: data.status === 'success',
              config: this.config
            }
          })

          // 如果执行成功，1分钟后重置完成状态
          if (data.status === 'success') {
            setTimeout(() => {
              this.$store.dispatch('xiaohongshu/setFunctionState', {
                functionType: 'profile',
                stateData: {
                  isScriptRunning: false,
                  isScriptCompleted: false,
                  config: this.config
                }
              })
            }, 60000)
          }
        }
      })
    }

    // 恢复组件状态（异步）
    this.restoreComponentState().then(() => {
      console.log('[ProfileConfig] 状态恢复完成，开始初始化Socket连接')
      // 初始化Socket连接（参考循环群发的实现）
      this.initializeSocket()
    }).catch(error => {
      console.error('[ProfileConfig] 状态恢复失败:', error)
      // 即使恢复失败也要初始化Socket连接
      this.initializeSocket()
    })
  },

  beforeDestroy() {
    console.log('[ProfileConfig] 组件即将销毁，清理资源')

    // 保存组件状态
    this.saveComponentState()

    // 断开socket连接
    if (this.socket) {
      this.socket.disconnect()
      console.log('[ProfileConfig] Socket连接已断开')
    }

    // 清理所有事件监听器
    this.$root.$off('xiaohongshu-task-started')
    this.$root.$off('xiaohongshu-task-stopped')
    this.$root.$off('xiaohongshu-script-completed')
    this.$root.$off('xiaohongshu-script-reset', this.handleScriptReset)
    this.$root.$off('xiaohongshu-task-restored', this.handleTaskRestored)
    this.$root.$off('device-offline')
    this.$root.$off('server-shutdown')
    this.$root.$off('server-disconnect')
    this.$root.$off('force-reset-all')

    // 重置事件监听器标记
    this.eventListenersSetup = false

    console.log('[ProfileConfig] 资源清理完成')
  }
}
</script>

<style scoped>
.profile-config {
  padding: 10px 0;
}
</style>
