import io from 'socket.io-client'
import store from '@/store'
import { getWebSocketUrl } from './serverConfig'

let socket = null

export function initSocket() {
  // 修改条件：允许在未登录状态下也建立Socket连接来接收设备事件
  if (!socket) {
    const isAuthenticated = store.getters['auth/isAuthenticated']
    console.log('初始化WebSocket连接...', { isAuthenticated })

    // 使用工具函数获取服务器地址
    const serverUrl = getWebSocketUrl()
    console.log('🔧 [Socket] 连接到WebSocket服务器:', serverUrl)
    console.log('🔧 [Socket] 当前页面信息:', {
      href: window.location.href,
      hostname: window.location.hostname,
      port: window.location.port,
      protocol: window.location.protocol
    })

    // 在开发环境中，如果serverUrl为空（使用代理），需要特殊配置
    const socketOptions = {
      timeout: 20000,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: 5,
      maxReconnectionAttempts: 5
    }

    // 只使用WebSocket传输，不使用长轮询
    if (serverUrl === '' || serverUrl === undefined) {
      console.log('🔧 [Socket] 开发环境代理模式，只使用WebSocket传输')
      socketOptions.transports = ['websocket'] // 移除polling，只使用WebSocket
      socketOptions.upgrade = false // 禁用传输升级
      socketOptions.forceNew = true
      // 确保使用相对路径
      socket = io(socketOptions)
    } else {
      console.log('🔧 [Socket] 生产环境或直接连接模式，连接到:', serverUrl)
      socketOptions.transports = ['websocket'] // 移除polling，只使用WebSocket
      socket = io(serverUrl, socketOptions)
    }

    socket.on('connect', () => {
      console.log('WebSocket连接成功')
    })

    socket.on('disconnect', (reason) => {
      console.log('WebSocket连接断开:', reason)
    })

    socket.on('connect_error', (error) => {
      console.error('WebSocket连接错误:', error)
    })

    // 延迟一点再连接到store，确保连接稳定
    setTimeout(() => {
      if (socket && socket.connected) {
        store.dispatch('socket/connect', socket)
      }
    }, 1000)
  }
  return socket
}

export function disconnectSocket() {
  if (socket) {
    console.log('断开WebSocket连接')
    socket.disconnect()
    store.dispatch('socket/disconnect')
    socket = null
  }
}

// 注释掉自动初始化，改用WebSocketManager统一管理
// setTimeout(() => {
//   initSocket()
// }, 1000) // 延迟1秒初始化，确保应用完全加载

// 注释掉token监听，改用WebSocketManager统一管理
// store.watch(
//   (state) => state.auth.token,
//   (token) => {
//     console.log('Token变化，重新初始化Socket连接:', !!token)
//     // 先断开现有连接
//     disconnectSocket()
//     // 延迟重新连接
//     setTimeout(() => {
//       initSocket()
//     }, 500)
//   }
// )

export default socket
