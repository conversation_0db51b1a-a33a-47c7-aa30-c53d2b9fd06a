const state = {
  connected: false,
  socket: null
}

const getters = {
  connected: state => state.connected,
  socket: state => state.socket
}

const mutations = {
  SET_CONNECTED(state, connected) {
    state.connected = connected
  },
  SET_SOCKET(state, socket) {
    state.socket = socket
  }
}

const actions = {
  connect({ commit, rootGetters, rootState }, socket) {
    commit('SET_SOCKET', socket)

    socket.on('connect', () => {
      console.log('WebSocket连接成功')
      commit('SET_CONNECTED', true)

      // 注册为Web客户端
      const user = rootGetters['auth/user']
      const isAuthenticated = rootGetters['auth/isAuthenticated']

      console.log('🔍 [WebSocket] 用户认证状态检查:')
      console.log('🔍 [WebSocket] isAuthenticated:', isAuthenticated)
      console.log('🔍 [WebSocket] user:', user)

      if (isAuthenticated && user) {
        console.log('🔍 [WebSocket] 发送认证用户连接:', { userId: user.id, username: user.username })
        socket.emit('web_client_connect', {
          userId: user.id,
          username: user.username
        })
        console.log('已注册为认证Web客户端:', user.username)
      } else {
        // 未登录状态下也注册为匿名Web客户端，以便接收设备事件
        const anonymousUserId = 'anonymous_' + Date.now()
        console.log('🔍 [WebSocket] 发送匿名用户连接:', { userId: anonymousUserId, username: 'anonymous', clientType: 'anonymous_web' })
        socket.emit('web_client_connect', {
          userId: anonymousUserId,
          username: 'anonymous',
          clientType: 'anonymous_web'
        })
        console.log('已注册为匿名Web客户端')
      }
    })

    socket.on('disconnect', () => {
      console.log('WebSocket连接断开')
      commit('SET_CONNECTED', false)
    })

    socket.on('devices_list', (devices) => {
      // 更新设备列表
      commit('device/SET_DEVICES', devices, { root: true })
    })

    socket.on('device_online', (device) => {
      // 设备上线
      commit('device/ADD_DEVICE', device, { root: true })
    })

    socket.on('device_offline', ({ deviceId }) => {
      // 设备离线
      commit('device/UPDATE_DEVICE', {
        deviceId,
        updates: { status: 'offline' }
      }, { root: true })
    })

    socket.on('script_result', (result) => {
      // 脚本执行结果
      console.log('脚本执行结果:', result)
      // 可以在这里触发通知或更新UI
    })

    socket.on('xiaohongshu_execution_completed', (data) => {
      // 小红书脚本执行完成
      console.log('小红书脚本执行完成:', data)
      // 通知小红书自动化页面重置状态
      commit('xiaohongshu/RESET_EXECUTION_STATE', data, { root: true })
    })

    socket.on('xianyu_execution_completed', (data) => {
      // 闲鱼脚本执行完成
      console.log('闲鱼脚本执行完成:', data)
      // 通知闲鱼自动化页面重置状态
      commit('xianyu/RESET_EXECUTION_STATE', data, { root: true })
    })

    // 监听小红书脚本重置事件
    socket.on('xiaohongshu_script_reset', (data) => {
      console.log('[Socket] 收到小红书脚本重置事件:', data)
      // 通知小红书模块重置对应功能的状态
      commit('xiaohongshu/SET_FUNCTION_STATE', {
        functionType: data.functionType,
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          taskId: null,
          config: {},
          selectedDevices: [],
          startTime: null,
          progress: 0,
          status: 'idle',
          logs: [],
          lastResult: null
        }
      }, { root: true })
    })

    // 监听小红书Vuex状态更新事件
    socket.on('xiaohongshu_vuex_state_update', (data) => {
      console.log('[Socket] 收到小红书Vuex状态更新事件:', data)
      if (data.action === 'stopTask') {
        commit('xiaohongshu/STOP_TASK', {
          functionType: data.functionType,
          reason: data.reason || 'server_stop'
        }, { root: true })
      }
    })

    // 监听设备状态更新事件（合并重复的监听器）
    socket.on('device_status_update', (data) => {
      console.log('[Socket] 收到设备状态更新事件:', data)
      // 通知设备管理模块更新设备状态
      commit('device/UPDATE_DEVICE_STATUS', data, { root: true })
      // 同时更新设备列表中的设备状态
      commit('device/UPDATE_DEVICE', {
        deviceId: data.deviceId,
        updates: {
          status: data.status,
          last_seen: data.lastSeen || new Date()
        }
      }, { root: true })
    })

    // 监听设备状态变化事件（包含设备断开连接）
    socket.on('device_status_changed', (data) => {
      console.log('[Socket] 收到设备状态变化事件:', data)

      if (data.type === 'device_disconnected') {
        console.log('[Socket] 设备断开连接，开始清理相关状态:', data.deviceId)

        // 1. 更新设备状态为离线
        commit('device/UPDATE_DEVICE_STATUS', {
          deviceId: data.deviceId,
          status: 'offline',
          lastSeen: data.timestamp || new Date()
        }, { root: true })

        // 2. 清理小红书自动化中该设备的执行状态和选择状态
        commit('xiaohongshu/CLEAR_DEVICE_STATE', {
          deviceId: data.deviceId
        }, { root: true })

        // 3. 清理闲鱼自动化中该设备的执行状态和选择状态
        commit('xianyu/CLEAR_DEVICE_STATE', {
          deviceId: data.deviceId
        }, { root: true })

        // 4. 从设备选择列表中移除该设备
        const currentSelectedDevices = rootState.device.selectedDevices || []
        const updatedSelectedDevices = currentSelectedDevices.filter(id => id !== data.deviceId)
        if (updatedSelectedDevices.length !== currentSelectedDevices.length) {
          commit('device/SET_SELECTED_DEVICES', updatedSelectedDevices, { root: true })
        }

        console.log('[Socket] 设备断开连接状态清理完成:', data.deviceId)
      }
    })

    // 监听设备离线事件（兼容旧版本）
    socket.on('device_offline', (data) => {
      console.log('[Socket] 收到设备离线事件:', data)

      // 触发设备断开连接的处理逻辑
      const statusChangeData = {
        type: 'device_disconnected',
        deviceId: data.deviceId,
        timestamp: new Date()
      }

      // 复用设备状态变化的处理逻辑
      socket.emit('device_status_changed', statusChangeData)
    })
  },

  disconnect({ commit, state }) {
    if (state.socket) {
      state.socket.disconnect()
      commit('SET_SOCKET', null)
      commit('SET_CONNECTED', false)
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
