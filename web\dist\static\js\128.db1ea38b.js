"use strict";(self["webpackChunkautojs_web_control"]=self["webpackChunkautojs_web_control"]||[]).push([[128],{3128:function(e,t,s){s.r(t),s.d(t,{default:function(){return r}});var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"devices"},[t("el-card",[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("设备管理")]),t("div",[t("el-button",{attrs:{type:"primary",icon:"el-icon-refresh",loading:e.loading},on:{click:e.refreshDevices}},[e._v(" 刷新 ")]),e.isAdmin?t("el-button",{attrs:{type:"success",icon:"el-icon-user"},on:{click:e.showAssignDialog}},[e._v(" 设备分配 ")]):e._e(),e.isAdmin?t("el-button",{attrs:{type:"info",icon:"el-icon-question"},on:{click:e.showUnassignedDevices}},[e._v(" 未分配设备 ")]):e._e(),t("el-button",{attrs:{type:"primary",icon:"el-icon-key"},on:{click:e.showConnectionCodesDialog}},[e._v(" 连接码管理 ")])],1)]),t("div",{staticClass:"device-stats"},[t("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("在线: "+e._s(e.onlineCount))]),t("el-tag",{attrs:{type:"warning",size:"medium"}},[e._v("忙碌: "+e._s(e.busyCount))]),t("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("离线: "+e._s(e.offlineCount))]),t("el-tag",{attrs:{type:"info",size:"medium"}},[e._v("总计: "+e._s(e.totalCount))])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.devices},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"55"}}),t("el-table-column",{attrs:{prop:"device_name",label:"设备名称",width:"150"}}),t("el-table-column",{attrs:{prop:"device_id",label:"设备ID",width:"200","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{label:"设备信息",width:"200"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.device_info?t("div",[t("div",[e._v(e._s(s.row.device_info.brand)+" "+e._s(s.row.device_info.model))]),t("div",{staticClass:"device-detail"},[e._v(" Android "+e._s(s.row.device_info.androidVersion)+" ")])]):e._e()]}}])}),t("el-table-column",{attrs:{label:"IP地址",width:"130"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("span",[e._v(e._s(s.row.ip_address||"未知"))])]}}])}),t("el-table-column",{attrs:{label:"屏幕分辨率",width:"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.device_info?t("span",[e._v(" "+e._s(s.row.device_info.screenWidth)+"x"+e._s(s.row.device_info.screenHeight)+" ")]):e._e()]}}])}),t("el-table-column",{attrs:{label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getStatusType(s.row.status),size:"small"}},[e._v(" "+e._s(e.getStatusText(s.row.status))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"last_seen",label:"最后在线",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$moment(t.row.last_seen).fromNow())+" ")]}}])}),t("el-table-column",{attrs:{label:"操作",width:"380"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.viewDevice(s.row)}}},[e._v(" 详情 ")]),t("el-button",{attrs:{size:"mini",type:"info"},on:{click:function(t){return e.viewDeviceApps(s.row)}}},[e._v(" 查看应用 ")]),t("el-button",{attrs:{size:"mini",type:"success",disabled:"online"!==s.row.status},on:{click:function(t){return e.testDevice(s.row)}}},[e._v(" 测试 ")]),t("el-button",{attrs:{size:"mini",type:"warning",disabled:"online"!==s.row.status},on:{click:function(t){return e.disconnectDevice(s.row)}}},[e._v(" 断开 ")]),t("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteDevice(s.row)}}},[e._v(" 删除 ")])]}}])})],1),e.selectedDevices.length>0?t("div",{staticClass:"batch-actions"},[t("span",[e._v("已选择 "+e._s(e.selectedDevices.length)+" 个设备")]),t("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.batchTest}},[e._v(" 批量测试 ")]),t("el-button",{attrs:{type:"success",size:"small"},on:{click:e.showScriptDialog}},[e._v(" 执行脚本 ")])],1):e._e()],1),t("el-dialog",{attrs:{title:"设备详情",visible:e.deviceDialogVisible,width:"900px"},on:{"update:visible":function(t){e.deviceDialogVisible=t}}},[e.currentDevice?t("div",[t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"设备名称"}},[e._v(" "+e._s(e.currentDevice.device_name)+" ")]),t("el-descriptions-item",{attrs:{label:"设备ID"}},[e._v(" "+e._s(e.currentDevice.device_id)+" ")]),t("el-descriptions-item",{attrs:{label:"IP地址"}},[e._v(" "+e._s(e.currentDevice.ip_address||"未知")+" ")]),t("el-descriptions-item",{attrs:{label:"品牌"}},[e._v(" "+e._s(e.currentDevice.device_info?.brand)+" ")]),t("el-descriptions-item",{attrs:{label:"型号"}},[e._v(" "+e._s(e.currentDevice.device_info?.model)+" ")]),t("el-descriptions-item",{attrs:{label:"Android版本"}},[e._v(" "+e._s(e.currentDevice.device_info?.androidVersion)+" ")]),t("el-descriptions-item",{attrs:{label:"屏幕分辨率"}},[e._v(" "+e._s(e.currentDevice.device_info?.screenWidth)+"x"+e._s(e.currentDevice.device_info?.screenHeight)+" ")]),t("el-descriptions-item",{attrs:{label:"状态"}},[t("el-tag",{attrs:{type:e.getStatusType(e.currentDevice.status)}},[e._v(" "+e._s(e.getStatusText(e.currentDevice.status))+" ")])],1),t("el-descriptions-item",{attrs:{label:"最后在线"}},[e._v(" "+e._s(e.$moment(e.currentDevice.last_seen).format("YYYY-MM-DD HH:mm:ss"))+" ")])],1),t("div",{staticStyle:{"margin-top":"20px"}},[t("h4",[e._v("设备应用信息")]),t("el-button",{staticStyle:{"margin-bottom":"10px"},attrs:{type:"primary",size:"small",loading:e.loadingApps},on:{click:e.loadDeviceApps}},[e._v(" "+e._s(e.loadingApps?"加载中...":"刷新应用信息")+" ")]),e.deviceApps?t("div",[t("el-card",{staticStyle:{"margin-bottom":"10px"},attrs:{shadow:"never"}},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("小红书应用 ("+e._s(e.deviceApps.xiaohongshu.length)+"个)")])]),e.deviceApps.xiaohongshu.length>0?t("div",[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.deviceApps.xiaohongshu,size:"small"}},[t("el-table-column",{attrs:{prop:"text",label:"应用名称",width:"200"}}),t("el-table-column",{attrs:{prop:"method",label:"检测方式",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{size:"mini",type:"keyword"===s.row.method?"success":"info"}},[e._v(" "+e._s("keyword"===s.row.method?"关键词":"正则")+" ")])]}}],null,!1,3100346815)}),t("el-table-column",{attrs:{prop:"clickable",label:"可点击",width:"80"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{size:"mini",type:s.row.clickable?"success":"danger"}},[e._v(" "+e._s(s.row.clickable?"是":"否")+" ")])]}}],null,!1,2862314889)}),t("el-table-column",{attrs:{prop:"detectedAt",label:"检测时间",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$moment(t.row.detectedAt).format("MM-DD HH:mm"))+" ")]}}],null,!1,210881056)})],1)],1):t("div",{staticClass:"no-data"},[t("el-empty",{attrs:{description:"未检测到小红书应用","image-size":60}})],1)]),t("el-card",{attrs:{shadow:"never"}},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("闲鱼应用 ("+e._s(e.deviceApps.xianyu.length)+"个)")])]),e.deviceApps.xianyu.length>0?t("div",[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.deviceApps.xianyu,size:"small"}},[t("el-table-column",{attrs:{prop:"text",label:"应用名称",width:"200"}}),t("el-table-column",{attrs:{prop:"method",label:"检测方式",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{size:"mini",type:"keyword"===s.row.method?"success":"info"}},[e._v(" "+e._s("keyword"===s.row.method?"关键词":"正则")+" ")])]}}],null,!1,3100346815)}),t("el-table-column",{attrs:{prop:"clickable",label:"可点击",width:"80"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{size:"mini",type:s.row.clickable?"success":"danger"}},[e._v(" "+e._s(s.row.clickable?"是":"否")+" ")])]}}],null,!1,2862314889)}),t("el-table-column",{attrs:{prop:"detectedAt",label:"检测时间",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$moment(t.row.detectedAt).format("MM-DD HH:mm"))+" ")]}}],null,!1,210881056)})],1)],1):t("div",{staticClass:"no-data"},[t("el-empty",{attrs:{description:"未检测到闲鱼应用","image-size":60}})],1)])],1):e.loadingApps?e._e():t("div",{staticClass:"no-data"},[t("el-empty",{attrs:{description:"点击上方按钮加载应用信息","image-size":60}})],1)],1)],1):e._e()]),t("el-dialog",{attrs:{title:"设备应用信息",visible:e.appsDialogVisible,width:"800px"},on:{"update:visible":function(t){e.appsDialogVisible=t}}},[e.currentDevice?t("div",[t("div",{staticStyle:{"margin-bottom":"20px"}},[t("h4",[e._v(e._s(e.currentDevice.device_name)+" ("+e._s(e.currentDevice.device_id)+")")]),t("el-button",{attrs:{type:"primary",size:"small",loading:e.loadingApps},on:{click:e.loadDeviceApps}},[e._v(" "+e._s(e.loadingApps?"加载中...":"刷新应用信息")+" ")])],1),e.deviceApps?t("div",[t("el-card",{staticStyle:{"margin-bottom":"15px"},attrs:{shadow:"never"}},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("小红书应用 ("+e._s(e.deviceApps.xiaohongshu.length)+"个)")])]),e.deviceApps.xiaohongshu.length>0?t("div",[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.deviceApps.xiaohongshu,size:"small"}},[t("el-table-column",{attrs:{prop:"text",label:"应用名称",width:"200"}}),t("el-table-column",{attrs:{prop:"method",label:"检测方式",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{size:"mini",type:"keyword"===s.row.method?"success":"info"}},[e._v(" "+e._s("keyword"===s.row.method?"关键词":"正则")+" ")])]}}],null,!1,3100346815)}),t("el-table-column",{attrs:{prop:"clickable",label:"可点击",width:"80"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{size:"mini",type:s.row.clickable?"success":"danger"}},[e._v(" "+e._s(s.row.clickable?"是":"否")+" ")])]}}],null,!1,2862314889)}),t("el-table-column",{attrs:{prop:"bounds",label:"位置信息",width:"200","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"detectedAt",label:"检测时间",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$moment(t.row.detectedAt).format("MM-DD HH:mm"))+" ")]}}],null,!1,210881056)})],1)],1):t("div",{staticClass:"no-data"},[t("el-empty",{attrs:{description:"未检测到小红书应用","image-size":60}})],1)]),t("el-card",{attrs:{shadow:"never"}},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("闲鱼应用 ("+e._s(e.deviceApps.xianyu.length)+"个)")])]),e.deviceApps.xianyu.length>0?t("div",[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.deviceApps.xianyu,size:"small"}},[t("el-table-column",{attrs:{prop:"text",label:"应用名称",width:"200"}}),t("el-table-column",{attrs:{prop:"method",label:"检测方式",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{size:"mini",type:"keyword"===s.row.method?"success":"info"}},[e._v(" "+e._s("keyword"===s.row.method?"关键词":"正则")+" ")])]}}],null,!1,3100346815)}),t("el-table-column",{attrs:{prop:"clickable",label:"可点击",width:"80"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{size:"mini",type:s.row.clickable?"success":"danger"}},[e._v(" "+e._s(s.row.clickable?"是":"否")+" ")])]}}],null,!1,2862314889)}),t("el-table-column",{attrs:{prop:"bounds",label:"位置信息",width:"200","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"detectedAt",label:"检测时间",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$moment(t.row.detectedAt).format("MM-DD HH:mm"))+" ")]}}],null,!1,210881056)})],1)],1):t("div",{staticClass:"no-data"},[t("el-empty",{attrs:{description:"未检测到闲鱼应用","image-size":60}})],1)])],1):e.loadingApps?e._e():t("div",{staticClass:"no-data"},[t("el-empty",{attrs:{description:"点击上方按钮加载应用信息","image-size":80}})],1)]):e._e()]),t("el-dialog",{attrs:{title:"执行脚本",visible:e.scriptDialogVisible,width:"500px"},on:{"update:visible":function(t){e.scriptDialogVisible=t}}},[t("el-form",[t("el-form-item",{attrs:{label:"选择脚本"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择脚本"},model:{value:e.selectedScript,callback:function(t){e.selectedScript=t},expression:"selectedScript"}},e._l(e.scripts,function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}),1)],1)],1),t("div",{attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.scriptDialogVisible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",loading:e.executing},on:{click:e.executeScript}},[e._v(" 执行 ")])],1)],1),t("el-dialog",{attrs:{title:"设备分配",visible:e.assignDialogVisible,width:"600px"},on:{"update:visible":function(t){e.assignDialogVisible=t}}},[t("div",[t("p",{staticStyle:{"margin-bottom":"20px",color:"#666"}},[e._v(" 将设备分配给指定用户，支持通过设备ID或IP地址查找设备。 ")]),t("el-form",{attrs:{"label-width":"100px"}},[t("el-form-item",{attrs:{label:"查找方式"}},[t("el-radio-group",{model:{value:e.assignSearchType,callback:function(t){e.assignSearchType=t},expression:"assignSearchType"}},[t("el-radio",{attrs:{label:"deviceId"}},[e._v("设备ID")]),t("el-radio",{attrs:{label:"ipAddress"}},[e._v("IP地址")])],1)],1),t("el-form-item",{attrs:{label:"deviceId"===e.assignSearchType?"设备ID":"IP地址"}},[t("el-input",{attrs:{placeholder:"deviceId"===e.assignSearchType?"请输入设备ID":"请输入IP地址，如：*************",clearable:""},model:{value:e.assignSearchValue,callback:function(t){e.assignSearchValue=t},expression:"assignSearchValue"}})],1),t("el-form-item",{attrs:{label:"目标用户"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择用户",filterable:""},model:{value:e.assignTargetUserId,callback:function(t){e.assignTargetUserId=t},expression:"assignTargetUserId"}},e._l(e.userList,function(e){return t("el-option",{key:e.id,attrs:{label:`${e.username} (ID: ${e.id})`,value:e.id}})}),1)],1)],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.assignDialogVisible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",loading:e.assignLoading,disabled:!e.assignSearchValue||!e.assignTargetUserId},on:{click:e.confirmAssign}},[e._v(" 确认分配 ")])],1)]),t("el-dialog",{attrs:{title:"未分配设备",visible:e.unassignedDialogVisible,width:"800px"},on:{"update:visible":function(t){e.unassignedDialogVisible=t}}},[t("div",[t("p",{staticStyle:{"margin-bottom":"20px",color:"#666"}},[e._v(" 以下是系统中未分配给任何用户的设备列表： ")]),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.unassignedLoading,expression:"unassignedLoading"}],staticStyle:{width:"100%"},attrs:{data:e.unassignedDevices}},[t("el-table-column",{attrs:{prop:"device_name",label:"设备名称",width:"150"}}),t("el-table-column",{attrs:{prop:"device_id",label:"设备ID",width:"200","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{label:"IP地址",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.device_info?.ipAddress||"未知")+" ")]}}])}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"80"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:"online"===s.row.status?"success":"busy"===s.row.status?"warning":"danger",size:"small"}},[e._v(" "+e._s("online"===s.row.status?"在线":"busy"===s.row.status?"忙碌":"离线")+" ")])]}}])}),t("el-table-column",{attrs:{label:"最后连接",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatTime(t.row.last_seen))+" ")]}}])}),t("el-table-column",{attrs:{label:"操作",width:"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.quickAssign(s.row)}}},[e._v(" 快速分配 ")])]}}])})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.unassignedDialogVisible=!1}}},[e._v("关闭")]),t("el-button",{attrs:{type:"primary",loading:e.unassignedLoading},on:{click:e.loadUnassignedDevices}},[e._v(" 刷新 ")])],1)]),t("el-dialog",{attrs:{title:"设备连接码管理",visible:e.connectionCodesDialogVisible,width:"900px"},on:{"update:visible":function(t){e.connectionCodesDialogVisible=t}}},[t("div",[t("div",{staticStyle:{"margin-bottom":"20px"}},[t("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.showCreateCodeDialog}},[e._v(" 创建连接码 ")]),t("el-button",{attrs:{type:"info",icon:"el-icon-refresh",loading:e.codesLoading},on:{click:e.loadConnectionCodes}},[e._v(" 刷新 ")])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.codesLoading,expression:"codesLoading"}],staticStyle:{width:"100%"},attrs:{data:e.connectionCodes}},[t("el-table-column",{attrs:{prop:"code",label:"连接码",width:"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:"primary",size:"medium"}},[e._v(e._s(s.row.code))])]}}])}),t("el-table-column",{attrs:{prop:"description",label:"描述",width:"150","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{label:"使用情况",width:"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("span",[e._v(e._s(s.row.used_count)+"/"+e._s(s.row.max_devices))]),t("el-progress",{staticStyle:{"margin-top":"5px"},attrs:{percentage:s.row.used_count/s.row.max_devices*100,"stroke-width":6,"show-text":!1}})]}}])}),t("el-table-column",{attrs:{label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:s.row.isAvailable?"success":s.row.isExpired?"danger":"warning",size:"small"}},[e._v(" "+e._s(s.row.isAvailable?"可用":s.row.isExpired?"已过期":"已满")+" ")])]}}])}),t("el-table-column",{attrs:{label:"过期时间",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.expires_at?e.formatTime(t.row.expires_at):"永不过期")+" ")]}}])}),t("el-table-column",{attrs:{label:"创建时间",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatTime(t.row.created_at))+" ")]}}])}),t("el-table-column",{attrs:{label:"操作",width:"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.copyConnectionCode(s.row.code)}}},[e._v(" 复制 ")]),t("el-button",{staticStyle:{color:"#f56c6c"},attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteConnectionCode(s.row.id)}}},[e._v(" 删除 ")])]}}])})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.connectionCodesDialogVisible=!1}}},[e._v("关闭")])],1)]),t("el-dialog",{attrs:{title:"创建设备连接码",visible:e.createCodeDialogVisible,width:"500px"},on:{"update:visible":function(t){e.createCodeDialogVisible=t}}},[t("el-form",{attrs:{model:e.newCodeForm,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"描述"}},[t("el-input",{attrs:{placeholder:"请输入连接码描述（可选）"},model:{value:e.newCodeForm.description,callback:function(t){e.$set(e.newCodeForm,"description",t)},expression:"newCodeForm.description"}})],1),t("el-form-item",{attrs:{label:"最大设备数"}},[t("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:100},model:{value:e.newCodeForm.maxDevices,callback:function(t){e.$set(e.newCodeForm,"maxDevices",t)},expression:"newCodeForm.maxDevices"}})],1),t("el-form-item",{attrs:{label:"有效期"}},[t("el-select",{staticStyle:{width:"100%"},model:{value:e.newCodeForm.expiresInHours,callback:function(t){e.$set(e.newCodeForm,"expiresInHours",t)},expression:"newCodeForm.expiresInHours"}},[t("el-option",{attrs:{label:"永不过期",value:null}}),t("el-option",{attrs:{label:"1小时",value:1}}),t("el-option",{attrs:{label:"24小时",value:24}}),t("el-option",{attrs:{label:"7天",value:168}}),t("el-option",{attrs:{label:"30天",value:720}})],1)],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.createCodeDialogVisible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",loading:e.createCodeLoading},on:{click:e.createConnectionCode}},[e._v(" 创建 ")])],1)],1)],1)},a=[],o={name:"Devices",data(){return{selectedDevices:[],deviceDialogVisible:!1,appsDialogVisible:!1,scriptDialogVisible:!1,currentDevice:null,selectedScript:null,executing:!1,hybridManager:null,socket:null,refreshTimer:null,refreshInterval:6e3,isAutoRefreshEnabled:!0,deviceApps:null,loadingApps:!1,assignDialogVisible:!1,assignSearchType:"ipAddress",assignSearchValue:"",assignTargetUserId:null,assignLoading:!1,userList:[],unassignedDialogVisible:!1,unassignedDevices:[],unassignedLoading:!1,connectionCodesDialogVisible:!1,connectionCodes:[],codesLoading:!1,createCodeDialogVisible:!1,createCodeLoading:!1,newCodeForm:{description:"",maxDevices:1,expiresInHours:null}}},computed:{devices(){return this.$store.getters["device/devices"]},scripts(){return this.$store.getters["script/scripts"]},loading(){return this.$store.getters["device/loading"]},totalCount(){return this.devices.length},onlineCount(){return this.devices.filter(e=>"online"===e.status).length},busyCount(){return this.devices.filter(e=>"busy"===e.status).length},offlineCount(){return this.devices.filter(e=>"offline"===e.status).length},isAdmin(){return"admin"===this.$store.getters["auth/user"]?.role}},async created(){await this.loadData(),this.initRealTimeSync(),this.startAutoRefresh()},beforeDestroy(){this.stopAutoRefresh(),this.cleanupRealTimeSync()},methods:{async loadData(){await this.$store.dispatch("device/fetchDevices"),await this.$store.dispatch("script/fetchScripts")},async refreshDevices(){console.log("刷新设备列表..."),await this.$store.dispatch("device/fetchDevices"),console.log("设备列表已刷新，当前设备数量:",this.devices.length)},startAutoRefresh(){this.isAutoRefreshEnabled&&(console.log(`[Devices] 开始自动刷新，间隔: ${this.refreshInterval}ms`),this.refreshTimer=setInterval(()=>{this.autoRefreshDevices()},this.refreshInterval))},stopAutoRefresh(){this.refreshTimer&&(console.log("[Devices] 停止自动刷新"),clearInterval(this.refreshTimer),this.refreshTimer=null)},async autoRefreshDevices(){try{await this.$store.dispatch("device/fetchDevices"),console.log("[Devices] 设备列表自动刷新完成，当前设备数量:",this.devices.length)}catch(e){console.error("[Devices] 自动刷新失败:",e)}},initRealTimeSync(){console.log("初始化WebSocket通信功能"),this.initWebSocketCommunication()},async initWebSocketCommunication(){try{const{getWebSocketManager:e,ensureConnection:t}=await Promise.resolve().then(s.bind(s,6006));this.wsManager=e(),await t(),this.wsManager.off("connection_established"),this.wsManager.on("connection_established",e=>{console.log("WebSocket连接建立:",e.type),this.$message.success("✅ WebSocket连接已建立")}),this.wsManager.off("connection_failed"),this.wsManager.on("connection_failed",e=>{console.error("WebSocket连接失败:",e),this.$message.error(`连接失败: ${e.reason}`)}),this.wsManager.off("device_status_changed"),this.wsManager.on("device_status_changed",e=>{this.handleDeviceStatusChange(e)}),this.wsManager.off("devices_list"),this.wsManager.on("devices_list",e=>{console.log("收到设备列表更新:",e),this.handleDevicesListUpdate(e)}),this.wsManager.off("device_status_update"),this.wsManager.on("device_status_update",e=>{console.log("收到设备状态更新:",e),this.handleDeviceStatusUpdate(e)}),this.wsManager.off("server_shutdown"),this.wsManager.on("server_shutdown",e=>{console.log("服务器关闭通知:",e),this.$message.error("服务器已关闭: "+e.message),this.$store.commit("device/SET_DEVICES",[])}),await this.wsManager.init(),this.showConnectionStatus()}catch(e){console.error("WebSocket通信初始化失败:",e.message),this.$message.error("通信初始化失败: "+e.message)}},showConnectionStatus(){if(this.wsManager){const e=this.wsManager.getConnectionStatus();console.log("连接状态:",e),e.isConnected&&this.$message.success("实时连接 (WebSocket)")}},handleDeviceStatusChange(e){switch(console.log("收到设备状态变化通知:",e),e.type){case"device_connected":this.$message.success(`设备 "${e.deviceName}" 已连接`),this.$store.commit("device/UPDATE_DEVICE",{deviceId:e.deviceId,updates:{status:"online"}});break;case"device_disconnected":this.$message.info(`设备 "${e.deviceName}" 已断开连接`),this.$store.commit("device/UPDATE_DEVICE",{deviceId:e.deviceId,updates:{status:"offline"}});break;case"device_deleted":this.$message.warning(`设备 "${e.deviceName}" 记录已删除`),this.$store.commit("device/REMOVE_DEVICE",e.deviceId);break;case"device_removed":case"device_removed_by_pc":this.$message.warning(`设备 "${e.deviceName}" 已被移除`),this.$store.commit("device/REMOVE_DEVICE",e.deviceId);break}console.log("状态变化事件触发设备列表刷新"),this.refreshDevices()},handleDevicesListUpdate(e){console.log("处理设备列表更新:",e),this.$store.commit("device/SET_DEVICES",e.map(e=>({device_id:e.deviceId,device_name:e.deviceName,device_info:e.deviceInfo,status:e.status,last_seen:e.lastSeen,created_at:e.connectedAt,ip_address:e.ipAddress||"Unknown"})))},handleDeviceStatusUpdate(e){console.log("处理设备状态更新:",e),this.$store.commit("device/UPDATE_DEVICE",{deviceId:e.deviceId,updates:{status:e.status,last_seen:e.lastSeen||(new Date).toISOString()}})},cleanupRealTimeSync(){console.log("清理设备页面事件监听器"),this.wsManager&&(this.wsManager.off("connection_established"),this.wsManager.off("connection_failed"),this.wsManager.off("device_status_changed"),this.wsManager.off("devices_list"),this.wsManager.off("device_status_update"),this.wsManager.off("server_shutdown"),this.wsManager=null),this.socket&&(this.socket.disconnect(),this.socket=null)},handleSelectionChange(e){this.selectedDevices=e},viewDevice(e){this.currentDevice=e,this.deviceDialogVisible=!0,this.deviceApps=null},viewDeviceApps(e){this.currentDevice=e,this.appsDialogVisible=!0,this.deviceApps=null,this.loadDeviceApps()},async loadDeviceApps(){if(this.currentDevice){this.loadingApps=!0;try{const e=await this.$http.get(`/api/device/${this.currentDevice.device_id}/apps`);e.data.success?(this.deviceApps=e.data.data,this.$message.success("应用信息加载成功")):this.$message.error("加载应用信息失败: "+e.data.message)}catch(e){console.error("加载设备应用信息失败:",e),this.$message.error("加载应用信息失败: "+(e.response?.data?.message||e.message))}finally{this.loadingApps=!1}}else this.$message.error("请先选择设备")},async testDevice(e){try{const t=`\n          toast('来自群控系统的测试消息');\n          console.log('设备测试成功: ${e.device_name}');\n        `;await this.$http.post("/api/script/execute",{deviceIds:[e.device_id],script:t}),this.$message.success("测试脚本已发送")}catch(t){this.$message.error("发送失败: "+t.message)}},async disconnectDevice(e){try{await this.$confirm(`确定要断开设备 "${e.device_name}" 的连接吗？\n\n断开后：\n• 手机端会同时收到断开通知并自动断开连接\n• 设备记录将保留，状态变为离线\n• 设备可以重新连接恢复在线状态`,"确认断开连接",{type:"warning",dangerouslyUseHTMLString:!1}),console.log("PC端断开设备:",e.device_name,e.device_id),await this.$store.dispatch("device/deleteDevice",e.device_id),this.$message.success("设备连接已断开，设备记录已保留为离线状态"),setTimeout(()=>{this.refreshDevices()},1e3)}catch(t){"cancel"!==t&&this.$message.error("断开失败: "+t.message)}},async deleteDevice(e){try{await this.$confirm(`确定要删除设备 "${e.device_name}" 吗？\n\n删除后：\n• 设备记录将从数据库中永久删除\n• 如果设备在线，将被强制断开连接\n• 此操作不可恢复`,"确认删除设备记录",{type:"error",dangerouslyUseHTMLString:!1}),await this.$store.dispatch("device/deleteDeviceRecord",e.device_id),this.$message.success("设备记录已删除"),await this.refreshDevices()}catch(t){"cancel"!==t&&this.$message.error("删除失败: "+t.message)}},async batchTest(){const e=this.selectedDevices.map(e=>e.device_id),t="\n        toast('批量测试消息');\n        console.log('批量测试执行成功');\n      ";try{await this.$http.post("/api/script/execute",{deviceIds:e,script:t}),this.$message.success(`测试脚本已发送到 ${e.length} 个设备`)}catch(s){this.$message.error("发送失败: "+s.message)}},showScriptDialog(){this.scriptDialogVisible=!0},async executeScript(){if(this.selectedScript){this.executing=!0;try{const e=this.selectedDevices.map(e=>e.device_id);await this.$store.dispatch("script/executeScript",{deviceIds:e,scriptId:this.selectedScript}),this.$message.success(`脚本已发送到 ${e.length} 个设备`),this.scriptDialogVisible=!1}catch(e){this.$message.error("执行失败: "+e.message)}finally{this.executing=!1}}else this.$message.warning("请选择要执行的脚本")},getStatusType(e){const t={online:"success",offline:"danger",busy:"warning"};return t[e]||"info"},getStatusText(e){const t={online:"在线",offline:"离线",busy:"忙碌"};return t[e]||e},showConnectionCodesDialog(){this.connectionCodesDialogVisible=!0,this.loadConnectionCodes()},async loadConnectionCodes(){this.codesLoading=!0;try{const e=await this.$http.get("/api/device/connection-codes");e.data.success?this.connectionCodes=e.data.data:this.$message.error("加载连接码失败: "+e.data.message)}catch(e){this.$message.error("加载连接码失败: "+e.message)}finally{this.codesLoading=!1}},showCreateCodeDialog(){this.createCodeDialogVisible=!0,this.newCodeForm={description:"",maxDevices:1,expiresInHours:null}},async createConnectionCode(){this.createCodeLoading=!0;try{const e=await this.$http.post("/api/device/connection-codes",this.newCodeForm);e.data.success?(this.$message.success("连接码创建成功: "+e.data.data.code),this.createCodeDialogVisible=!1,this.loadConnectionCodes()):this.$message.error("创建失败: "+e.data.message)}catch(e){this.$message.error("创建失败: "+e.message)}finally{this.createCodeLoading=!1}},async copyConnectionCode(e){try{await navigator.clipboard.writeText(e),this.$message.success("连接码已复制到剪贴板")}catch(t){const s=document.createElement("textarea");s.value=e,document.body.appendChild(s),s.select(),document.execCommand("copy"),document.body.removeChild(s),this.$message.success("连接码已复制到剪贴板")}},async deleteConnectionCode(e){try{await this.$confirm("确定要删除这个连接码吗？","确认删除",{type:"warning"});const t=await this.$http.delete(`/api/device/connection-codes/${e}`);t.data.success?(this.$message.success("连接码删除成功"),this.loadConnectionCodes()):this.$message.error("删除失败: "+t.data.message)}catch(t){"cancel"!==t&&this.$message.error("删除失败: "+t.message)}},formatTime(e){return e?new Date(e).toLocaleString("zh-CN"):""}}},n=o,l=s(1656),c=(0,l.A)(n,i,a,!1,null,"2b530f83",null),r=c.exports}}]);