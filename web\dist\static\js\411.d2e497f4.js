"use strict";(self["webpackChunkautojs_web_control"]=self["webpackChunkautojs_web_control"]||[]).push([[411],{7282:function(e,t,s){s.r(t),s.d(t,{default:function(){return De}});var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"xiaohongshu-automation"},[t("div",{staticClass:"page-header"},[t("div",{staticClass:"header-content"},[e._m(0),t("div",{staticClass:"header-actions"},[t("el-button",{attrs:{type:"info",icon:"el-icon-refresh",size:"small",title:"刷新状态（解决状态显示不一致问题）"},on:{click:e.refreshVuexState}},[e._v(" 刷新状态 ")]),t("el-button",{attrs:{type:"primary",icon:"el-icon-notebook-1"},on:{click:e.goTo<PERSON>ogs}},[e._v(" 查看执行日志 ")])],1)])]),t("el-row",{staticClass:"function-cards",attrs:{gutter:20}},e._l(e.functions,function(s){return t("el-col",{key:s.key,attrs:{span:6}},[t("el-card",{staticClass:"function-card",class:{active:e.selectedFunction===s.key},attrs:{shadow:"hover"},nativeOn:{click:function(t){return e.selectFunction(s.key)}}},[t("div",{staticClass:"card-content"},[t("div",{staticClass:"card-header"},[t("i",{staticClass:"function-icon",class:s.icon}),e.getFunctionRunningCount(s.key)>0?t("el-button",{staticClass:"batch-stop-btn",attrs:{type:"danger",size:"mini",icon:"el-icon-close",title:`停止所有${s.name}任务`},on:{click:function(t){return t.stopPropagation(),e.batchStopFunction(s.key)}}},[e._v(" "+e._s(e.getFunctionRunningCount(s.key))+" ")]):e._e()],1),t("h3",[e._v(e._s(s.name))]),t("p",[e._v(e._s(s.description))]),e.getFunctionRunningCount(s.key)>0?t("div",{staticClass:"running-status"},[t("span",{staticClass:"running-indicator"},[e._v(e._s(e.getFunctionRunningCount(s.key))+"个设备执行中")])]):e._e()])])],1)}),1),"uidFileMessage"===e.selectedFunction?t("UidFileManager",{attrs:{showAllocationConfig:!0,selectedDevices:e.selectedDevices,selectedDeviceCount:e.selectedDevices.length},on:{"allocation-config-change":e.handleUidAllocationConfigChange}}):e._e(),"videoPublish"===e.selectedFunction?t("VideoFileManager",{on:{"videos-uploaded":e.handleVideosUploaded}}):e._e(),e.selectedFunction?t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("DeviceInfo",{attrs:{enableBatchSelect:!0,selectedDeviceIds:e.selectedDevices,currentFunction:e.selectedFunction},on:{"device-selected":e.handleDeviceSelected,"device-removed":e.handleDeviceRemoved,"devices-selection-changed":e.handleDevicesSelectionChanged}})],1),t("el-col",{attrs:{span:16}},[t("el-card",{staticClass:"config-panel"},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(e.getCurrentFunction().name)+"配置")])]),0===e.selectedDevices.length?t("div",{staticClass:"config-section"},[t("el-alert",{attrs:{title:"请先选择执行设备",type:"warning",closable:!1,"show-icon":""}},[e._v(" 请在左侧设备信息中选择要执行脚本的设备 ")])],1):t("div",{staticClass:"config-section"},[t("h4",[e._v("已选设备 ("+e._s(e.selectedDevices.length)+"个)")]),e._l(e.selectedDevices,function(s){return t("el-tag",{key:s,staticStyle:{"margin-right":"8px","margin-bottom":"8px"},attrs:{closable:"",type:e.getDeviceTagType(s)},on:{close:function(t){return e.removeDevice(s)}}},[e._v(" "+e._s(e.getDeviceNameWithStatus(s))+" ")])})],2),e.selectedDevices.length>0?t("div",{staticClass:"config-section"},[e.getComponentName()?t("div",[t("el-tabs",{staticClass:"device-config-tabs",attrs:{type:"card"},on:{"tab-click":e.handleTabClick},model:{value:e.activeDeviceTab,callback:function(t){e.activeDeviceTab=t},expression:"activeDeviceTab"}},e._l(e.selectedDevices,function(s){return t("el-tab-pane",{key:s,attrs:{label:e.getDeviceTabLabel(s),name:s}},[t("div",{staticClass:"device-config-content"},[t("div",{staticClass:"device-config-header"},[t("h5",[e._v(e._s(e.getDeviceNameWithStatus(s))+" - 配置参数")]),t("el-button",{attrs:{type:"primary",size:"small",loading:e.executing,disabled:!e.isDeviceConfigValid(s)},on:{click:function(t){return e.executeForDevice(s)}}},[e._v(" 执行此设备 ")])],1),t(e.getComponentName(),{key:`${e.selectedFunction}-${s}`,ref:`config_${s}`,refInFor:!0,tag:"component",attrs:{"device-id":s,"selected-devices":"videoPublish"===e.selectedFunction?e.selectedDevices:[s],"online-devices":e.getOnlineDevices()},on:{update:function(t){return e.handleDeviceConfigUpdate(s,t)},"validation-error":e.handleValidationError,"validation-success":e.handleValidationSuccess,"task-started":e.handleTaskStarted,"task-stopped":e.handleTaskStopped,"execute-script":e.handleExecuteScript},model:{value:e.deviceConfigs[s],callback:function(t){e.$set(e.deviceConfigs,s,t)},expression:"deviceConfigs[deviceId]"}})],1)])}),1),t("div",{staticClass:"batch-execute-section"},[t("el-button",{attrs:{type:"success",size:"medium",loading:e.executing,disabled:!e.hasValidConfigs(),icon:"el-icon-s-promotion"},on:{click:e.executeAllDevices}},[e._v(" 批量执行所有设备 ("+e._s(e.getValidConfigCount())+"/"+e._s(e.selectedDevices.length)+") ")]),t("el-button",{attrs:{type:"info",size:"medium",disabled:e.selectedDevices.length<=1||!e.activeDeviceTab,icon:"el-icon-document-copy"},on:{click:e.copyConfigToAll}},[e._v(" 复制当前配置到所有设备 ")])],1)],1):t("div",{staticClass:"no-component"},[t("el-alert",{attrs:{title:"组件加载错误",type:"warning",closable:!1,"show-icon":""}},[e._v(" 未找到对应的配置组件："+e._s(e.selectedFunction)+" ")])],1)]):e._e(),t("div",{staticClass:"config-section"},[t("h4",[e._v("执行计划")]),t("el-form",{attrs:{model:e.scheduleConfig,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"执行模式"}},[t("el-radio-group",{model:{value:e.scheduleConfig.mode,callback:function(t){e.$set(e.scheduleConfig,"mode",t)},expression:"scheduleConfig.mode"}},[t("el-radio",{attrs:{label:"immediate"}},[e._v("立即执行")]),t("el-radio",{attrs:{label:"scheduled"}},[e._v("定时执行")]),t("el-radio",{attrs:{label:"loop"}},[e._v("循环执行")])],1)],1),"scheduled"===e.scheduleConfig.mode?t("el-form-item",{attrs:{label:"执行时间"}},[t("el-date-picker",{attrs:{type:"datetime",placeholder:"选择执行时间",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.scheduleConfig.scheduledTime,callback:function(t){e.$set(e.scheduleConfig,"scheduledTime",t)},expression:"scheduleConfig.scheduledTime"}})],1):e._e(),"loop"===e.scheduleConfig.mode?t("el-form-item",{attrs:{label:"循环间隔"}},[t("el-input-number",{attrs:{min:1,max:1440,placeholder:"分钟"},model:{value:e.scheduleConfig.interval,callback:function(t){e.$set(e.scheduleConfig,"interval",t)},expression:"scheduleConfig.interval"}}),t("span",{staticStyle:{"margin-left":"10px"}},[e._v("分钟")])],1):e._e()],1)],1)])],1)],1):e._e(),t("el-card",{staticClass:"log-panel"},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("执行日志")]),t("el-button",{staticStyle:{float:"right"},attrs:{type:"info",size:"small"},on:{click:e.clearLogs}},[e._v(" 清空日志 ")])],1),t("div",{staticClass:"log-content"},[e._l(e.logs,function(s,i){return t("div",{key:i,staticClass:"log-item",class:s.level},[t("span",{staticClass:"log-time"},[e._v(e._s(e.$moment(s.time).format("HH:mm:ss")))]),t("span",{staticClass:"log-device"},[e._v(e._s(s.device))]),t("span",{staticClass:"log-message"},[e._v(e._s(s.message))])])}),0===e.logs.length?t("div",{staticClass:"empty-logs"},[e._v(" 暂无执行日志 ")]):e._e()],2)]),e.showRealtimeStatus?t("div",{staticClass:"realtime-status-panel"},[t("el-card",{staticClass:"status-card"},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("📊 实时执行状态")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:e.hideRealtimeStatus}},[t("i",{staticClass:"el-icon-close"})])],1),t("div",{staticClass:"status-content"},[t("div",{staticClass:"status-section"},[t("h4",[e._v("当前执行功能")]),t("div",{staticClass:"function-info"},[t("span",{staticClass:"function-name"},[e._v(e._s(e.getCurrentFunctionName()))]),t("el-tag",{attrs:{type:e.getStatusTagType(),size:"small"}},[e._v(e._s(e.currentExecutionStatus))])],1)]),t("div",{staticClass:"status-section"},[t("h4",[e._v("设备执行状态")]),t("div",{staticClass:"device-status-grid"},e._l(e.executingDevices,function(s){return t("div",{key:s.deviceId,staticClass:"device-status-item"},[t("div",{staticClass:"device-header"},[t("span",{staticClass:"device-name"},[e._v(e._s(s.deviceName||s.deviceId))]),t("el-tag",{attrs:{type:e.getDeviceStatusType(s.status),size:"mini"}},[e._v(" "+e._s(s.status)+" ")])],1),s.realtimeStatus?t("div",{staticClass:"realtime-info"},[t("div",{staticClass:"status-row"},[t("span",{staticClass:"label"},[e._v("当前步骤：")]),t("span",{staticClass:"value"},[e._v(e._s(s.realtimeStatus.currentStep))])]),t("div",{staticClass:"status-row"},[t("span",{staticClass:"label"},[e._v("执行状态：")]),t("span",{staticClass:"value",class:e.getStatusClass(s.realtimeStatus.currentStatus)},[e._v(" "+e._s(s.realtimeStatus.currentStatus)+" ")])]),s.realtimeStatus.message?t("div",{staticClass:"status-row"},[t("span",{staticClass:"label"},[e._v("状态信息：")]),t("span",{staticClass:"value"},[e._v(e._s(s.realtimeStatus.message))])]):e._e(),s.realtimeStatus.errorMessage?t("div",{staticClass:"status-row error"},[t("span",{staticClass:"label"},[e._v("错误信息：")]),t("span",{staticClass:"value"},[e._v(e._s(s.realtimeStatus.errorMessage))])]):e._e(),e.hasProgressInfo(s.realtimeStatus)?t("div",{staticClass:"progress-info"},[t("div",{staticClass:"progress-text"},[e._v(" 进度: "+e._s(s.realtimeStatus.processedCount||0)+" / "+e._s(s.realtimeStatus.totalCount||0)+" ")]),t("el-progress",{attrs:{percentage:e.getDeviceProgress(s.realtimeStatus),status:e.getProgressStatus(s.realtimeStatus),size:"small"}})],1):e._e()]):e._e()])}),0)])])])],1):e._e()],1)},o=[function(){var e=this,t=e._self._c;return t("div",[t("h2",[e._v("小红书自动化工具")]),t("p",[e._v("专业的小红书运营自动化工具，支持资料修改、群聊管理、消息群发、文章评论等功能")])])}],n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"profile-config"},[t("el-form",{attrs:{model:e.config,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"新昵称"}},[t("el-input",{attrs:{placeholder:"请输入新的昵称（2-24字符）",maxlength:"24","show-word-limit":""},on:{input:e.onInputChange,blur:e.onInputChange},model:{value:e.config.nickname,callback:function(t){e.$set(e.config,"nickname",t)},expression:"config.nickname"}}),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 昵称长度限制：2-24个字符 ")])],1),t("el-form-item",{attrs:{label:"新简介"}},[t("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入新的个人简介",maxlength:"200","show-word-limit":""},on:{input:e.onInputChange,blur:e.onInputChange},model:{value:e.config.profile,callback:function(t){e.$set(e.config,"profile",t)},expression:"config.profile"}})],1),t("el-form-item",{attrs:{label:"修改选项"}},[t("el-checkbox-group",{on:{change:e.onModifyOptionsChange},model:{value:e.config.modifyOptions,callback:function(t){e.$set(e.config,"modifyOptions",t)},expression:"config.modifyOptions"}},[t("el-checkbox",{attrs:{label:"onlyNickname",disabled:e.isOnlyProfileSelected}},[e._v(" 只修改昵称 ")]),t("el-checkbox",{attrs:{label:"onlyProfile",disabled:e.isOnlyNicknameSelected}},[e._v(" 只修改简介 ")]),t("el-checkbox",{attrs:{label:"autoSave"}},[e._v("自动保存设置")])],1),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(' • 只修改昵称：仅更新昵称，不修改简介（与"只修改简介"互斥）'),t("br"),e._v(' • 只修改简介：仅更新简介，不修改昵称（与"只修改昵称"互斥）'),t("br"),e._v(" • 自动保存设置：保存配置以便下次使用 ")])],1),t("el-form-item",{attrs:{label:"小红书应用"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择要使用的小红书应用"},on:{change:e.onAppSelectionChange},model:{value:e.config.selectedApp,callback:function(t){e.$set(e.config,"selectedApp",t)},expression:"config.selectedApp"}},e._l(e.xiaohongshuApps,function(s){return t("el-option",{key:s.text,attrs:{label:s.text,value:s.text}},[t("span",{staticStyle:{float:"left"}},[e._v(e._s(s.text))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(" "+e._s("keyword"===s.method?"关键词":"正则")+" ")])])}),1),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 选择设备上要使用的小红书应用版本 ")])],1),t("el-form-item",{attrs:{label:"操作间隔"}},[t("el-input-number",{attrs:{min:1,max:10,placeholder:"秒"},on:{change:e.onInputChange},model:{value:e.config.operationDelay,callback:function(t){e.$set(e.config,"operationDelay",t)},expression:"config.operationDelay"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("每个操作步骤间隔时间（秒）")])],1),t("el-form-item",{attrs:{label:"安全设置"}},[t("el-checkbox-group",{model:{value:e.config.safetyOptions,callback:function(t){e.$set(e.config,"safetyOptions",t)},expression:"config.safetyOptions"}},[t("el-checkbox",{attrs:{label:"backupOriginal"}},[e._v("备份原有信息")]),t("el-checkbox",{attrs:{label:"confirmBeforeChange"}},[e._v("修改完成后确认保存")]),t("el-checkbox",{attrs:{label:"validateInput"}},[e._v("验证输入格式")])],1)],1),t("el-alert",{attrs:{title:"使用提醒",type:"info",closable:!1,"show-icon":""}},[t("div",[e._v(" • 请确保小红书应用已安装并可正常使用"),t("br"),e._v(" • 建议在修改前备份原有昵称和简介信息"),t("br"),e._v(" • 昵称修改可能有频率限制，请适度使用"),t("br"),e._v(" • 修改过程中请勿手动操作手机 ")])]),t("div",{staticClass:"realtime-status-section",staticStyle:{"margin-bottom":"20px"}},[t("el-divider",{attrs:{"content-position":"left"}},[t("span",{staticStyle:{color:"#409EFF","font-weight":"bold"}},[e._v("实时状态")])]),t("div",{staticClass:"status-grid",staticStyle:{display:"grid","grid-template-columns":"1fr 1fr",gap:"15px","margin-bottom":"15px"}},[t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("操作次数：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#67C23A","font-weight":"bold"}},[e._v(e._s(e.operationCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("已处理步骤：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#E6A23C","font-weight":"bold"}},[e._v(e._s(e.processedStepCount))])])]),t("div",{staticClass:"current-status",staticStyle:{"margin-bottom":"15px"}},[t("span",{staticClass:"status-label"},[e._v("当前状态：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#409EFF","font-weight":"bold"}},[e._v(e._s(e.currentStatus||"等待开始"))])])],1),t("el-form-item",{attrs:{label:"脚本控制"}},[t("el-button",{attrs:{type:"primary",size:"small",disabled:e.isScriptRunning||!e.canExecute,loading:e.isScriptRunning},on:{click:e.startScript}},[e._v(" "+e._s(e.isScriptRunning?"脚本执行中...":"开始执行")+" ")]),t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"danger",size:"small",disabled:!e.isScriptRunning},on:{click:e.stopScript}},[e._v(" 停止脚本 ")]),e.isScriptRunning?t("span",{staticStyle:{"margin-left":"10px",color:"#67C23A","font-size":"12px"}},[e._v(" 脚本正在执行中... ")]):e.isScriptCompleted?t("span",{staticStyle:{"margin-left":"10px",color:"#409EFF","font-size":"12px"}},[e._v(" 脚本执行完成，1分钟后可重新执行 ")]):t("span",{staticStyle:{"margin-left":"10px",color:"#909399","font-size":"12px"}},[e._v(" 脚本未运行 ")])],1)],1)],1)},a=[],l=s(4787),c={data(){return{xiaohongshuApps:[],loadingApps:!1}},watch:{deviceId:{handler(e){e?(console.log("设备ID变化，加载应用信息:",e),this.loadDeviceApps(e)):(this.xiaohongshuApps=[],this.config&&void 0!==this.config.selectedApp&&(this.config.selectedApp=""))},immediate:!0}},methods:{async loadDeviceApps(e){if(e){this.loadingApps=!0;try{console.log("加载设备应用信息:",e);const t=await this.$http.get(`/api/device/${e}/apps`);t.data.success?(this.xiaohongshuApps=t.data.data.xiaohongshu||[],console.log("小红书应用列表:",this.xiaohongshuApps),this.xiaohongshuApps.length>0&&this.config&&(!this.config.selectedApp||""===this.config.selectedApp)&&(this.config.selectedApp=this.xiaohongshuApps[0].text,console.log("自动选择第一个小红书应用:",this.config.selectedApp),this.onInputChange?this.onInputChange():this.updateConfig&&this.updateConfig())):(console.error("加载设备应用信息失败:",t.data.message),this.xiaohongshuApps=[])}catch(t){console.error("加载设备应用信息异常:",t),this.xiaohongshuApps=[]}finally{this.loadingApps=!1}}else console.log("设备ID为空，跳过加载应用信息")},onAppSelectionChange(e){console.log("选择的小红书应用:",e),this.config&&(this.config.selectedApp=e,this.onInputChange?this.onInputChange():this.updateConfig&&this.updateConfig())}}},r={name:"ProfileConfig",mixins:[c],props:{value:{type:Object,default:()=>({})},deviceId:{type:String,default:""}},data(){return{config:{nickname:"",profile:"",modifyOptions:[],operationDelay:2,safetyOptions:[],selectedApp:""},currentLogId:null,currentTaskId:null,operationCount:0,processedStepCount:0,currentStatus:"等待开始",socket:null,eventListenersSetup:!1}},computed:{isScriptRunning(){const e=this.$store.getters["xiaohongshu/getFunctionState"]("profile");return!!e&&e.isScriptRunning},isScriptCompleted(){const e=this.$store.getters["xiaohongshu/getFunctionState"]("profile");return!!e&&e.isScriptCompleted},canExecute(){return this.config.nickname&&this.config.nickname.trim().length>=2},isOnlyNicknameSelected(){return this.config.modifyOptions.includes("onlyNickname")},isOnlyProfileSelected(){return this.config.modifyOptions.includes("onlyProfile")}},watch:{value:{handler(e){e&&"object"===typeof e&&(this.config={...this.config,...e})},immediate:!0},config:{handler(e,t){t&&JSON.stringify(e)!==JSON.stringify(t)&&(console.log("配置发生变化，准备更新:",e),this.$nextTick(()=>{this.updateConfig()}))},deep:!0}},methods:{updateConfig(){console.log("ProfileConfig updateConfig 被调用"),console.log("当前 config:",this.config);const e=this.validateConfig();e.length>0?(console.log("配置验证失败:",e),this.$emit("validation-error",e)):(console.log("配置验证成功"),this.$emit("validation-success")),console.log("发送 input 事件:",this.config),console.log("发送 update 事件:",this.config),this.$emit("input",this.config),this.$emit("update",this.config)},onInputChange(){console.log("用户输入发生变化，触发配置更新"),this.updateConfig()},onModifyOptionsChange(e){console.log("修改选项发生变化:",e);const t=e.includes("onlyNickname"),s=e.includes("onlyProfile");if(t&&s){const t=this.config.modifyOptions;t.includes("onlyNickname")?t.includes("onlyProfile")||(this.config.modifyOptions=e.filter(e=>"onlyNickname"!==e),console.log('移除"只修改昵称"选项，保留"只修改简介"')):(this.config.modifyOptions=e.filter(e=>"onlyProfile"!==e),console.log('移除"只修改简介"选项，保留"只修改昵称"'))}this.onInputChange()},validateConfig(){const e=[];return this.config.modifyOptions.includes("onlyProfile")||((!this.config.nickname||this.config.nickname.trim().length<2)&&e.push("昵称长度不能少于2个字符"),this.config.nickname&&this.config.nickname.length>24&&e.push("昵称长度不能超过24个字符")),this.config.modifyOptions.includes("onlyNickname")||this.config.profile&&0!==this.config.profile.trim().length||e.push("请输入个人简介内容"),this.config.modifyOptions.includes("onlyNickname")&&this.config.modifyOptions.includes("onlyProfile")&&e.push('不能同时选择"只修改昵称"和"只修改简介"'),e},async startScript(){if(this.canExecute)try{console.log("[ProfileConfig] 开始执行修改资料脚本"),this.$emit("execute-script",{functionType:"profile",config:this.config,deviceId:this.deviceId}),console.log("[ProfileConfig] 脚本执行请求已发送")}catch(e){console.error("[ProfileConfig] 启动脚本失败:",e),this.$message.error("启动脚本失败: "+e.message)}else this.$message.warning("请输入有效的昵称（至少2个字符）")},async stopScript(){try{console.log("[ProfileConfig] 停止修改资料脚本执行");let e="/api/xiaohongshu/stop",t={};this.deviceId?(console.log("[ProfileConfig] 停止特定设备:",this.deviceId),console.log("[ProfileConfig] 使用保存的logId:",this.currentLogId),console.log("[ProfileConfig] 使用保存的taskId:",this.currentTaskId),t={deviceId:this.deviceId,taskId:this.currentTaskId||`xiaohongshu_profile_${this.deviceId}`,logId:this.currentLogId}):console.log("[ProfileConfig] 停止所有修改资料任务");const s=await this.$http.post(e,t);s.data.success?(this.$message.success("脚本停止成功"),this.currentLogId=null,this.currentTaskId=null,console.log("[ProfileConfig] 已清空保存的logId和taskId"),await this.$store.dispatch("xiaohongshu/stopTask",{functionType:"profile",reason:"manual"}),this.saveComponentState(),this.$emit("task-stopped",{functionType:"profile",reason:"manual",deviceId:this.deviceId}),this.$root.$emit("xiaohongshu-task-stopped",{functionType:"profile",reason:"manual",deviceId:this.deviceId}),console.log("[ProfileConfig] 脚本停止完成，状态已重置")):this.$message.error("停止脚本失败: "+s.data.message)}catch(e){console.error("[ProfileConfig] 停止脚本请求失败:",e),this.$message.error("停止脚本请求失败"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}})}},handleTaskStarted(e){console.log("[ProfileConfig] 收到任务开始事件:",e),"profile"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[ProfileConfig] 修改资料任务开始，更新状态"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{isScriptRunning:!0,isScriptCompleted:!1,config:this.config}}))},handleTaskStopped(e){console.log("[ProfileConfig] 收到任务停止事件:",e),"profile"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[ProfileConfig] 修改资料任务停止，更新状态"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}}))},async handleTaskRestored(e){"profile"===e.functionType&&(console.log("[ProfileConfig] 恢复任务状态:",e.state),e.state.config&&Object.keys(e.state.config).length>0&&(this.config={...this.config,...e.state.config}),await this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{isScriptRunning:e.state.isScriptRunning,isScriptCompleted:e.state.isScriptCompleted,config:this.config}}),console.log("[ProfileConfig] 状态已恢复:",{isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,config:this.config}))},async saveComponentState(){try{await this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{config:this.config,realtimeData:{operationCount:this.operationCount,processedStepCount:this.processedStepCount,currentStatus:this.currentStatus},taskInfo:{currentTaskId:this.currentTaskId,isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted}}}),console.log("[ProfileConfig] 组件状态已保存")}catch(e){console.error("[ProfileConfig] 保存组件状态失败:",e)}},async restoreComponentState(){try{const e=this.$store.getters["xiaohongshu/getFunctionState"]("profile");e&&Object.keys(e).length>0&&(console.log("[ProfileConfig] 恢复组件状态:",e),e.config&&Object.keys(e.config).length>0&&(this.config={...this.config,...e.config}),e.realtimeData&&(this.operationCount=e.realtimeData.operationCount||0,this.processedStepCount=e.realtimeData.processedStepCount||0,this.currentStatus=e.realtimeData.currentStatus||"等待开始",console.log("[ProfileConfig] 实时状态已恢复:",e.realtimeData)),e.taskInfo&&(this.currentTaskId=e.taskInfo.currentTaskId||null,this.isScriptRunning=e.taskInfo.isScriptRunning||!1,this.isScriptCompleted=e.taskInfo.isScriptCompleted||!1,console.log("[ProfileConfig] 任务信息已恢复:",e.taskInfo)),console.log("[ProfileConfig] 组件状态已恢复:",{isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,config:this.config,realtimeData:{operationCount:this.operationCount,processedStepCount:this.processedStepCount,currentStatus:this.currentStatus},currentTaskId:this.currentTaskId}))}catch(e){console.error("[ProfileConfig] 恢复组件状态失败:",e)}},handleRealtimeStatus(e){console.log("[ProfileConfig] 收到实时状态数据:",e),console.log("[ProfileConfig] 当前组件taskId:",this.currentTaskId),console.log("[ProfileConfig] 数据中的taskId:",e.taskId),console.log("[ProfileConfig] taskId匹配:",this.currentTaskId&&e.taskId===this.currentTaskId),this.currentTaskId&&e.taskId===this.currentTaskId?(console.log("[ProfileConfig] ✅ taskId匹配，更新实时状态:",e),void 0!==e.operationCount&&(this.operationCount=e.operationCount),void 0!==e.processedStepCount&&(this.processedStepCount=e.processedStepCount),e.currentStatus&&(this.currentStatus=e.currentStatus),console.log("[ProfileConfig] 实时状态已更新:",{operationCount:this.operationCount,processedStepCount:this.processedStepCount,currentStatus:this.currentStatus})):console.log("[ProfileConfig] ❌ taskId不匹配或currentTaskId为空，忽略实时状态更新")},resetRealtimeStatus(){this.operationCount=0,this.processedStepCount=0,this.currentStatus="等待开始",console.log("[ProfileConfig] 实时状态已重置")},handleScriptReset(e){console.log("🔄 [ProfileConfig] 收到脚本重置事件:",e),"profile"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId?console.log("⏭️ [ProfileConfig] 脚本重置事件不匹配当前组件，忽略"):(console.log("✅ [ProfileConfig] 重置脚本状态"),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.resetRealtimeStatus(),this.currentTaskId=null,this.currentLogId=null,this.saveComponentState(),console.log("✅ [ProfileConfig] 脚本状态重置完成"))},async initializeSocket(){try{console.log("🔧 [ProfileConfig] 使用统一WebSocket管理器");const{getWebSocketManager:e}=await Promise.resolve().then(s.bind(s,6006));this.wsManager=e(),await this.wsManager.init(),this.socket=this.wsManager.socket,console.log("✅ [ProfileConfig] 已连接到统一WebSocket管理器")}catch(e){console.error("❌ [ProfileConfig] WebSocket连接失败:",e)}this.socket.on("disconnect",()=>{console.log("❌ [ProfileConfig] Socket连接断开")}),this.socket.on("xiaohongshu_realtime_status",e=>{console.log("🎯 [ProfileConfig] 收到WebSocket实时状态事件:",e),this.handleRealtimeStatus(e)}),this.socket.on("test_realtime_broadcast",e=>{console.log("🧪 [ProfileConfig] 收到测试广播:",e)}),console.log("[ProfileConfig] Socket初始化完成")},setupWebSocketListeners(){const e=this.$store.getters["socket/socket"];console.log("[ProfileConfig] 设置WebSocket监听，Socket状态:",e?"可用":"不可用"),console.log("[ProfileConfig] Socket连接状态:",this.$store.getters["socket/connected"]),e&&this.$store.getters["socket/connected"]?(e.off("xiaohongshu_execution_completed"),e.off("xiaohongshu_realtime_status"),e.off("test_realtime_broadcast"),e.on("xiaohongshu_execution_completed",e=>{console.log("[ProfileConfig] 收到WebSocket脚本执行完成事件:",e),e.deviceId!==this.deviceId&&this.deviceId||(console.log("[ProfileConfig] 脚本执行完成，更新状态"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{isScriptRunning:!1,isScriptCompleted:"success"===e.status,config:this.config}}),"success"===e.status&&setTimeout(()=>{this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}})},6e4))}),e.on("xiaohongshu_realtime_status",e=>{console.log("🎯 [ProfileConfig] 直接收到WebSocket实时状态事件:",e),this.handleRealtimeStatus(e)}),e.on("test_realtime_broadcast",e=>{console.log("🧪 [ProfileConfig] 收到测试广播:",e)}),console.log("✅ [ProfileConfig] WebSocket事件监听已设置")):console.warn("⚠️ [ProfileConfig] Socket未连接或不可用，无法设置事件监听")},setupEventListeners(){console.log("[ProfileConfig] 设置事件监听器"),this.$root.$on("xiaohongshu-task-restored",this.handleTaskRestored),this.$root.$on("xiaohongshu-script-reset",this.handleScriptReset),console.log("[ProfileConfig] 事件监听器设置完成")},handleDeviceOffline(){console.log("[ProfileConfig] 处理设备离线，重置状态"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}}),this.resetRealtimeStatus(),this.currentTaskId=null,this.currentLogId=null,this.saveComponentState(),console.log("[ProfileConfig] 设备离线处理完成")},handleServerShutdown(){console.log("[ProfileConfig] 处理服务器关闭，重置状态"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}}),this.resetRealtimeStatus(),this.currentTaskId=null,this.currentLogId=null,this.saveComponentState(),console.log("[ProfileConfig] 服务器关闭处理完成")}},mounted(){console.log("[ProfileConfig] 组件已挂载，设备ID:",this.deviceId),console.log("[ProfileConfig] 初始 config:",this.config),this.eventListenersSetup||(this.setupEventListeners(),this.eventListenersSetup=!0),this.$root.$on("xiaohongshu-task-started",e=>{console.log("[ProfileConfig] 收到任务开始事件:",e),console.log("[ProfileConfig] 当前组件deviceId:",this.deviceId),console.log("[ProfileConfig] 事件deviceId:",e.deviceId),console.log("[ProfileConfig] 功能类型匹配:","profile"===e.functionType),console.log("[ProfileConfig] 设备ID匹配:",!this.deviceId||e.deviceId===this.deviceId),"profile"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId?console.log("[ProfileConfig] 任务开始事件不匹配，忽略"):(console.log("[ProfileConfig] 修改资料任务开始，更新状态 (设备ID匹配)"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{isScriptRunning:!0,isScriptCompleted:!1,config:this.config}}),e.logId&&(this.currentLogId=e.logId,console.log("[ProfileConfig] 保存logId:",this.currentLogId)),e.taskId&&(this.currentTaskId=e.taskId,console.log("[ProfileConfig] 保存taskId:",this.currentTaskId)),this.resetRealtimeStatus(),console.log("[ProfileConfig] 状态已更新:",{isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,currentLogId:this.currentLogId,currentTaskId:this.currentTaskId}))}),this.$root.$on("xiaohongshu-task-stopped",e=>{console.log("[ProfileConfig] 收到任务停止事件:",e);const t="string"===typeof e?e:e.functionType,s=e.reason||"manual",i="profile"===t&&("batch_stop"===s||!this.deviceId||e.deviceId===this.deviceId);i&&(console.log(`[ProfileConfig] 修改资料任务停止，原因: ${s}`),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}}),this.currentLogId=null,this.currentTaskId=null,this.resetRealtimeStatus(),console.log("[ProfileConfig] 已清空保存的logId和taskId"),console.log("[ProfileConfig] 组件状态已通过Vuex更新"),"batch_stop"===s&&this.$message.info("修改资料功能已被批量停止"))}),this.$root.$on("xiaohongshu-script-completed",e=>{console.log("[ProfileConfig] 收到脚本完成事件:",e);const t="string"===typeof e?e:e.functionType;"profile"!==t||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[ProfileConfig] 修改资料脚本完成 (设备ID匹配)"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{isScriptRunning:!1,isScriptCompleted:!0,config:this.config}}),setTimeout(()=>{this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}})},6e4))}),this.$root.$on("device-offline",e=>{console.log("[ProfileConfig] 收到设备离线事件:",e),e.deviceId===this.deviceId&&(console.log("[ProfileConfig] 当前设备离线，重置状态"),this.handleDeviceOffline())}),this.$root.$on("server-shutdown",e=>{console.log("[ProfileConfig] 收到服务器关闭事件:",e),this.handleServerShutdown()}),this.$root.$on("server-disconnect",e=>{console.log("[ProfileConfig] 收到服务器断开事件:",e),this.handleServerShutdown()}),this.$root.$on("force-reset-all",e=>{console.log("[ProfileConfig] 收到强制重置事件:",e),this.handleServerShutdown()}),this.$socket&&this.$socket.on("xiaohongshu_execution_completed",e=>{console.log("[ProfileConfig] 收到WebSocket脚本执行完成事件:",e),e.deviceId!==this.deviceId&&this.deviceId||(console.log("[ProfileConfig] 脚本执行完成，更新状态"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{isScriptRunning:!1,isScriptCompleted:"success"===e.status,config:this.config}}),"success"===e.status&&setTimeout(()=>{this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"profile",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}})},6e4))}),this.restoreComponentState().then(()=>{console.log("[ProfileConfig] 状态恢复完成，开始初始化Socket连接"),this.initializeSocket()}).catch(e=>{console.error("[ProfileConfig] 状态恢复失败:",e),this.initializeSocket()})},beforeDestroy(){console.log("[ProfileConfig] 组件即将销毁，清理资源"),this.saveComponentState(),this.socket&&(this.socket.disconnect(),console.log("[ProfileConfig] Socket连接已断开")),this.$root.$off("xiaohongshu-task-started"),this.$root.$off("xiaohongshu-task-stopped"),this.$root.$off("xiaohongshu-script-completed"),this.$root.$off("xiaohongshu-script-reset",this.handleScriptReset),this.$root.$off("xiaohongshu-task-restored",this.handleTaskRestored),this.$root.$off("device-offline"),this.$root.$off("server-shutdown"),this.$root.$off("server-disconnect"),this.$root.$off("force-reset-all"),this.eventListenersSetup=!1,console.log("[ProfileConfig] 资源清理完成")}},d=r,u=s(1656),h=(0,u.A)(d,n,a,!1,null,"61c47fc9",null),g=h.exports,p=function(){var e=this,t=e._self._c;return t("div",{staticClass:"search-group-chat-config"},[t("el-form",{attrs:{model:e.config,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"搜索关键词"}},[t("el-input",{attrs:{placeholder:"请输入搜索关键词（默认：私域）"},model:{value:e.config.searchKeyword,callback:function(t){e.$set(e.config,"searchKeyword",t)},expression:"config.searchKeyword"}}),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 系统将搜索包含此关键词的群聊 ")])],1),t("el-form-item",{attrs:{label:"目标加入次数"}},[t("el-input-number",{attrs:{min:1,max:50},model:{value:e.config.targetJoinCount,callback:function(t){e.$set(e.config,"targetJoinCount",t)},expression:"config.targetJoinCount"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("想要成功加入的群聊数量")])],1),t("el-form-item",{attrs:{label:"最大滚动次数"}},[t("el-input-number",{attrs:{min:5,max:50},model:{value:e.config.maxScrollAttempts,callback:function(t){e.$set(e.config,"maxScrollAttempts",t)},expression:"config.maxScrollAttempts"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("搜索群聊时最大滚动尝试次数")])],1),t("el-form-item",{attrs:{label:"小红书应用"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择要使用的小红书应用"},on:{change:e.onAppSelectionChange},model:{value:e.config.selectedApp,callback:function(t){e.$set(e.config,"selectedApp",t)},expression:"config.selectedApp"}},e._l(e.xiaohongshuApps,function(s){return t("el-option",{key:s.text,attrs:{label:s.text,value:s.text}},[t("span",{staticStyle:{float:"left"}},[e._v(e._s(s.text))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(" "+e._s("keyword"===s.method?"关键词":"正则")+" ")])])}),1),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 选择设备上要使用的小红书应用版本 ")])],1),t("el-form-item",{attrs:{label:"详细日志"}},[t("el-switch",{attrs:{"active-text":"开启","inactive-text":"关闭"},model:{value:e.config.enableDetailedLog,callback:function(t){e.$set(e.config,"enableDetailedLog",t)},expression:"config.enableDetailedLog"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("启用详细的执行日志输出")])],1),t("div",{staticClass:"realtime-status-section",staticStyle:{"margin-bottom":"20px"}},[t("el-divider",{attrs:{"content-position":"left"}},[t("span",{staticStyle:{color:"#409EFF","font-weight":"bold"}},[e._v("实时状态")])]),t("div",{staticClass:"status-grid",staticStyle:{display:"grid","grid-template-columns":"1fr 1fr",gap:"15px","margin-bottom":"15px"}},[t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("已加入群聊：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#67C23A","font-weight":"bold"}},[e._v(e._s(e.joinedGroupCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("已处理步骤：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#E6A23C","font-weight":"bold"}},[e._v(e._s(e.processedStepCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("滚动尝试次数：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#F56C6C","font-weight":"bold"}},[e._v(e._s(e.scrollAttemptCount))])])]),t("div",{staticClass:"current-status",staticStyle:{"margin-bottom":"15px"}},[t("span",{staticClass:"status-label"},[e._v("当前状态：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#409EFF","font-weight":"bold"}},[e._v(e._s(e.currentStatus||"等待开始"))])])],1),t("el-form-item",{attrs:{label:"脚本控制"}},[t("el-button",{attrs:{type:"primary",size:"small",disabled:e.isScriptRunning||!e.canExecute,loading:e.isScriptRunning},on:{click:e.startScript}},[e._v(" "+e._s(e.isScriptRunning?"脚本执行中...":"开始执行")+" ")]),t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"danger",size:"small",disabled:!e.isScriptRunning},on:{click:e.stopScript}},[e._v(" 停止脚本 ")]),e.isScriptRunning?t("span",{staticStyle:{"margin-left":"10px",color:"#67C23A","font-size":"12px"}},[e._v(" 脚本正在执行中... ")]):e.isScriptCompleted?t("span",{staticStyle:{"margin-left":"10px",color:"#409EFF","font-size":"12px"}},[e._v(" 脚本执行完成，1分钟后可重新执行 ")]):t("span",{staticStyle:{"margin-left":"10px",color:"#909399","font-size":"12px"}},[e._v(" 脚本未运行 ")])],1)],1)],1)},f=[],v={name:"SearchGroupChatConfig",mixins:[c],props:{value:{type:Object,default:()=>({})},selectedDevices:{type:Array,default:()=>[]},onlineDevices:{type:Array,default:()=>[]},deviceId:{type:String,default:""}},data(){return{updateTimer:null,currentLogId:null,currentTaskId:null,config:{searchKeyword:"私域",targetJoinCount:5,maxScrollAttempts:10,enableDetailedLog:!1,selectedApp:""},joinedGroupCount:0,processedStepCount:0,scrollAttemptCount:0,currentStatus:"等待开始",socket:null}},computed:{canExecute(){return this.config.searchKeyword&&this.config.targetJoinCount>0},isScriptRunning(){const e=this.$store.getters["xiaohongshu/getFunctionState"]("searchGroupChat");return!!e&&e.isScriptRunning},isScriptCompleted(){const e=this.$store.getters["xiaohongshu/getFunctionState"]("searchGroupChat");return!!e&&e.isScriptCompleted}},watch:{config:{handler(e){this.debouncedUpdate(e)},deep:!0,immediate:!0},value:{handler(e){e&&Object.keys(e).length>0&&(this.config={...this.config,...e})},immediate:!0}},mounted(){this.$root.$on("xiaohongshu-task-started",this.handleTaskStarted),this.$root.$on("xiaohongshu-task-stopped",this.handleTaskStopped),this.$root.$on("xiaohongshu-script-completed",this.handleScriptCompleted),this.$root.$on("xiaohongshu-task-restored",this.handleTaskRestored),this.$root.$on("device-offline",e=>{e.deviceId===this.deviceId&&(console.log("[SearchGroupChatConfig] 当前设备离线，重置状态"),this.handleDeviceOffline())}),this.$root.$on("server-shutdown",e=>{console.log("[SearchGroupChatConfig] 收到服务器关闭事件:",e),this.handleDeviceOffline()}),this.restoreComponentState().then(()=>{console.log("[SearchGroupChatConfig] 状态恢复完成，开始初始化Socket连接"),this.initializeSocket()}).catch(e=>{console.error("[SearchGroupChatConfig] 状态恢复失败:",e),this.initializeSocket()})},beforeDestroy(){this.saveComponentState(),this.socket&&(this.socket.disconnect(),console.log("[SearchGroupChatConfig] Socket连接已断开")),this.$root.$off("xiaohongshu-task-started",this.handleTaskStarted),this.$root.$off("xiaohongshu-task-stopped",this.handleTaskStopped),this.$root.$off("xiaohongshu-script-completed",this.handleScriptCompleted),this.$root.$off("xiaohongshu-task-restored",this.handleTaskRestored),this.$root.$off("device-offline"),this.$root.$off("server-shutdown"),this.updateTimer&&clearTimeout(this.updateTimer)},methods:{debouncedUpdate(e){this.updateTimer&&clearTimeout(this.updateTimer),this.updateTimer=setTimeout(()=>{this.$emit("update",e)},300)},async startScript(){if(this.canExecute)try{console.log("[SearchGroupChatConfig] 开始执行搜索加群脚本"),this.$emit("execute-script",{functionType:"searchGroupChat",config:this.config,deviceId:this.deviceId}),console.log("[SearchGroupChatConfig] 脚本执行请求已发送")}catch(e){console.error("[SearchGroupChatConfig] 启动脚本失败:",e),this.$message.error("启动脚本失败: "+e.message)}else this.$message.warning("请完善配置参数")},async stopScript(){try{console.log("停止搜索群聊脚本执行"),console.log("[SearchGroupChatConfig] 当前保存的logId:",this.currentLogId),console.log("[SearchGroupChatConfig] 当前保存的taskId:",this.currentTaskId);let e={};this.deviceId?(console.log("[SearchGroupChatConfig] 停止特定设备:",this.deviceId),e={deviceId:this.deviceId,taskId:this.currentTaskId||`xiaohongshu_searchGroupChat_${this.deviceId}`,logId:this.currentLogId||`xiaohongshu_searchGroupChat_${Date.now()}_${this.deviceId}`},console.log("[SearchGroupChatConfig] 停止数据:",e)):console.log("[SearchGroupChatConfig] 停止所有搜索群聊任务");const t=await this.$http.post("/api/xiaohongshu/stop",e);await this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"searchGroupChat",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}}),t.data.success?this.$message.success("脚本停止成功"):this.$message.error("停止脚本失败: "+t.data.message)}catch(e){console.error("[SearchGroupChatConfig] 停止脚本请求失败:",e),this.$message.error("停止脚本请求失败"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"searchGroupChat",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}})}},handleTaskStarted(e){console.log("[SearchGroupChatConfig] 收到任务开始事件:",e),console.log("[SearchGroupChatConfig] 当前组件deviceId:",this.deviceId),console.log("[SearchGroupChatConfig] 事件deviceId:",e.deviceId),console.log("[SearchGroupChatConfig] 功能类型匹配:","searchGroupChat"===e.functionType),console.log("[SearchGroupChatConfig] 设备ID匹配:",!this.deviceId||e.deviceId===this.deviceId),"searchGroupChat"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId?console.log("[SearchGroupChatConfig] 任务开始事件不匹配，忽略"):(console.log("[SearchGroupChatConfig] 任务开始 (设备ID匹配):",e),this.isScriptRunning=!0,this.isScriptCompleted=!1,e.logId&&(this.currentLogId=e.logId,console.log("[SearchGroupChatConfig] 保存logId:",this.currentLogId)),e.taskId&&(this.currentTaskId=e.taskId,console.log("[SearchGroupChatConfig] 保存taskId:",this.currentTaskId)),this.resetRealtimeStatus(),this.$forceUpdate(),console.log("[SearchGroupChatConfig] 组件状态已强制更新"),this.saveComponentState())},handleTaskStopped(e){console.log("[SearchGroupChatConfig] 收到任务停止事件:",e),console.log("[SearchGroupChatConfig] 当前组件deviceId:",this.deviceId),console.log("[SearchGroupChatConfig] 事件deviceId:",e.deviceId),console.log("[SearchGroupChatConfig] 功能类型匹配:","searchGroupChat"===e.functionType),console.log("[SearchGroupChatConfig] 设备ID匹配:",!this.deviceId||e.deviceId===this.deviceId);const t="string"===typeof e?e:e.functionType,s=e.reason||"manual",i=("searchGroupChat"===t||"all"===t)&&("batch_stop"===s||!this.deviceId||e.deviceId===this.deviceId);i?(console.log(`[SearchGroupChatConfig] 搜索群聊任务停止，原因: ${s}`),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.currentLogId=null,this.currentTaskId=null,console.log("[SearchGroupChatConfig] 已清空logId和taskId"),this.resetRealtimeStatus(),this.$forceUpdate(),console.log("[SearchGroupChatConfig] 组件状态已强制更新"),this.$store.dispatch("xiaohongshu/stopTask",{functionType:"searchGroupChat",reason:"batch_stop"===s?"batch_stop":"completed"}),this.saveComponentState(),"batch_stop"===s&&this.$message.info("搜索加群功能已被批量停止")):console.log("[SearchGroupChatConfig] 任务停止事件不匹配，忽略")},handleScriptCompleted(e){console.log("[SearchGroupChatConfig] 收到脚本完成事件:",e);const t="string"===typeof e?e:e.functionType;"searchGroupChat"!==t||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[SearchGroupChatConfig] 搜索群聊脚本完成"),this.isScriptRunning=!1,this.isScriptCompleted=!0,this.currentLogId=null,this.currentTaskId=null,console.log("[SearchGroupChatConfig] 脚本完成，已清空logId和taskId"),this.$forceUpdate(),console.log("[SearchGroupChatConfig] 脚本完成，组件状态已强制更新"),this.saveComponentState(),setTimeout(()=>{this.isScriptCompleted=!1,this.saveComponentState()},6e4))},handleTaskRestored(e){"searchGroupChat"===e.functionType&&(console.log("[SearchGroupChatConfig] 恢复任务状态:",e.state),this.isScriptRunning=e.state.isScriptRunning,this.isScriptCompleted=e.state.isScriptCompleted,e.state.config&&Object.keys(e.state.config).length>0&&(this.config={...this.config,...e.state.config}),console.log("[SearchGroupChatConfig] 状态已恢复:",{isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,config:this.config}))},async saveComponentState(){try{await this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"searchGroupChat",stateData:{isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,config:this.config,currentLogId:this.currentLogId,currentTaskId:this.currentTaskId,realtimeData:{joinedGroupCount:this.joinedGroupCount,processedStepCount:this.processedStepCount,scrollAttemptCount:this.scrollAttemptCount,currentStatus:this.currentStatus}}}),console.log("[SearchGroupChatConfig] 组件状态已保存")}catch(e){console.error("[SearchGroupChatConfig] 保存组件状态失败:",e)}},async restoreComponentState(){try{const e=this.$store.getters["xiaohongshu/getFunctionState"]("searchGroupChat");e&&Object.keys(e).length>0&&(console.log("[SearchGroupChatConfig] 恢复组件状态:",e),this.isScriptRunning=e.isScriptRunning||!1,this.isScriptCompleted=e.isScriptCompleted||!1,this.currentLogId=e.currentLogId||null,this.currentTaskId=e.currentTaskId||null,e.config&&Object.keys(e.config).length>0&&(this.config={...this.config,...e.config}),e.realtimeData&&(this.joinedGroupCount=e.realtimeData.joinedGroupCount||0,this.processedStepCount=e.realtimeData.processedStepCount||0,this.scrollAttemptCount=e.realtimeData.scrollAttemptCount||0,this.currentStatus=e.realtimeData.currentStatus||"等待开始",console.log("[SearchGroupChatConfig] 实时状态已恢复:",e.realtimeData)),console.log("[SearchGroupChatConfig] 组件状态已恢复:",{isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,currentLogId:this.currentLogId,currentTaskId:this.currentTaskId,config:this.config,realtimeData:{joinedGroupCount:this.joinedGroupCount,processedStepCount:this.processedStepCount,scrollAttemptCount:this.scrollAttemptCount,currentStatus:this.currentStatus}}))}catch(e){console.error("[SearchGroupChatConfig] 恢复组件状态失败:",e)}},handleRealtimeStatus(e){console.log("🔄 [SearchGroupChatConfig] 收到实时状态数据:",e),console.log("📋 [SearchGroupChatConfig] 当前组件taskId:",this.currentTaskId),console.log("📋 [SearchGroupChatConfig] 数据中的taskId:",e.taskId),console.log("🔍 [SearchGroupChatConfig] taskId匹配:",this.currentTaskId&&e.taskId===this.currentTaskId),this.currentTaskId&&e.taskId===this.currentTaskId?(console.log("✅ [SearchGroupChatConfig] taskId匹配，更新实时状态:",e),void 0!==e.joinedGroupCount&&(this.joinedGroupCount=e.joinedGroupCount,console.log("📊 [SearchGroupChatConfig] 更新已加入群聊数:",this.joinedGroupCount)),void 0!==e.processedStepCount&&(this.processedStepCount=e.processedStepCount,console.log("📊 [SearchGroupChatConfig] 更新已处理步骤数:",this.processedStepCount)),void 0!==e.scrollAttemptCount&&(this.scrollAttemptCount=e.scrollAttemptCount,console.log("📊 [SearchGroupChatConfig] 更新滚动尝试次数:",this.scrollAttemptCount)),e.currentStatus&&(this.currentStatus=e.currentStatus,console.log("📊 [SearchGroupChatConfig] 更新当前状态:",this.currentStatus)),console.log("✅ [SearchGroupChatConfig] 实时状态已更新:",{joinedGroupCount:this.joinedGroupCount,processedStepCount:this.processedStepCount,scrollAttemptCount:this.scrollAttemptCount,currentStatus:this.currentStatus}),this.$forceUpdate(),console.log("🔄 [SearchGroupChatConfig] 已强制更新视图")):console.log("❌ [SearchGroupChatConfig] taskId不匹配或currentTaskId为空，忽略实时状态更新")},resetRealtimeStatus(){this.joinedGroupCount=0,this.processedStepCount=0,this.scrollAttemptCount=0,this.currentStatus="等待开始",console.log("[SearchGroupChatConfig] 实时状态已重置")},async initializeSocket(){try{console.log("🔧 [SearchGroupChatConfig] 使用统一WebSocket管理器");const{getWebSocketManager:e}=await Promise.resolve().then(s.bind(s,6006));this.wsManager=e(),await this.wsManager.init(),this.socket=this.wsManager.socket,console.log("✅ [SearchGroupChatConfig] 已连接到统一WebSocket管理器")}catch(e){console.error("❌ [SearchGroupChatConfig] WebSocket连接失败:",e)}this.socket.on("connect",()=>{console.log("✅ [SearchGroupChatConfig] Socket连接成功")}),this.socket.on("disconnect",()=>{console.log("❌ [SearchGroupChatConfig] Socket连接断开")}),this.socket.on("connect_error",e=>{console.error("❌ [SearchGroupChatConfig] Socket连接错误:",e)}),this.socket.onAny((e,t)=>{(e.includes("xiaohongshu")||e.includes("test"))&&console.log(`🔍 [SearchGroupChatConfig] 收到WebSocket事件: ${e}`,t)}),this.socket.on("xiaohongshu_realtime_status",e=>{console.log("🎯 [SearchGroupChatConfig] 收到WebSocket实时状态事件:",e),this.handleRealtimeStatus(e)}),this.socket.on("xiaohongshu_execution_completed",e=>{console.log("[SearchGroupChatConfig] 收到WebSocket脚本执行完成事件:",e),e.deviceId!==this.deviceId&&this.deviceId||(console.log("[SearchGroupChatConfig] 脚本执行完成，更新状态"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"searchGroupChat",stateData:{isScriptRunning:!1,isScriptCompleted:"success"===e.status,config:this.config}}),"success"===e.status&&setTimeout(()=>{this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"searchGroupChat",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}})},6e4))}),this.socket.on("test_realtime_broadcast",e=>{console.log("🧪 [SearchGroupChatConfig] 收到测试广播:",e)}),console.log("✅ [SearchGroupChatConfig] Socket初始化完成")},handleDeviceOffline(){console.log("[SearchGroupChatConfig] 处理设备离线，重置状态"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"searchGroupChat",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}}),this.resetRealtimeStatus(),this.currentTaskId=null,this.currentLogId=null,this.saveComponentState(),console.log("[SearchGroupChatConfig] 设备离线处理完成")}}},m=v,C=(0,u.A)(m,p,f,!1,null,"17d487c8",null),S=C.exports,_=function(){var e=this,t=e._self._c;return t("div",{staticClass:"group-message-config-original"},[t("el-card",{staticClass:"service-card",staticStyle:{"margin-bottom":"10px"}},[t("div",{staticClass:"service-content"},[t("div",{staticClass:"service-indicator"}),t("div",{staticClass:"service-info"},[t("div",{staticClass:"service-header"},[t("span",{staticClass:"service-title"},[e._v("无障碍服务")]),t("el-switch",{staticStyle:{"margin-left":"auto"},model:{value:e.autoServiceEnabled,callback:function(t){e.autoServiceEnabled=t},expression:"autoServiceEnabled"}})],1),t("div",{staticClass:"service-status",style:{color:e.autoServiceEnabled?"#4CAF50":"#F44336"}},[e._v(" "+e._s(e.autoServiceEnabled?"已开启":"未开启")+" ")])])])]),t("el-card",{staticClass:"group-message-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",{staticStyle:{"font-size":"18px","font-weight":"bold",color:"#333333"}},[e._v("群发设置")])]),t("div",{staticStyle:{"margin-bottom":"15px"}},[t("div",{staticStyle:{"margin-bottom":"8px"}},[t("span",{staticStyle:{"font-size":"14px",color:"#666666"}},[e._v("小红书应用")])]),t("el-select",{staticStyle:{width:"100%",background:"#F5F5F5","border-radius":"4px"},attrs:{placeholder:"请选择要使用的小红书应用"},on:{change:e.onAppSelectionChange},model:{value:e.selectedApp,callback:function(t){e.selectedApp=t},expression:"selectedApp"}},e._l(e.xiaohongshuApps,function(s){return t("el-option",{key:s.text,attrs:{label:s.text,value:s.text}},[t("span",{staticStyle:{float:"left"}},[e._v(e._s(s.text))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(" "+e._s("keyword"===s.method?"关键词":"正则")+" ")])])}),1)],1),t("div",{staticStyle:{"margin-bottom":"15px"}},[t("div",{staticStyle:{"margin-bottom":"8px"}},[t("span",{staticStyle:{"font-size":"14px",color:"#666666"}},[e._v("发送间隔(秒)")])]),t("el-input",{staticStyle:{width:"100%",background:"#F5F5F5","border-radius":"4px"},attrs:{placeholder:"每条消息发送间隔时间（默认：10秒）",type:"number"},model:{value:e.intervalInput,callback:function(t){e.intervalInput=t},expression:"intervalInput"}})],1),t("div",{staticClass:"statistics-section"},[t("div",{staticClass:"stat-item",staticStyle:{"margin-bottom":"15px"}},[t("span",{staticClass:"stat-label"},[e._v("已发送消息数：")]),t("span",{staticClass:"stat-value sent-count"},[e._v(e._s(e.sentMessageCount))])]),t("div",{staticClass:"stat-item",staticStyle:{"margin-bottom":"15px"}},[t("span",{staticClass:"stat-label"},[e._v("已处理控件数：")]),t("span",{staticClass:"stat-value processed-count"},[e._v(e._s(e.processedControlCount))])]),t("div",{staticClass:"stat-item",staticStyle:{"margin-bottom":"15px"}},[t("span",{staticClass:"stat-label"},[e._v("执行次数：")]),t("span",{staticClass:"stat-value execution-count"},[e._v(e._s(e.executionCount))])]),t("div",{staticClass:"stat-item",staticStyle:{"margin-bottom":"15px"}},[t("span",{staticClass:"stat-label"},[e._v("循环次数：")]),t("span",{staticClass:"stat-value loop-count"},[e._v(e._s(e.loopCount))])]),t("div",{staticClass:"stat-item",staticStyle:{"margin-bottom":"15px"}},[t("span",{staticClass:"stat-label"},[e._v("当前状态：")]),t("span",{staticClass:"stat-value current-status"},[e._v(e._s(e.currentStatus))])]),t("div",{staticClass:"setting-item",staticStyle:{"margin-bottom":"15px"}},[t("span",{staticClass:"setting-label",staticStyle:{width:"120px",display:"inline-block"}},[e._v("自动保存设置")]),t("el-switch",{model:{value:e.autoSave,callback:function(t){e.autoSave=t},expression:"autoSave"}})],1),t("div",{staticClass:"setting-item",staticStyle:{"margin-bottom":"20px"}},[t("span",{staticClass:"setting-label",staticStyle:{width:"120px",display:"inline-block"}},[e._v("循环执行模式")]),t("el-switch",{model:{value:e.loopMode,callback:function(t){e.loopMode=t},expression:"loopMode"}})],1),t("div",{staticStyle:{display:"flex",gap:"4px"}},[t("el-button",{staticStyle:{flex:"1",height:"50px",background:"#FF4FB3FF","border-color":"#FF4FB3FF",margin:"0"},attrs:{type:"primary",size:"medium",disabled:e.isRunning},on:{click:e.startScript}},[e._v(" "+e._s(e.startButtonText)+" ")]),t("el-button",{staticStyle:{flex:"0.8",height:"50px",background:"#4CAF50","border-color":"#4CAF50",margin:"0"},attrs:{type:"success",size:"medium",disabled:!e.restartEnabled},on:{click:e.restartScript}},[e._v(" 再次开始 ")]),t("el-button",{staticStyle:{flex:"0.6",height:"50px",background:"#F44336","border-color":"#F44336",margin:"0"},attrs:{type:"danger",size:"medium",disabled:!e.stopEnabled},on:{click:e.stopScript}},[e._v(" 终止 ")])],1)])]),t("el-card",{staticClass:"log-card",staticStyle:{"margin-top":"10px"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",{staticStyle:{"font-size":"14px","font-weight":"bold",color:"#FF4FB3FF"}},[e._v("操作日志")])]),t("div",{staticClass:"log-content",staticStyle:{height:"150px","overflow-y":"auto",background:"#FAFAFA",padding:"10px","border-radius":"4px"}},[0===e.logMessages.length?t("div",{staticStyle:{color:"#666666","font-size":"12px"}},[e._v(" 等待操作... ")]):t("div",e._l(e.logMessages,function(s,i){return t("div",{key:i,staticStyle:{color:"#666666","font-size":"12px","line-height":"1.4","margin-bottom":"2px"}},[e._v(" "+e._s(s)+" ")])}),0)])]),t("el-card",{staticClass:"usage-card",staticStyle:{"margin-top":"10px"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",{staticStyle:{"font-size":"14px","font-weight":"bold",color:"#FF4FB3FF"}},[e._v("使用说明")])]),t("div",{staticClass:"usage-content",staticStyle:{padding:"8px 0"}},[t("div",{staticClass:"usage-item"},[e._v("• 请先开启无障碍服务")]),t("div",{staticClass:"usage-item"},[e._v("• 确保小红书应用已安装")]),t("div",{staticClass:"usage-item"},[e._v("• 可自定义发送间隔时间")]),t("div",{staticClass:"usage-item"},[e._v("• 脚本会自动记录所有群聊控件并循环点击")]),t("div",{staticClass:"usage-item"},[e._v("• 自动排除系统消息、活动消息等无效控件")]),t("div",{staticClass:"usage-item"},[e._v("• 智能滑动查找页面中的所有群聊")]),t("div",{staticClass:"usage-item"},[e._v("• 执行过程中请勿手动操作手机")]),t("div",{staticClass:"usage-item success"},[e._v("• 无需截屏权限，仅使用无障碍服务")]),t("div",{staticClass:"usage-item warning"},[e._v("• 每次执行完成后自动退出小红书并休眠30秒(测试版)")]),t("div",{staticClass:"usage-item loop"},[e._v("• 循环执行模式：开启后脚本会自动循环执行")]),t("div",{staticClass:"usage-item loop"},[e._v("• 循环模式下：执行完成→退出小红书→休眠1小时→重新执行")]),t("div",{staticClass:"usage-item primary"},[e._v("• 开始执行脚本：首次启动脚本")]),t("div",{staticClass:"usage-item success"},[e._v("• 再次开始：重新从头开始执行，重置所有状态")]),t("div",{staticClass:"usage-item danger"},[e._v("• 终止：完全终止脚本并重置所有状态")])])])],1)},I=[],b={name:"GroupMessageConfigOriginal",mixins:[c],props:{value:{type:Object,default:()=>({})},selectedDevices:{type:Array,default:()=>[]},onlineDevices:{type:Array,default:()=>[]},deviceId:{type:String,default:""}},data(){return{autoServiceEnabled:!1,intervalInput:"10",autoSave:!0,loopMode:!1,isRunning:!1,sentMessageCount:0,processedControlCount:0,executionCount:0,loopCount:0,currentStatus:"等待开始",logMessages:[],socket:null,currentTaskId:null,currentLogId:null,selectedApp:""}},watch:{intervalInput(){this.updateConfig()},loopMode(){this.updateConfig()},autoSave(){this.updateConfig()},selectedApp(){this.updateConfig()},value:{handler(e){e&&"object"===typeof e&&(void 0!==e.sendInterval&&(this.intervalInput=e.sendInterval.toString()),void 0!==e.executionMode&&(this.loopMode="loop"===e.executionMode),void 0!==e.autoSave&&(this.autoSave=e.autoSave))},immediate:!0,deep:!0}},computed:{startButtonText(){return this.isRunning?"正在执行...":"开始执行脚本"},restartEnabled(){return!this.isRunning},stopEnabled(){return this.isRunning}},async mounted(){this.initializeSocket(),this.addLog("UI界面初始化完成"),this.$root.$on("xiaohongshu_realtime_status",e=>{console.log("[GroupMessageConfig] 收到全局实时状态事件:",e),this.handleRealtimeStatus(e)}),this.$root.$on("xiaohongshu-task-started",e=>{console.log("[GroupMessageConfig] 收到任务开始事件:",e),"groupMessage"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[GroupMessageConfig] 循环群发任务开始，更新按钮状态"),this.isRunning=!0,e.logId&&(this.currentLogId=e.logId,console.log("[GroupMessageConfig] 保存logId:",this.currentLogId)),e.taskId&&(this.currentTaskId=e.taskId,console.log("[GroupMessageConfig] 保存taskId:",this.currentTaskId)),this.isRunning=!0,this.updateCurrentStatus("脚本执行中"),this.addLog("=== 脚本开始执行 ==="),this.sentMessageCount=0,this.processedControlCount=0,this.executionCount=0,this.loopCount=0,this.saveComponentState())}),this.$root.$on("xiaohongshu-task-stopped",e=>{console.log("[GroupMessageConfig] 收到任务停止事件:",e);const t="string"===typeof e?e:e.functionType;"groupMessage"!==t||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[GroupMessageConfig] 循环群发任务停止，重置按钮状态"),this.currentLogId=null,this.currentTaskId=null,console.log("[GroupMessageConfig] 已清空logId和taskId"),this.handleScriptStopped(),this.saveComponentState())}),this.$root.$on("xiaohongshu-script-completed",e=>{console.log("[GroupMessageConfig] 收到脚本完成事件:",e);const t="string"===typeof e?e:e.functionType;"groupMessage"!==t||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[GroupMessageConfig] 循环群发脚本完成"),this.isRunning=!1,this.updateCurrentStatus("success"===e.status?"执行完成":"执行失败"),this.addLog("success"===e.status?"✅ 脚本执行完成":"❌ 脚本执行失败"),this.saveComponentState())}),this.$root.$on("xiaohongshu_execution_completed",e=>{console.log("[GroupMessageConfig] 收到脚本执行完成事件:",e);const t="string"===typeof e?e:e.functionType;"groupMessage"!==t||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[GroupMessageConfig] 循环群发脚本执行完成"),this.isRunning=!1,this.updateCurrentStatus("success"===e.status?"执行完成":"执行失败"),this.addLog("success"===e.status?"✅ 脚本执行完成":"❌ 脚本执行失败"),this.currentLogId=null,this.currentTaskId=null,console.log("[GroupMessageConfig] 脚本完成，已清空logId和taskId"),this.saveComponentState())}),await this.checkRunningTasks(),this.isRunning||this.addLog("请先开启无障碍服务，然后点击开始按钮"),this.$socket&&this.$socket.on("xiaohongshu_execution_completed",e=>{console.log("[GroupMessageConfig] 收到WebSocket脚本执行完成事件:",e),e.deviceId!==this.deviceId&&this.deviceId||(console.log("[GroupMessageConfig] 脚本执行完成，更新状态"),this.isRunning=!1,this.updateCurrentStatus("success"===e.status?"执行完成":"执行失败"),this.addLog("success"===e.status?"✅ 脚本执行完成":"❌ 脚本执行失败"),this.saveComponentState())}),this.restoreComponentState()},beforeDestroy(){this.saveComponentState(),this.socket&&(this.socket.off("xiaohongshu_task_update"),this.socket.off("xiaohongshu_all_tasks_stopped"),this.socket.off("script_stopped"),this.socket.off("script_status"),this.socket.off("xiaohongshu_realtime_status"),console.log("[GroupMessageConfig] Socket事件监听器已清理")),this.$root.$off("xiaohongshu_realtime_status"),this.$root.$off("xiaohongshu-task-started"),this.$root.$off("xiaohongshu-task-stopped"),this.$root.$off("xiaohongshu-script-completed"),this.$root.$off("xiaohongshu_execution_completed")},methods:{addLog(e){const t=(new Date).toLocaleTimeString(),s=`[${t}] ${e}`;this.logMessages.push(s),this.logMessages.length>20&&this.logMessages.shift(),console.log(s)},updateCurrentStatus(e){this.currentStatus=e},updateSentCount(){},updateProcessedCount(){},updateExecutionCount(){},updateLoopCount(){},initializeSocket(){if(this.socket=this.$store.getters["socket/socket"],!this.socket)return console.warn("[GroupMessageConfig] 全局Socket未连接，等待连接..."),void this.$store.watch(e=>e.socket.socket,e=>{e&&(console.log("[GroupMessageConfig] 检测到Socket连接，重新初始化监听器"),this.socket=e,this.setupSocketListeners())});console.log("[GroupMessageConfig] 使用全局Socket连接"),this.setupSocketListeners()},setupSocketListeners(){this.socket&&(console.log("[GroupMessageConfig] 设置Socket事件监听器"),this.socket.on("xiaohongshu_task_update",e=>{console.log("[GroupMessageConfig] Socket收到任务状态更新:",e),this.handleTaskUpdate(e)}),this.socket.on("xiaohongshu_all_tasks_stopped",()=>{console.log("[GroupMessageConfig] Socket收到所有任务停止事件"),this.handleAllTasksStopped()}),this.socket.on("script_stopped",e=>{console.log("[GroupMessageConfig] Socket收到脚本停止事件:",e),this.handleDeviceScriptStopped(e)}),this.socket.on("script_status",e=>{console.log("[GroupMessageConfig] Socket收到脚本状态事件:",e),this.handleScriptStatus(e)}),this.socket.on("xiaohongshu_realtime_status",e=>{console.log("[GroupMessageConfig] Socket收到实时状态更新:",e),this.handleRealtimeStatus(e)}),console.log("[GroupMessageConfig] Socket事件监听器设置完成"))},handleTaskUpdate(e){e.taskId===this.currentTaskId&&e.params&&"groupMessage"===e.params.function&&(this.addLog(`任务更新: ${e.message}`),"executing"===e.status?this.updateCurrentStatus("正在执行"):"sleeping"===e.status?this.updateCurrentStatus("休眠中"):"completed"===e.status?this.loopMode?(this.loopCount++,this.updateCurrentStatus("循环执行中")):this.handleTaskStopped():"failed"===e.status&&(this.updateCurrentStatus("执行失败"),this.$message.error("任务执行失败: "+e.message)))},handleAllTasksStopped(){this.isRunning&&(this.addLog("📱 收到服务器停止信号"),this.addLog("✅ 手机端脚本已停止执行"),this.handleScriptStopped())},handleDeviceScriptStopped(e){this.currentTaskId&&e.taskId===this.currentTaskId&&(this.addLog(`📱 设备 ${e.deviceId} 脚本已停止`),this.handleScriptStopped())},handleScriptStatus(e){this.currentTaskId&&e.taskId===this.currentTaskId&&(this.addLog(`📱 设备状态: ${e.status} - ${e.message}`),"stopped"===e.status||"completed"===e.status||"failed"===e.status?this.handleScriptStopped():"running"===e.status&&this.updateCurrentStatus("正在执行"))},handleRealtimeStatus(e){this.currentTaskId&&e.taskId===this.currentTaskId&&(void 0!==e.sentMessageCount&&(this.sentMessageCount=e.sentMessageCount),void 0!==e.processedControlCount&&(this.processedControlCount=e.processedControlCount),void 0!==e.executionCount&&(this.executionCount=e.executionCount),void 0!==e.loopCount&&(this.loopCount=e.loopCount),e.currentStatus&&this.updateCurrentStatus(e.currentStatus),e.message&&this.addLog(e.message),console.log("实时状态更新:",e))},async checkRunningTasks(){try{const e=await this.$http.get("/api/xiaohongshu/tasks");if(e.data.success&&e.data.data.activeTasks){const t=e.data.data.activeTasks,s=t.find(e=>"groupMessage"===e.function&&("running"===e.status||"executing"===e.status));s?(this.addLog("🔄 检测到正在运行的群发任务，正在恢复状态..."),this.restoreTaskState(s)):this.addLog("✅ 当前没有正在运行的群发任务")}}catch(e){console.error("检查运行任务失败:",e),this.addLog("❌ 检查运行任务失败: "+e.message)}},restoreTaskState(e){this.currentTaskId=e.id,this.isRunning=!0,e.config&&(e.config.sendInterval&&(this.intervalInput=e.config.sendInterval.toString()),e.config.executionMode&&(this.loopMode="loop"===e.config.executionMode),void 0!==e.config.autoSave&&(this.autoSave=e.config.autoSave)),e.realtimeData?(this.sentMessageCount=e.realtimeData.sentMessageCount||0,this.processedControlCount=e.realtimeData.processedControlCount||0,this.executionCount=e.realtimeData.executionCount||0,this.loopCount=e.realtimeData.loopCount||0,e.realtimeData.currentStatus?this.updateCurrentStatus(e.realtimeData.currentStatus):this.updateCurrentStatus("正在执行"),this.addLog(`📊 已恢复实时数据: 消息${this.sentMessageCount} 控件${this.processedControlCount} 执行${this.executionCount} 循环${this.loopCount}`)):this.updateCurrentStatus("正在执行"),this.addLog(`✅ 已恢复任务状态: ${e.id}`),this.addLog("📊 实时状态数据将自动同步"),this.updateConfig()},async saveComponentState(){try{const e=`groupMessage_state_${this.deviceId||"default"}`,t={isRunning:this.isRunning,currentStatus:this.currentStatus,intervalInput:this.intervalInput,loopMode:this.loopMode,currentTaskId:this.currentTaskId,currentLogId:this.currentLogId,sentMessageCount:this.sentMessageCount,processedControlCount:this.processedControlCount,executionCount:this.executionCount,loopCount:this.loopCount,logMessages:this.logMessages,realtimeLogs:this.realtimeLogs};localStorage.setItem(e,JSON.stringify(t)),console.log("[GroupMessageConfig] 状态已保存:",t)}catch(e){console.error("[GroupMessageConfig] 保存状态失败:",e)}},async restoreComponentState(){try{const e=`groupMessage_state_${this.deviceId||"default"}`,t=localStorage.getItem(e);if(t){const e=JSON.parse(t);this.isRunning=e.isRunning||!1,this.currentStatus=e.currentStatus||"等待开始",this.intervalInput=e.intervalInput||"10",this.loopMode=e.loopMode||!1,this.currentTaskId=e.currentTaskId||null,this.currentLogId=e.currentLogId||null,this.sentMessageCount=e.sentMessageCount||0,this.processedControlCount=e.processedControlCount||0,this.executionCount=e.executionCount||0,this.loopCount=e.loopCount||0,this.logMessages=e.logMessages||[],this.realtimeLogs=e.realtimeLogs||[],console.log("[GroupMessageConfig] 状态已恢复:",e)}}catch(e){console.error("[GroupMessageConfig] 恢复状态失败:",e)}},async startScript(){if(this.isRunning)return void this.$message.warning("脚本正在运行中，请等待完成");const e=parseInt(this.intervalInput)||10;if(isNaN(e)||e<=0)this.$message.error("请输入有效的发送间隔时间（大于0的数字）");else try{console.log("[GroupMessageConfig] 开始执行循环群发脚本");const t={sendInterval:e,executionMode:this.loopMode?"loop":"once",loopInterval:this.loopMode?60:void 0};this.$emit("execute-script",{functionType:"groupMessage",config:t,deviceId:this.deviceId}),this.addLog("=== 开始执行脚本 ==="),this.addLog(`发送间隔: ${e}秒`),this.addLog("执行模式: "+(this.loopMode?"循环执行":"单次执行")),console.log("[GroupMessageConfig] 脚本执行请求已发送")}catch(t){console.error("[GroupMessageConfig] 启动脚本失败:",t),this.$message.error("启动脚本失败: "+t.message),this.addLog("启动失败: "+t.message)}},async restartScript(){this.isRunning?this.$confirm("脚本正在运行中，是否要先终止当前脚本然后重新开始？","确认重新开始",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{this.addLog("正在终止当前脚本...");const e=await this.$http.post("/api/xiaohongshu/stop");e.data.success?(this.addLog("✅ 当前脚本已终止"),await new Promise(e=>setTimeout(e,1500)),this.handleScriptStopped(),this.performRestart()):this.$message.error("停止失败，无法重新开始")}catch(e){this.$message.error("停止失败: "+e.message)}}).catch(()=>{}):this.$confirm("确定要重新开始执行脚本吗？这将重置所有状态并从头开始执行。","确认再次开始",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.performRestart()}).catch(()=>{})},performRestart(){this.sentMessageCount=0,this.processedControlCount=0,this.executionCount=0,this.loopCount=0,this.updateCurrentStatus("重新开始"),this.logMessages=[],this.realtimeLogs=[],this.addLog("=== 重新开始脚本执行 ==="),this.addLog("所有状态已重置，从头开始执行脚本"),setTimeout(()=>{this.startScript()},500)},async stopScript(){this.isRunning?this.$confirm("确定要完全终止脚本吗？这将终止手机端正在执行的脚本并重置所有状态。","确认完全终止脚本",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{this.addLog("正在终止手机端脚本..."),this.updateCurrentStatus("正在终止..."),console.log("[GroupMessageConfig] 当前保存的logId:",this.currentLogId),console.log("[GroupMessageConfig] 当前保存的taskId:",this.currentTaskId);let e={};this.deviceId?(console.log("[GroupMessageConfig] 停止特定设备:",this.deviceId),e={deviceId:this.deviceId,taskId:this.currentTaskId||`xiaohongshu_groupMessage_${this.deviceId}`,logId:this.currentLogId||`xiaohongshu_groupMessage_${Date.now()}_${this.deviceId}`},console.log("[GroupMessageConfig] 停止数据:",e)):console.log("[GroupMessageConfig] 停止所有循环群发任务"),this.isRunning=!1,this.updateCurrentStatus("已停止"),this.addLog("✅ 正在停止脚本...");const t=await this.$http.post("/api/xiaohongshu/stop",e);t.data.success?(this.addLog("✅ 手机端脚本终止成功"),this.addLog("📱 所有设备的脚本执行已停止"),this.handleScriptStopped(),this.$message.success("脚本已完全终止并重置"),this.$emit("task-stopped",{taskId:this.currentTaskId,function:"groupMessage"}),this.$root.$emit("xiaohongshu-task-stopped",{functionType:"groupMessage",reason:"manual",deviceId:this.deviceId})):(this.$message.error("停止失败: "+t.data.message),this.addLog("❌ 停止失败: "+t.data.message))}catch(e){this.$message.error("停止失败: "+e.message),this.addLog("❌ 停止失败: "+e.message),console.error("[GroupMessageConfig] 停止脚本失败:",e),this.isRunning=!1,this.updateCurrentStatus("停止失败"),this.handleScriptStopped()}}).catch(()=>{}):this.$message.warning("脚本未在运行")},handleScriptStopped(){this.isRunning=!1,this.currentTaskId=null,this.sentMessageCount=0,this.processedControlCount=0,this.executionCount=0,this.loopCount=0,this.updateCurrentStatus("等待开始"),this.addLog("🔄 所有状态已重置"),this.addLog("✅ 可以重新点击开始按钮从头执行脚本"),this.saveComponentState()},updateConfig(){const e={sendInterval:parseInt(this.intervalInput)||10,executionMode:this.loopMode?"loop":"once",loopInterval:this.loopMode?60:void 0,autoSave:this.autoSave,selectedApp:this.selectedApp||""};this.$emit("input",e),this.$emit("update",e)},onAppSelectionChange(e){console.log("选择的小红书应用:",e),this.selectedApp=e,this.updateConfig()},onConfigChange(){this.$nextTick(()=>{this.updateConfig()})}}},y=b,k=(0,u.A)(y,_,I,!1,null,"1644552c",null),$=k.exports,x=function(){var e=this,t=e._self._c;return t("div",{staticClass:"article-comment-config"},[t("el-form",{attrs:{model:e.config,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"搜索关键词"}},[t("el-input",{attrs:{placeholder:"请输入搜索关键词（如：美食推荐）"},model:{value:e.config.searchKeyword,callback:function(t){e.$set(e.config,"searchKeyword",t)},expression:"config.searchKeyword"}}),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 系统将搜索包含此关键词的文章进行评论 ")]),t("div",{staticStyle:{"margin-top":"5px",color:"#67C23A","font-size":"12px"}},[e._v(" 当前值: "+e._s(e.config.searchKeyword)+" ")])],1),t("el-form-item",{attrs:{label:"评论文章数量"}},[t("el-input-number",{attrs:{min:1,max:50},model:{value:e.config.commentCount,callback:function(t){e.$set(e.config,"commentCount",t)},expression:"config.commentCount"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("要评论的文章数量")])],1),t("el-form-item",{attrs:{label:"小红书应用"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择要使用的小红书应用"},on:{change:e.onAppSelectionChange},model:{value:e.config.selectedApp,callback:function(t){e.$set(e.config,"selectedApp",t)},expression:"config.selectedApp"}},e._l(e.xiaohongshuApps,function(s){return t("el-option",{key:s.text,attrs:{label:s.text,value:s.text}},[t("span",{staticStyle:{float:"left"}},[e._v(e._s(s.text))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(" "+e._s("keyword"===s.method?"关键词":"正则")+" ")])])}),1),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 选择设备上要使用的小红书应用版本 ")])],1),t("el-form-item",{attrs:{label:"操作间隔"}},[t("el-input-number",{attrs:{min:3,max:60},model:{value:e.config.operationDelay,callback:function(t){e.$set(e.config,"operationDelay",t)},expression:"config.operationDelay"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("每次评论间隔时间（秒）")])],1),t("el-form-item",{attrs:{label:"调试信息"}},[t("div",{staticStyle:{background:"#f5f5f5",padding:"10px","border-radius":"4px","font-size":"12px"}},[t("div",[e._v("当前设备ID: "),t("strong",[e._v(e._s(e.deviceId||"未设置"))])]),t("div",[e._v("当前搜索关键词: "),t("strong",[e._v(e._s(e.config.searchKeyword))])]),t("div",[e._v("当前评论数量: "),t("strong",[e._v(e._s(e.config.commentCount))])]),t("div",[e._v("当前操作延迟: "),t("strong",[e._v(e._s(e.config.operationDelay))]),e._v("秒")]),t("div",[e._v("脚本运行状态: "),t("strong",[e._v(e._s(e.isScriptRunning?"执行中":"未运行"))])]),t("div",[e._v("脚本完成状态: "),t("strong",[e._v(e._s(e.isScriptCompleted?"已完成":"未完成"))])])]),t("el-button",{staticStyle:{"margin-top":"10px"},attrs:{size:"small"},on:{click:e.debugUpdateConfig}},[e._v(" 手动更新配置到父组件 ")])],1),t("el-alert",{attrs:{title:"使用提醒",type:"warning",closable:!1,"show-icon":""}},[t("div",[e._v(" • 请文明评论，遵守社区规范和法律法规"),t("br"),e._v(" • 建议设置合理的评论间隔，避免被识别为机器行为"),t("br"),e._v(" • 评论内容要与文章相关，避免无意义的刷屏"),t("br"),e._v(" • 过度评论可能导致账号被限制，请适度使用 ")])]),t("div",{staticClass:"realtime-status-section",staticStyle:{"margin-bottom":"20px"}},[t("el-divider",{attrs:{"content-position":"left"}},[t("span",{staticStyle:{color:"#409EFF","font-weight":"bold"}},[e._v("实时状态")])]),t("div",{staticClass:"status-grid",staticStyle:{display:"grid","grid-template-columns":"1fr 1fr",gap:"15px","margin-bottom":"15px"}},[t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("已评论文章：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#67C23A","font-weight":"bold"}},[e._v(e._s(e.commentedArticleCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("已处理步骤：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#E6A23C","font-weight":"bold"}},[e._v(e._s(e.processedStepCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("搜索尝试次数：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#F56C6C","font-weight":"bold"}},[e._v(e._s(e.searchAttemptCount))])])]),t("div",{staticClass:"current-status",staticStyle:{"margin-bottom":"15px"}},[t("span",{staticClass:"status-label"},[e._v("当前状态：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#409EFF","font-weight":"bold"}},[e._v(e._s(e.currentStatus||"等待开始"))])])],1),t("el-form-item",{attrs:{label:"脚本控制"}},[t("el-button",{attrs:{type:"primary",size:"small",disabled:e.isScriptRunning||!e.canExecute,loading:e.isScriptRunning},on:{click:e.startScript}},[e._v(" "+e._s(e.isScriptRunning?"脚本执行中...":"开始执行")+" ")]),t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"danger",size:"small",disabled:!e.isScriptRunning},on:{click:e.stopScript}},[e._v(" 停止脚本 ")]),e.isScriptRunning?t("span",{staticStyle:{"margin-left":"10px",color:"#67C23A","font-size":"12px"}},[e._v(" 脚本正在执行中... ")]):e.isScriptCompleted?t("span",{staticStyle:{"margin-left":"10px",color:"#409EFF","font-size":"12px"}},[e._v(" 脚本执行完成，1分钟后可重新执行 ")]):t("span",{staticStyle:{"margin-left":"10px",color:"#909399","font-size":"12px"}},[e._v(" 脚本未运行 ")])],1)],1)],1)},D=[],w={name:"ArticleCommentConfig",mixins:[c],props:{value:{type:Object,default:()=>({})},deviceId:{type:String,default:""}},data(){return{isScriptRunning:!1,isScriptCompleted:!1,currentLogId:null,currentTaskId:null,config:{searchKeyword:"美食推荐",commentCount:3,operationDelay:5,selectedApp:""},commentedArticleCount:0,processedStepCount:0,searchAttemptCount:0,currentStatus:"等待开始",socket:null}},watch:{value:{handler(e){e&&"object"===typeof e&&(this.config={...this.config,...e})},immediate:!0},config:{handler(e,t){t&&JSON.stringify(e)!==JSON.stringify(t)&&(console.log("配置发生变化，调用updateConfig"),console.log("新值:",e.searchKeyword,e.commentCount),this.$nextTick(()=>{this.updateConfig()}))},deep:!0},"config.searchKeyword"(e,t){e!==t&&(console.log("搜索关键词变化:",t,"->",e),this.updateConfig())},"config.commentCount"(e,t){e!==t&&(console.log("评论数量变化:",t,"->",e),this.updateConfig())},"config.operationDelay"(e,t){e!==t&&(console.log("操作延迟变化:",t,"->",e),this.updateConfig())}},mounted(){console.log("ArticleCommentConfig mounted, 发送初始配置:",this.config),setTimeout(()=>{this.updateConfig()},100),this.$root.$on("xiaohongshu-task-restored",this.handleTaskRestored),this.$root.$on("xiaohongshu-task-started",this.handleTaskStarted),this.$root.$on("xiaohongshu-task-stopped",this.handleTaskStopped),this.$root.$on("xiaohongshu-script-completed",this.handleScriptCompleted),this.$root.$on("xiaohongshu-task-stopped",this.handleForceReset),this.$root.$on("device-offline",e=>{e.deviceId===this.deviceId&&(console.log("[ArticleCommentConfig] 当前设备离线，重置状态"),this.handleDeviceOffline())}),this.restoreComponentState().then(()=>{console.log("[ArticleCommentConfig] 状态恢复完成，开始初始化Socket连接"),this.initializeSocket()}).catch(e=>{console.error("[ArticleCommentConfig] 状态恢复失败:",e),this.initializeSocket()})},beforeDestroy(){this.saveComponentState(),this.socket&&(this.socket.disconnect(),console.log("[ArticleCommentConfig] Socket连接已断开")),this.$root.$off("xiaohongshu-task-restored",this.handleTaskRestored),this.$root.$off("xiaohongshu-task-started",this.handleTaskStarted),this.$root.$off("xiaohongshu-task-stopped",this.handleTaskStopped),this.$root.$off("xiaohongshu-script-completed",this.handleScriptCompleted),this.$root.$off("device-offline")},computed:{canExecute(){return this.config.searchKeyword&&this.config.searchKeyword.trim().length>0&&this.config.commentCount>0}},methods:{updateConfig(){console.log("ArticleCommentConfig updateConfig 被调用:",this.config),this.$emit("input",this.config),this.$emit("update",this.config)},async startScript(){if(this.canExecute)try{console.log("[ArticleCommentConfig] 开始执行文章评论脚本"),this.$emit("execute-script",{functionType:"articleComment",config:this.config,deviceId:this.deviceId}),console.log("[ArticleCommentConfig] 脚本执行请求已发送")}catch(e){console.error("[ArticleCommentConfig] 启动脚本失败:",e),this.$message.error("启动脚本失败: "+e.message)}else this.$message.warning("请完善配置参数")},async stopScript(){try{console.log("[ArticleCommentConfig] 停止文章评论脚本执行");let e="/api/xiaohongshu/stop",t={};this.deviceId?(console.log("[ArticleCommentConfig] 停止特定设备:",this.deviceId),console.log("[ArticleCommentConfig] 使用保存的taskId:",this.currentTaskId),console.log("[ArticleCommentConfig] 使用保存的logId:",this.currentLogId),t={deviceId:this.deviceId,taskId:this.currentTaskId||`xiaohongshu_articleComment_${this.deviceId}`,logId:this.currentLogId||`xiaohongshu_articleComment_${Date.now()}_${this.deviceId}`}):console.log("[ArticleCommentConfig] 停止所有文章评论任务"),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.saveComponentState();const s=await this.$http.post(e,t);s.data.success?(this.$message.success("脚本停止成功"),this.currentLogId=null,this.currentTaskId=null,console.log("[ArticleCommentConfig] 已清空保存的logId和taskId"),this.$forceUpdate(),console.log("[ArticleCommentConfig] 组件状态已强制更新")):this.$message.error("停止脚本失败: "+s.data.message)}catch(e){console.error("[ArticleCommentConfig] 停止脚本请求失败:",e),this.$message.error("停止脚本请求失败"),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.saveComponentState()}},handleTaskStarted(e){console.log("[ArticleCommentConfig] 收到任务开始事件:",e),"articleComment"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[ArticleCommentConfig] 文章评论任务开始，更新状态"),this.isScriptRunning=!0,this.isScriptCompleted=!1,this.currentTaskId=e.taskId,this.currentLogId=e.logId,this.saveComponentState())},handleTaskStopped(e){console.log("[ArticleCommentConfig] 收到任务停止事件:",e),"articleComment"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[ArticleCommentConfig] 文章评论任务停止，更新状态"),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.saveComponentState())},handleScriptCompleted(e){console.log("[ArticleCommentConfig] 收到脚本完成事件:",e);const t="string"===typeof e?e:e.functionType;"articleComment"!==t||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[ArticleCommentConfig] 文章评论脚本完成"),this.isScriptRunning=!1,this.isScriptCompleted=!0,setTimeout(()=>{this.isScriptCompleted=!1,this.saveComponentState()},6e4),this.saveComponentState())},async handleTaskRestored(e){console.log("[ArticleCommentConfig] 收到任务恢复事件:",e),"articleComment"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[ArticleCommentConfig] 恢复文章评论任务状态"),this.isScriptRunning=e.isRunning||!1,this.isScriptCompleted=e.isCompleted||!1,this.currentTaskId=e.taskId,this.currentLogId=e.logId,this.saveComponentState())},async restoreComponentStateOld(){try{const e=`articleComment_state_${this.deviceId||"default"}`,t=localStorage.getItem(e);if(t){const e=JSON.parse(t);this.isScriptRunning=e.isScriptRunning||!1,this.isScriptCompleted=e.isScriptCompleted||!1,this.currentTaskId=e.currentTaskId||null,this.currentLogId=e.currentLogId||null,e.config&&(this.config={...this.config,...e.config}),console.log("[ArticleCommentConfig] 状态已恢复:",e)}}catch(e){console.error("[ArticleCommentConfig] 恢复状态失败:",e)}},debugUpdateConfig(){console.log("=== 手动触发配置更新 ==="),console.log("当前config:",this.config),this.updateConfig()},handleTaskRestored(e){"articleComment"===e.functionType&&(console.log("[ArticleCommentConfig] 恢复任务状态:",e.state),this.isScriptRunning=e.state.isScriptRunning,this.isScriptCompleted=e.state.isScriptCompleted,e.state.config&&Object.keys(e.state.config).length>0&&(this.config={...this.config,...e.state.config}),console.log("[ArticleCommentConfig] 状态已恢复:",{isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,config:this.config}))},handleForceReset(e){console.log("[ArticleCommentConfig] 收到强制重置事件:",e),"all"!==e.functionType&&"articleComment"!==e.functionType||(console.log("[ArticleCommentConfig] 执行强制重置"),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.currentTaskId=null,this.currentLogId=null,this.saveComponentState(),console.log("[ArticleCommentConfig] 强制重置完成"))},handleTaskStarted(e){console.log("[ArticleCommentConfig] 收到任务开始事件:",e),console.log("[ArticleCommentConfig] 当前组件deviceId:",this.deviceId),console.log("[ArticleCommentConfig] 事件deviceId:",e.deviceId),console.log("[ArticleCommentConfig] 功能类型匹配:","articleComment"===e.functionType),console.log("[ArticleCommentConfig] 设备ID匹配:",!this.deviceId||e.deviceId===this.deviceId),"articleComment"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId?console.log("[ArticleCommentConfig] 任务开始事件不匹配，忽略"):(console.log("[ArticleCommentConfig] 任务开始 (设备ID匹配):",e),this.isScriptRunning=!0,this.isScriptCompleted=!1,e.logId&&(this.currentLogId=e.logId,console.log("[ArticleCommentConfig] 保存logId:",this.currentLogId)),e.taskId&&(this.currentTaskId=e.taskId,console.log("[ArticleCommentConfig] 保存taskId:",this.currentTaskId)),this.resetRealtimeStatus(),this.$forceUpdate(),console.log("[ArticleCommentConfig] 组件状态已强制更新"),this.saveComponentState())},handleTaskStopped(e){console.log("[ArticleCommentConfig] 收到任务停止事件:",e),console.log("[ArticleCommentConfig] 当前组件deviceId:",this.deviceId),console.log("[ArticleCommentConfig] 事件deviceId:",e.deviceId),console.log("[ArticleCommentConfig] 功能类型匹配:","articleComment"===e.functionType),console.log("[ArticleCommentConfig] 设备ID匹配:",!this.deviceId||e.deviceId===this.deviceId);const t=e.reason||"manual",s="articleComment"===e.functionType&&("batch_stop"===t||!this.deviceId||e.deviceId===this.deviceId);s?(console.log(`[ArticleCommentConfig] 文章评论任务停止，原因: ${t}`),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.currentLogId=null,this.currentTaskId=null,console.log("[ArticleCommentConfig] 已清空logId和taskId"),this.resetRealtimeStatus(),this.$forceUpdate(),console.log("[ArticleCommentConfig] 组件状态已强制更新"),this.saveComponentState(),"batch_stop"===t&&this.$message.info("文章评论功能已被批量停止")):console.log("[ArticleCommentConfig] 任务停止事件不匹配，忽略")},handleScriptCompleted(e){"articleComment"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[ArticleCommentConfig] 脚本完成 (设备ID匹配):",e),this.isScriptRunning=!1,this.isScriptCompleted=!0,this.currentLogId=null,this.currentTaskId=null,console.log("[ArticleCommentConfig] 脚本完成，已清空logId和taskId"),this.saveComponentState(),setTimeout(()=>{this.isScriptCompleted=!1,this.saveComponentState()},6e4))},async saveComponentState(){const{saveComponentState:e}=await Promise.resolve().then(s.bind(s,5596)),t={isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,config:this.config,currentLogId:this.currentLogId,currentTaskId:this.currentTaskId,realtimeData:{commentedArticleCount:this.commentedArticleCount,processedStepCount:this.processedStepCount,searchAttemptCount:this.searchAttemptCount,currentStatus:this.currentStatus}};await e(this,"articleComment",t),console.log("[ArticleCommentConfig] 组件状态已保存，currentTaskId:",this.currentTaskId)},async restoreComponentState(){try{const{restoreComponentState:e}=await Promise.resolve().then(s.bind(s,5596)),t=await e(this,"articleComment");t&&Object.keys(t).length>0&&(console.log("[ArticleCommentConfig] 恢复组件状态:",t),this.isScriptRunning=t.isScriptRunning||!1,this.isScriptCompleted=t.isScriptCompleted||!1,this.currentLogId=t.currentLogId||null,this.currentTaskId=t.currentTaskId||null,t.config&&Object.keys(t.config).length>0&&(this.config={...this.config,...t.config}),t.realtimeData&&(this.commentedArticleCount=t.realtimeData.commentedArticleCount||0,this.processedStepCount=t.realtimeData.processedStepCount||0,this.searchAttemptCount=t.realtimeData.searchAttemptCount||0,this.currentStatus=t.realtimeData.currentStatus||"等待开始",console.log("[ArticleCommentConfig] 实时状态已恢复:",t.realtimeData)),console.log("[ArticleCommentConfig] 组件状态已恢复:",{isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,currentLogId:this.currentLogId,currentTaskId:this.currentTaskId,config:this.config,realtimeData:{commentedArticleCount:this.commentedArticleCount,processedStepCount:this.processedStepCount,searchAttemptCount:this.searchAttemptCount,currentStatus:this.currentStatus}}))}catch(e){console.error("[ArticleCommentConfig] 恢复组件状态失败:",e)}},handleRealtimeStatus(e){console.log("🔄 [ArticleCommentConfig] 收到实时状态数据:",e),console.log("📋 [ArticleCommentConfig] 当前组件taskId:",this.currentTaskId),console.log("📋 [ArticleCommentConfig] 数据中的taskId:",e.taskId),console.log("🔍 [ArticleCommentConfig] taskId匹配:",this.currentTaskId&&e.taskId===this.currentTaskId),this.currentTaskId&&e.taskId===this.currentTaskId?(console.log("✅ [ArticleCommentConfig] taskId匹配，更新实时状态:",e),void 0!==e.commentedArticleCount&&(this.commentedArticleCount=e.commentedArticleCount,console.log("📊 [ArticleCommentConfig] 更新已评论文章数:",this.commentedArticleCount)),void 0!==e.processedStepCount&&(this.processedStepCount=e.processedStepCount,console.log("📊 [ArticleCommentConfig] 更新已处理步骤数:",this.processedStepCount)),void 0!==e.searchAttemptCount&&(this.searchAttemptCount=e.searchAttemptCount,console.log("📊 [ArticleCommentConfig] 更新搜索尝试次数:",this.searchAttemptCount)),e.currentStatus&&(this.currentStatus=e.currentStatus,console.log("📊 [ArticleCommentConfig] 更新当前状态:",this.currentStatus)),console.log("✅ [ArticleCommentConfig] 实时状态已更新:",{commentedArticleCount:this.commentedArticleCount,processedStepCount:this.processedStepCount,searchAttemptCount:this.searchAttemptCount,currentStatus:this.currentStatus}),this.$forceUpdate(),console.log("🔄 [ArticleCommentConfig] 已强制更新视图"),this.saveComponentState()):console.log("❌ [ArticleCommentConfig] taskId不匹配或currentTaskId为空，忽略实时状态更新")},resetRealtimeStatus(){this.commentedArticleCount=0,this.processedStepCount=0,this.searchAttemptCount=0,this.currentStatus="等待开始",console.log("[ArticleCommentConfig] 实时状态已重置")},async initializeSocket(){try{const{getWebSocketManager:e}=await Promise.resolve().then(s.bind(s,6006));this.wsManager=e(),console.log("🔧 [ArticleCommentConfig] 开始初始化WebSocket连接"),await this.wsManager.init(),console.log("✅ [ArticleCommentConfig] WebSocket连接成功")}catch(e){console.error("❌ [ArticleCommentConfig] WebSocket连接错误:",e)}this.socket.onAny((e,t)=>{(e.includes("xiaohongshu")||e.includes("test"))&&console.log(`🔍 [ArticleCommentConfig] 收到WebSocket事件: ${e}`,t)}),this.socket.on("xiaohongshu_execution_completed",e=>{console.log("[ArticleCommentConfig] 收到WebSocket脚本执行完成事件:",e),e.deviceId!==this.deviceId&&this.deviceId||(console.log("[ArticleCommentConfig] 脚本执行完成，更新状态"),this.isScriptRunning=!1,this.isScriptCompleted="success"===e.status,"success"===e.status&&setTimeout(()=>{this.isScriptCompleted=!1},6e4),this.saveComponentState())}),this.socket.on("xiaohongshu_realtime_status",e=>{console.log("🎯 [ArticleCommentConfig] 收到WebSocket实时状态事件:",e),this.handleRealtimeStatus(e)}),this.socket.on("test_realtime_broadcast",e=>{console.log("🧪 [ArticleCommentConfig] 收到测试广播:",e)}),console.log("✅ [ArticleCommentConfig] Socket初始化完成")},handleDeviceOffline(){console.log("[ArticleCommentConfig] 处理设备离线，重置状态"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"articleComment",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}}),this.resetRealtimeStatus(),this.currentTaskId=null,this.currentLogId=null,this.saveComponentState(),console.log("[ArticleCommentConfig] 设备离线处理完成")}}},T=w,V=(0,u.A)(T,x,D,!1,null,"407afc1a",null),A=V.exports,F=function(){var e=this,t=e._self._c;return t("div",{staticClass:"uid-message-config"},[t("el-form",{attrs:{model:e.config,"label-width":"120px"}},[t("el-form-item",[t("el-alert",{attrs:{title:"手动输入UID私信",type:"info",closable:!1,"show-icon":""}},[t("div",{attrs:{slot:"description"},slot:"description"},[t("p",[e._v("此功能支持手动输入UID列表进行私信")]),t("p",[e._v("请在下方输入框中输入要私信的UID，每行一个")])])])],1),t("el-form-item",{attrs:{label:"UID列表"}},[t("el-input",{attrs:{type:"textarea",rows:6,placeholder:"请输入UID，每行一个，例如：&#10;95018838961&#10;123456789&#10;987654321"},on:{blur:e.onUidListInput,change:e.onUidListInput},model:{value:e.config.uidList,callback:function(t){e.$set(e.config,"uidList",t)},expression:"config.uidList"}}),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 每行输入一个UID，支持批量输入 ")])],1),t("el-form-item",{attrs:{label:"小红书应用"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择要使用的小红书应用"},on:{change:e.onAppSelectionChange},model:{value:e.config.selectedApp,callback:function(t){e.$set(e.config,"selectedApp",t)},expression:"config.selectedApp"}},e._l(e.xiaohongshuApps,function(s){return t("el-option",{key:s.text,attrs:{label:s.text,value:s.text}},[t("span",{staticStyle:{float:"left"}},[e._v(e._s(s.text))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(" "+e._s("keyword"===s.method?"关键词":"正则")+" ")])])}),1),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 选择设备上要使用的小红书应用版本 ")])],1),t("el-form-item",{attrs:{label:"私信内容"}},[t("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入要发送的私信内容",maxlength:"500","show-word-limit":""},on:{blur:e.onInputChange,change:e.onInputChange},model:{value:e.config.message,callback:function(t){e.$set(e.config,"message",t)},expression:"config.message"}}),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 建议私信内容简洁友好，避免过于商业化的语言 ")])],1),t("el-form-item",{attrs:{label:"执行参数"}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"操作间隔(秒)"}},[t("el-input-number",{attrs:{min:3,max:30},on:{change:e.onInputChange},model:{value:e.config.delay,callback:function(t){e.$set(e.config,"delay",t)},expression:"config.delay"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"最大执行数量"}},[t("el-input-number",{attrs:{min:1,max:100},on:{change:e.onInputChange},model:{value:e.config.maxCount,callback:function(t){e.$set(e.config,"maxCount",t)},expression:"config.maxCount"}})],1)],1)],1)],1),t("el-form-item",{attrs:{label:"高级选项"}},[t("el-checkbox-group",{on:{change:e.onInputChange},model:{value:e.config.advancedOptions,callback:function(t){e.$set(e.config,"advancedOptions",t)},expression:"config.advancedOptions"}},[t("el-checkbox",{attrs:{label:"enableDetailLog"}},[e._v("启用详细日志")]),t("el-checkbox",{attrs:{label:"skipUsedUids"}},[e._v("跳过已使用的UID")]),t("el-checkbox",{attrs:{label:"autoMarkUsed"}},[e._v("自动标记UID为已使用")])],1),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" • 启用详细日志：记录详细的执行过程"),t("br"),e._v(" • 跳过已使用的UID：避免重复私信同一用户"),t("br"),e._v(" • 自动标记UID为已使用：执行后自动标记UID状态 ")])],1),t("div",{staticClass:"realtime-status-section",staticStyle:{"margin-bottom":"20px"}},[t("el-divider",{attrs:{"content-position":"left"}},[t("span",{staticStyle:{color:"#409EFF","font-weight":"bold"}},[e._v("实时状态")])]),t("div",{staticClass:"status-grid",staticStyle:{display:"grid","grid-template-columns":"1fr 1fr 1fr",gap:"15px","margin-bottom":"15px"}},[t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("已处理UID：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#67C23A","font-weight":"bold"}},[e._v(e._s(e.processedUidCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("成功私信：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#67C23A","font-weight":"bold"}},[e._v(e._s(e.successCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("失败私信：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#F56C6C","font-weight":"bold"}},[e._v(e._s(e.failedCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("已处理步骤：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#E6A23C","font-weight":"bold"}},[e._v(e._s(e.processedStepCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("搜索尝试次数：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#F56C6C","font-weight":"bold"}},[e._v(e._s(e.searchAttemptCount))])])]),t("div",{staticClass:"current-status",staticStyle:{"margin-bottom":"15px"}},[t("span",{staticClass:"status-label"},[e._v("当前状态：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#409EFF","font-weight":"bold"}},[e._v(e._s(e.currentStatus||"等待开始"))])])],1),t("el-form-item",{attrs:{label:"脚本控制"}},[t("el-button",{attrs:{type:"danger",size:"small",disabled:!e.isScriptRunning},on:{click:e.stopScript}},[e._v(" 停止脚本 ")]),e.isScriptRunning?t("span",{staticStyle:{"margin-left":"10px",color:"#67C23A","font-size":"12px"}},[e._v(" 脚本正在执行中... ")]):e.isScriptCompleted?t("span",{staticStyle:{"margin-left":"10px",color:"#409EFF","font-size":"12px"}},[e._v(" 脚本执行完成，1分钟后可重新执行 ")]):t("span",{staticStyle:{"margin-left":"10px",color:"#909399","font-size":"12px"}},[e._v(" 脚本未运行 ")])],1)],1)],1)},P=[],M={name:"UidMessageConfig",mixins:[c],props:{value:{type:Object,default:()=>({})},deviceId:{type:String,required:!0}},data(){return{config:{inputMode:"manual",uidList:"95018838961\n123456789\n987654321",message:"你好，很喜欢你的内容！",delay:15,advancedOptions:["enableDetailLog"],selectedApp:""},currentLogId:null,currentTaskId:null,isInternalUpdate:!1,processedUidCount:0,successCount:0,failedCount:0,processedStepCount:0,searchAttemptCount:0,currentStatus:"等待开始",socket:null}},computed:{isScriptRunning(){const e=this.$store.getters["xiaohongshu/getFunctionState"]("uidMessage");return!!e&&e.isScriptRunning},isScriptCompleted(){const e=this.$store.getters["xiaohongshu/getFunctionState"]("uidMessage");return!!e&&e.isScriptCompleted},canExecute(){let e=!1;if("string"===typeof this.config.uidList){const t=this.config.uidList.split("\n").map(e=>e.trim()).filter(e=>e.length>0);e=t.length>0}else Array.isArray(this.config.uidList)&&(e=this.config.uidList.length>0);const t=this.config.message&&this.config.message.trim().length>0,s=this.config.delay&&this.config.delay>=3,i=this.config.maxCount&&this.config.maxCount>0;return console.log("[UID私信验证] 验证结果:",{hasValidUids:e,hasValidMessage:t,hasValidDelay:s,hasValidMaxCount:i,inputMode:this.config.inputMode,uidList:this.config.uidList}),e&&t&&s&&i}},watch:{value:{handler(e){if(!this.isInternalUpdate&&e&&"object"===typeof e){const t={...e};t.uidList&&(Array.isArray(t.uidList)?t.uidList=t.uidList.join("\n"):"string"!==typeof t.uidList&&(t.uidList=String(t.uidList))),this.config={...this.config,...t}}},immediate:!0,deep:!0}},mounted(){if(this.loadConfig(),this.value&&Object.keys(this.value).length>0){const e={...this.value};e.uidList&&(Array.isArray(e.uidList)?e.uidList=e.uidList.join("\n"):"string"!==typeof e.uidList&&(e.uidList=String(e.uidList))),this.config={...this.config,...e}}this.restoreComponentState().then(()=>{console.log("[UidMessageConfig] 状态恢复完成，开始初始化Socket连接"),this.initializeSocket()}).catch(e=>{console.error("[UidMessageConfig] 状态恢复失败:",e),this.initializeSocket()}),this.$emit("input-mode-change",this.config.inputMode),this.$root.$on("xiaohongshu-task-restored",this.handleTaskRestored),this.$root.$on("xiaohongshu-task-started",e=>{console.log("[UidMessageConfig] 收到任务开始事件:",e),"uidMessage"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId?console.log("[UidMessageConfig] 任务开始事件不匹配，忽略"):(console.log("[UidMessageConfig] UID私信任务开始，更新Vuex状态"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"uidMessage",stateData:{isScriptRunning:!0,isScriptCompleted:!1,config:this.config}}),e.logId&&(this.currentLogId=e.logId,console.log("[UidMessageConfig] 保存logId:",this.currentLogId)),e.taskId&&(this.currentTaskId=e.taskId,console.log("[UidMessageConfig] 保存taskId:",this.currentTaskId)),console.log("[UidMessageConfig] 状态已更新:",{isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,currentLogId:this.currentLogId,currentTaskId:this.currentTaskId}))}),this.$root.$on("xiaohongshu-task-stopped",e=>{console.log("[UidMessageConfig] 收到任务停止事件:",e);const t="string"===typeof e?e:e.functionType,s=e.reason||"manual",i="uidMessage"===t&&("batch_stop"===s||!this.deviceId||e.deviceId===this.deviceId);i&&(console.log(`[UidMessageConfig] UID私信任务停止，原因: ${s}`),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"uidMessage",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}}),this.currentLogId=null,this.currentTaskId=null,console.log("[UidMessageConfig] 已清空保存的logId和taskId"),"batch_stop"===s&&this.$message.info("手动输入UID私信功能已被批量停止"))}),this.$root.$on("xiaohongshu-script-completed",e=>{console.log("[UidMessageConfig] 收到脚本完成事件:",e),"uidMessage"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[UidMessageConfig] UID私信脚本完成，状态:",e.status),"stopped"===e.status?(console.log("[UidMessageConfig] UID私信脚本被手动停止"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"uidMessage",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}})):"success"===e.status?(console.log("[UidMessageConfig] UID私信脚本成功完成"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"uidMessage",stateData:{isScriptRunning:!1,isScriptCompleted:!0,config:this.config}}),setTimeout(()=>{this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"uidMessage",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}})},6e4)):"failed"===e.status&&(console.log("[UidMessageConfig] UID私信脚本执行失败"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"uidMessage",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}})),this.currentLogId=null,this.currentTaskId=null,console.log("[UidMessageConfig] 已清空保存的logId和taskId"))}),this.$root.$on("device-offline",e=>{e.deviceId===this.deviceId&&(console.log("[UidMessageConfig] 当前设备离线，重置状态"),this.handleDeviceOffline())})},beforeDestroy(){this.saveComponentState(),this.socket&&(this.socket.disconnect(),console.log("[UidMessageConfig] Socket连接已断开")),this.$root.$off("xiaohongshu-task-started"),this.$root.$off("xiaohongshu-task-stopped"),this.$root.$off("xiaohongshu-script-completed"),this.$root.$off("xiaohongshu-task-restored",this.handleTaskRestored),this.$root.$off("device-offline")},methods:{onInputChange(){console.log("=== UID私信配置变更开始 ==="),console.log("当前配置:",JSON.stringify(this.config,null,2));const e={...this.config,inputMode:"manual"};if("string"===typeof this.config.uidList){const t=this.config.uidList.split("\n").map(e=>e.trim()).filter(e=>e.length>0);if(console.log("手动输入模式 - 原始文本:",this.config.uidList),console.log("手动输入模式 - 转换后数组:",t),0===t.length)return console.warn("警告：UID列表为空，请输入至少一个UID"),void this.$message.warning("请输入至少一个UID");e.uidList=t}if(!e.message||0===e.message.trim().length)return console.warn("警告：私信内容为空"),void this.$message.warning("请输入私信内容");this.config.advancedOptions&&(e.enableDetailLog=this.config.advancedOptions.includes("enableDetailLog"),e.skipUsedUids=this.config.advancedOptions.includes("skipUsedUids"),e.autoMarkUsed=this.config.advancedOptions.includes("autoMarkUsed")),console.log("最终处理后的配置:",JSON.stringify(e,null,2)),console.log("发送update事件..."),this.isInternalUpdate=!0,this.$emit("input",e),this.$emit("update",e),this.saveConfig(),this.$nextTick(()=>{this.isInternalUpdate=!1}),console.log("=== UID私信配置变更结束 ===")},onUidListInput(){console.log("UID列表输入变化:",this.config.uidList),this.onInputChange()},stopScript(){console.log("[UidMessageConfig] 停止脚本"),this.$root.$emit("xiaohongshu-stop-script",{functionType:"uidMessage",deviceId:this.deviceId,logId:this.currentLogId,taskId:this.currentTaskId}),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"uidMessage",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}}),this.$message.success("已发送停止脚本命令")},saveConfig(){try{localStorage.setItem("uidMessageConfig",JSON.stringify(this.config))}catch(e){console.error("保存配置失败:",e)}},loadConfig(){try{const e=localStorage.getItem("uidMessageConfig");if(e){const t=JSON.parse(e);t.uidList&&(Array.isArray(t.uidList)?t.uidList=t.uidList.join("\n"):"string"!==typeof t.uidList&&(t.uidList=String(t.uidList))),this.config={...this.config,...t}}}catch(e){console.error("加载配置失败:",e)}},async saveComponentState(){const{saveComponentState:e}=await Promise.resolve().then(s.bind(s,5596)),t={config:this.config,currentLogId:this.currentLogId,currentTaskId:this.currentTaskId,isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,realtimeData:{processedUidCount:this.processedUidCount,successCount:this.successCount,failedCount:this.failedCount,processedStepCount:this.processedStepCount,searchAttemptCount:this.searchAttemptCount,currentStatus:this.currentStatus}};await e(this,"uidMessage",t),console.log("[UidMessageConfig] 组件状态已保存，currentTaskId:",this.currentTaskId)},async restoreComponentState(){try{const{restoreComponentState:e}=await Promise.resolve().then(s.bind(s,5596)),t=await e(this,"uidMessage");t&&Object.keys(t).length>0?(console.log("[UidMessageConfig] 恢复组件状态:",t),t.config&&Object.keys(t.config).length>0&&(this.config={...this.config,...t.config}),this.currentLogId=t.currentLogId||null,this.currentTaskId=t.currentTaskId||null,this.isScriptRunning=t.isScriptRunning||!1,this.isScriptCompleted=t.isScriptCompleted||!1,t.realtimeData&&(this.processedUidCount=t.realtimeData.processedUidCount||0,this.successCount=t.realtimeData.successCount||0,this.failedCount=t.realtimeData.failedCount||0,this.processedStepCount=t.realtimeData.processedStepCount||0,this.searchAttemptCount=t.realtimeData.searchAttemptCount||0,this.currentStatus=t.realtimeData.currentStatus||"等待开始",console.log("[UidMessageConfig] 实时状态已恢复:",t.realtimeData)),console.log("[UidMessageConfig] 组件状态已恢复，currentTaskId:",this.currentTaskId)):(console.log("[UidMessageConfig] 没有找到保存的状态，使用默认值"),this.currentLogId=null,this.currentTaskId=null,this.isScriptRunning=!1,this.isScriptCompleted=!1)}catch(e){console.error("[UidMessageConfig] 恢复组件状态失败:",e)}},handleTaskRestored(e){console.log("[UidMessageConfig] 收到任务恢复事件:",e),"uidMessage"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId||(this.currentLogId=e.logId,this.currentTaskId=e.taskId,this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"uidMessage",stateData:{isScriptRunning:!0,isScriptCompleted:!1,config:this.config}}))},handleRealtimeStatus(e){console.log("🔄 [UidMessageConfig] 收到实时状态数据:",e),console.log("📋 [UidMessageConfig] 当前组件taskId:",this.currentTaskId),console.log("📋 [UidMessageConfig] 数据中的taskId:",e.taskId),console.log("🔍 [UidMessageConfig] taskId匹配:",this.currentTaskId&&e.taskId===this.currentTaskId),this.currentTaskId&&e.taskId===this.currentTaskId?(console.log("✅ [UidMessageConfig] taskId匹配，更新实时状态:",e),void 0!==e.processedUidCount&&(this.processedUidCount=e.processedUidCount,console.log("📊 [UidMessageConfig] 更新已处理UID数:",this.processedUidCount)),void 0!==e.successCount&&(this.successCount=e.successCount,console.log("📊 [UidMessageConfig] 更新成功私信数:",this.successCount)),void 0!==e.failedCount&&(this.failedCount=e.failedCount,console.log("📊 [UidMessageConfig] 更新失败私信数:",this.failedCount)),void 0!==e.processedStepCount&&(this.processedStepCount=e.processedStepCount,console.log("📊 [UidMessageConfig] 更新已处理步骤数:",this.processedStepCount)),void 0!==e.searchAttemptCount&&(this.searchAttemptCount=e.searchAttemptCount,console.log("📊 [UidMessageConfig] 更新搜索尝试次数:",this.searchAttemptCount)),e.currentStatus&&(this.currentStatus=e.currentStatus,console.log("📊 [UidMessageConfig] 更新当前状态:",this.currentStatus)),console.log("✅ [UidMessageConfig] 实时状态已更新:",{processedUidCount:this.processedUidCount,successCount:this.successCount,failedCount:this.failedCount,processedStepCount:this.processedStepCount,searchAttemptCount:this.searchAttemptCount,currentStatus:this.currentStatus}),this.$forceUpdate(),console.log("🔄 [UidMessageConfig] 已强制更新视图")):console.log("❌ [UidMessageConfig] taskId不匹配或currentTaskId为空，忽略实时状态更新")},resetRealtimeStatus(){this.processedUidCount=0,this.successCount=0,this.failedCount=0,this.processedStepCount=0,this.searchAttemptCount=0,this.currentStatus="等待开始",console.log("[UidMessageConfig] 实时状态已重置")},async initializeSocket(){try{console.log("🔧 [UidMessageConfig] 使用统一WebSocket管理器");const{getWebSocketManager:e}=await Promise.resolve().then(s.bind(s,6006));this.wsManager=e(),await this.wsManager.init(),this.socket=this.wsManager.socket,console.log("✅ [UidMessageConfig] 已连接到统一WebSocket管理器")}catch(e){console.error("❌ [UidMessageConfig] WebSocket连接失败:",e)}this.socket.on("disconnect",()=>{console.log("❌ [UidMessageConfig] Socket连接断开")}),this.socket.on("xiaohongshu_realtime_status",e=>{console.log("🎯 [UidMessageConfig] 收到WebSocket实时状态事件:",e),this.handleRealtimeStatus(e)}),console.log("[UidMessageConfig] Socket初始化完成")},handleDeviceOffline(){console.log("[UidMessageConfig] 处理设备离线，重置状态"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"uidMessage",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}}),this.currentTaskId=null,this.currentLogId=null,this.saveComponentState(),console.log("[UidMessageConfig] 设备离线处理完成")}}},U=M,L=(0,u.A)(U,F,P,!1,null,"006ae891",null),R=L.exports,z=function(){var e=this,t=e._self._c;return t("div",{staticClass:"uid-file-message-config"},[t("el-form",{attrs:{model:e.config,"label-width":"120px"}},[t("el-form-item",[t("el-alert",{attrs:{title:"文件上传UID私信",type:"info",closable:!1,"show-icon":""}},[t("div",{attrs:{slot:"description"},slot:"description"},[t("p",[e._v("此功能使用上传的UID文件进行批量私信")]),t("p",[e._v('请先在上方的"UID文件管理"区域上传UID文件并配置分配参数')]),t("p",[e._v("系统会根据配置自动分配UID给各个设备执行")])])])],1),t("el-form-item",{attrs:{label:"小红书应用"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择要使用的小红书应用"},on:{change:e.onAppSelectionChange},model:{value:e.config.selectedApp,callback:function(t){e.$set(e.config,"selectedApp",t)},expression:"config.selectedApp"}},e._l(e.xiaohongshuApps,function(s){return t("el-option",{key:s.text,attrs:{label:s.text,value:s.text}},[t("span",{staticStyle:{float:"left"}},[e._v(e._s(s.text))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(" "+e._s("keyword"===s.method?"关键词":"正则")+" ")])])}),1),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 选择设备上要使用的小红书应用版本 ")])],1),t("el-form-item",{attrs:{label:"私信内容"}},[t("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入要发送的私信内容",maxlength:"500","show-word-limit":""},on:{blur:e.onInputChange,change:e.onInputChange},model:{value:e.config.message,callback:function(t){e.$set(e.config,"message",t)},expression:"config.message"}}),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 建议私信内容简洁友好，避免过于商业化的语言 ")])],1),t("el-form-item",{attrs:{label:"执行间隔"}},[t("el-input-number",{staticStyle:{width:"150px"},attrs:{min:5,max:60},on:{change:e.onInputChange},model:{value:e.config.delay,callback:function(t){e.$set(e.config,"delay",t)},expression:"config.delay"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("秒（建议5-30秒）")])],1),t("el-form-item",{attrs:{label:"高级选项"}},[t("el-checkbox-group",{on:{change:e.onInputChange},model:{value:e.config.advancedOptions,callback:function(t){e.$set(e.config,"advancedOptions",t)},expression:"config.advancedOptions"}},[t("el-checkbox",{attrs:{label:"enableDetailLog"}},[e._v("启用详细日志")]),t("el-checkbox",{attrs:{label:"skipUsedUids"}},[e._v("跳过已使用的UID")]),t("el-checkbox",{attrs:{label:"autoMarkUsed"}},[e._v("自动标记UID为已使用")])],1)],1),t("div",{staticClass:"realtime-status-section",staticStyle:{"margin-bottom":"20px"}},[t("el-divider",{attrs:{"content-position":"left"}},[t("span",{staticStyle:{color:"#409EFF","font-weight":"bold"}},[e._v("实时状态")])]),t("div",{staticClass:"status-grid",staticStyle:{display:"grid","grid-template-columns":"1fr 1fr 1fr",gap:"15px","margin-bottom":"15px"}},[t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("已处理UID：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#67C23A","font-weight":"bold"}},[e._v(e._s(e.processedUidCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("成功私信：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#67C23A","font-weight":"bold"}},[e._v(e._s(e.successCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("失败私信：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#F56C6C","font-weight":"bold"}},[e._v(e._s(e.failedCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("已处理步骤：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#E6A23C","font-weight":"bold"}},[e._v(e._s(e.processedStepCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("搜索尝试次数：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#F56C6C","font-weight":"bold"}},[e._v(e._s(e.searchAttemptCount))])])]),t("div",{staticClass:"current-status",staticStyle:{"margin-bottom":"15px"}},[t("span",{staticClass:"status-label"},[e._v("当前状态：")]),t("span",{staticClass:"status-value",staticStyle:{color:"#409EFF","font-weight":"bold"}},[e._v(e._s(e.currentStatus||"等待开始"))])])],1),t("el-form-item",{attrs:{label:"脚本控制"}},[t("el-button",{attrs:{type:"primary",size:"small",disabled:e.isScriptRunning||!e.canExecute,loading:e.isScriptRunning},on:{click:e.startScript}},[e._v(" "+e._s(e.isScriptRunning?"脚本执行中...":"开始执行")+" ")]),t("el-button",{attrs:{type:"danger",size:"small",disabled:!e.isScriptRunning},on:{click:e.stopScript}},[e._v(" 停止脚本 ")]),e.isScriptRunning?t("span",{staticStyle:{"margin-left":"10px",color:"#67C23A","font-size":"12px"}},[e._v(" 脚本正在执行中... ")]):e.isScriptCompleted?t("span",{staticStyle:{"margin-left":"10px",color:"#409EFF","font-size":"12px"}},[e._v(" 脚本执行完成，1分钟后可重新执行 ")]):t("span",{staticStyle:{"margin-left":"10px",color:"#909399","font-size":"12px"}},[e._v(" 脚本未运行 ")])],1)],1)],1)},O=[],G=s(5353),E={name:"UidFileMessageConfig",mixins:[c],props:{value:{type:Object,default:()=>({})},deviceId:{type:String,default:""},selectedDevices:{type:Array,default:()=>[]},onlineDevices:{type:Array,default:()=>[]}},data(){return{config:{inputMode:"file",message:"",delay:15,advancedOptions:["autoMarkUsed"],selectedFileId:null,totalUidCount:10,uidsPerDevice:5,selectedApp:""},currentLogId:null,currentTaskId:null,isInternalUpdate:!1,processedUidCount:0,successCount:0,failedCount:0,processedStepCount:0,searchAttemptCount:0,currentStatus:"等待开始",socket:null}},computed:{...(0,G.aH)({token:e=>e.auth.token}),isScriptRunning(){const e=this.$store.getters["xiaohongshu/getFunctionState"]("uidFileMessage");return!!e&&e.isScriptRunning},isScriptCompleted(){const e=this.$store.getters["xiaohongshu/getFunctionState"]("uidFileMessage");return!!e&&e.isScriptCompleted},canExecute(){return this.config.message&&this.config.message.trim().length>0}},watch:{value:{handler(e){e&&Object.keys(e).length>0&&(this.config={...this.config,...e})},deep:!0}},mounted(){console.log("[UidFileMessageConfig] 组件已挂载"),console.log("[UidFileMessageConfig] 挂载时的初始状态:",{currentTaskId:this.currentTaskId,currentLogId:this.currentLogId,deviceId:this.deviceId}),this.loadConfig(),this.value&&Object.keys(this.value).length>0&&(this.config={...this.config,...this.value}),console.log("[UidFileMessageConfig] 开始调用状态恢复方法...");try{this.restoreComponentState().then(()=>{console.log("[UidFileMessageConfig] 状态恢复完成，开始初始化Socket连接"),console.log("[UidFileMessageConfig] 恢复后的状态:",{currentTaskId:this.currentTaskId,currentLogId:this.currentLogId,deviceId:this.deviceId}),this.initializeSocket()}).catch(e=>{console.error("[UidFileMessageConfig] 状态恢复失败:",e),console.error("[UidFileMessageConfig] 错误堆栈:",e.stack),this.initializeSocket()})}catch(e){console.error("[UidFileMessageConfig] 调用状态恢复方法时发生同步错误:",e),console.error("[UidFileMessageConfig] 同步错误堆栈:",e.stack),this.initializeSocket()}this.onInputChange(),this.$root.$on("xiaohongshu-task-started",e=>{console.log("[UidFileMessageConfig] 收到任务开始事件:",e),"uidFileMessage"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId||(e.logId&&(this.currentLogId=e.logId,console.log("[UidFileMessageConfig] 保存logId:",this.currentLogId)),e.taskId&&(this.currentTaskId=e.taskId,console.log("[UidFileMessageConfig] 保存taskId:",this.currentTaskId)),this.resetRealtimeStatus(),console.log("[UidFileMessageConfig] 状态已更新:",{isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,currentLogId:this.currentLogId,currentTaskId:this.currentTaskId}))}),this.$root.$on("xiaohongshu-task-stopped",e=>{console.log("[UidFileMessageConfig] 收到任务停止事件:",e);const t=e.reason||"manual",s="uidFileMessage"===e.functionType&&("batch_stop"===t||!this.deviceId||e.deviceId===this.deviceId);s&&(console.log(`[UidFileMessageConfig] 文件UID私信任务停止，原因: ${t}`),this.currentLogId=null,this.currentTaskId=null,this.resetRealtimeStatus(),console.log("[UidFileMessageConfig] 已清空保存的logId和taskId"),"batch_stop"===t&&this.$message.info("文件上传UID私信功能已被批量停止"))}),this.$root.$on("xiaohongshu-task-completed",e=>{console.log("[UidFileMessageConfig] 收到任务完成事件:",e),"uidFileMessage"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId||(this.currentLogId=null,this.currentTaskId=null,console.log("[UidFileMessageConfig] 已清空保存的logId和taskId"))}),this.$root.$on("device-offline",e=>{e.deviceId===this.deviceId&&(console.log("[UidFileMessageConfig] 当前设备离线，重置状态"),this.handleDeviceOffline())})},beforeDestroy(){this.saveConfig(),this.saveComponentState(),this.socket&&(this.socket.disconnect(),console.log("[UidFileMessageConfig] Socket连接已断开")),this.$root.$off("xiaohongshu-task-started"),this.$root.$off("xiaohongshu-task-stopped"),this.$root.$off("xiaohongshu-task-completed"),this.$root.$off("device-offline")},methods:{onInputChange(){console.log("=== 文件UID私信配置变更开始 ==="),console.log("当前配置:",JSON.stringify(this.config,null,2));const e={...this.config,inputMode:"file"};if(!e.message||0===e.message.trim().length)return console.warn("警告：私信内容为空"),void this.$message.warning("请输入私信内容");this.config.advancedOptions&&(e.enableDetailLog=this.config.advancedOptions.includes("enableDetailLog"),e.skipUsedUids=this.config.advancedOptions.includes("skipUsedUids"),e.autoMarkUsed=this.config.advancedOptions.includes("autoMarkUsed")),e.uidList=[],console.log("最终处理后的配置:",JSON.stringify(e,null,2)),this.$emit("input",e),this.$emit("update",e),this.saveConfig(),console.log("=== 文件UID私信配置变更结束 ===")},async startScript(){if(this.canExecute)try{console.log("[UidFileMessageConfig] 开始执行文件UID私信脚本"),this.$emit("execute-script",{functionType:"uidFileMessage",config:this.config,deviceId:this.deviceId}),console.log("[UidFileMessageConfig] 脚本执行请求已发送")}catch(e){console.error("[UidFileMessageConfig] 执行脚本失败:",e),this.$message.error("执行脚本失败: "+e.message)}else this.$message.warning("请输入私信内容")},stopScript(){console.log("[UidFileMessageConfig] 停止脚本"),this.$root.$emit("xiaohongshu-stop-script",{functionType:"uidFileMessage",deviceId:this.deviceId,logId:this.currentLogId,taskId:this.currentTaskId}),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"uidFileMessage",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}}),this.$message.success("已发送停止脚本命令")},executeScript(){this.canExecute?(console.log("[UidFileMessageConfig] 准备执行脚本"),this.$emit("execute-script",{functionType:"uidFileMessage",config:this.config,deviceId:this.deviceId})):this.$message.warning("请填写完整的配置信息")},saveConfig(){const e=`uidFileMessageConfig_${this.deviceId}`;try{localStorage.setItem(e,JSON.stringify(this.config))}catch(t){console.warn("保存配置失败:",t)}},loadConfig(){const e=`uidFileMessageConfig_${this.deviceId}`;try{const t=localStorage.getItem(e);if(t){const e=JSON.parse(t);this.config={...this.config,...e},console.log("加载已保存的配置:",this.config)}}catch(t){console.warn("加载配置失败:",t)}},handleRealtimeStatus(e){console.log("🔄 [UidFileMessageConfig] 收到实时状态数据:",e),console.log("📋 [UidFileMessageConfig] 当前组件taskId:",this.currentTaskId),console.log("📋 [UidFileMessageConfig] 数据中的taskId:",e.taskId),console.log("🔍 [UidFileMessageConfig] taskId匹配:",this.currentTaskId&&e.taskId===this.currentTaskId),console.log("🔍 [UidFileMessageConfig] 组件状态调试:",{currentTaskId:this.currentTaskId,currentLogId:this.currentLogId,isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,deviceId:this.deviceId}),!this.currentTaskId&&e.taskId&&e.taskId.includes("uidFileMessage")&&(console.log("🔄 [UidFileMessageConfig] 检测到taskId为空，尝试从WebSocket数据恢复taskId:",e.taskId),this.currentTaskId=e.taskId,console.log("✅ [UidFileMessageConfig] 已从WebSocket数据恢复taskId:",this.currentTaskId),this.saveComponentState()),this.currentTaskId&&e.taskId===this.currentTaskId?(console.log("✅ [UidFileMessageConfig] taskId匹配，更新实时状态:",e),void 0!==e.processedUidCount&&(this.processedUidCount=e.processedUidCount,console.log("📊 [UidFileMessageConfig] 更新已处理UID数:",this.processedUidCount)),void 0!==e.successCount&&(this.successCount=e.successCount,console.log("📊 [UidFileMessageConfig] 更新成功私信数:",this.successCount)),void 0!==e.failedCount&&(this.failedCount=e.failedCount,console.log("📊 [UidFileMessageConfig] 更新失败私信数:",this.failedCount)),void 0!==e.processedStepCount&&(this.processedStepCount=e.processedStepCount,console.log("📊 [UidFileMessageConfig] 更新已处理步骤数:",this.processedStepCount)),void 0!==e.searchAttemptCount&&(this.searchAttemptCount=e.searchAttemptCount,console.log("📊 [UidFileMessageConfig] 更新搜索尝试次数:",this.searchAttemptCount)),e.currentStatus&&(this.currentStatus=e.currentStatus,console.log("📊 [UidFileMessageConfig] 更新当前状态:",this.currentStatus)),console.log("✅ [UidFileMessageConfig] 实时状态已更新:",{processedUidCount:this.processedUidCount,successCount:this.successCount,failedCount:this.failedCount,processedStepCount:this.processedStepCount,searchAttemptCount:this.searchAttemptCount,currentStatus:this.currentStatus}),this.$forceUpdate(),console.log("🔄 [UidFileMessageConfig] 已强制更新视图")):console.log("❌ [UidFileMessageConfig] taskId不匹配或currentTaskId为空，忽略实时状态更新")},resetRealtimeStatus(){this.processedUidCount=0,this.successCount=0,this.failedCount=0,this.processedStepCount=0,this.searchAttemptCount=0,this.currentStatus="等待开始",console.log("[UidFileMessageConfig] 实时状态已重置")},async initializeSocket(){try{console.log("🔧 [UidFileMessageConfig] 使用统一WebSocket管理器");const{getWebSocketManager:e}=await Promise.resolve().then(s.bind(s,6006));this.wsManager=e(),await this.wsManager.init(),this.socket=this.wsManager.socket,console.log("✅ [UidFileMessageConfig] 已连接到统一WebSocket管理器")}catch(e){console.error("❌ [UidFileMessageConfig] WebSocket连接失败:",e)}this.socket.on("disconnect",()=>{console.log("❌ [UidFileMessageConfig] Socket连接断开")}),this.socket.on("xiaohongshu_realtime_status",e=>{console.log("🎯 [UidFileMessageConfig] 收到WebSocket实时状态事件:",e),this.handleRealtimeStatus(e)}),console.log("[UidFileMessageConfig] Socket初始化完成")},async saveComponentState(){const{saveComponentState:e}=await Promise.resolve().then(s.bind(s,5596)),t={config:this.config,currentLogId:this.currentLogId,currentTaskId:this.currentTaskId,isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,realtimeData:{processedUidCount:this.processedUidCount,successCount:this.successCount,failedCount:this.failedCount,processedStepCount:this.processedStepCount,searchAttemptCount:this.searchAttemptCount,currentStatus:this.currentStatus}};await e(this,"uidFileMessage",t),console.log("[UidFileMessageConfig] 组件状态已保存，currentTaskId:",this.currentTaskId)},async restoreComponentState(){console.log("🚀 [UidFileMessageConfig] restoreComponentState方法被调用");try{console.log("[UidFileMessageConfig] 开始恢复组件状态..."),console.log("[UidFileMessageConfig] 当前deviceId:",this.deviceId);const e=this.$store.getters["xiaohongshu/getFunctionState"]("uidFileMessage");console.log("[UidFileMessageConfig] Vuex中的状态:",e);const t=`uidFileMessage_backup_${this.deviceId||"default"}`,i=localStorage.getItem(t);console.log("[UidFileMessageConfig] localStorage备份键:",t),console.log("[UidFileMessageConfig] localStorage备份数据:",i);const{restoreComponentState:o}=await Promise.resolve().then(s.bind(s,5596)),n=await o(this,"uidFileMessage");console.log("[UidFileMessageConfig] 状态管理工具返回的状态:",n),n&&Object.keys(n).length>0?(console.log("[UidFileMessageConfig] 恢复组件状态:",n),n.config&&Object.keys(n.config).length>0&&(this.config={...this.config,...n.config}),this.currentLogId=n.currentLogId||null,this.currentTaskId=n.currentTaskId||null,this.isScriptRunning=n.isScriptRunning||!1,this.isScriptCompleted=n.isScriptCompleted||!1,n.realtimeData&&(this.processedUidCount=n.realtimeData.processedUidCount||0,this.successCount=n.realtimeData.successCount||0,this.failedCount=n.realtimeData.failedCount||0,this.processedStepCount=n.realtimeData.processedStepCount||0,this.searchAttemptCount=n.realtimeData.searchAttemptCount||0,this.currentStatus=n.realtimeData.currentStatus||"等待开始",console.log("[UidFileMessageConfig] 实时状态已恢复:",n.realtimeData)),console.log("[UidFileMessageConfig] 组件状态已恢复，currentTaskId:",this.currentTaskId)):(console.log("[UidFileMessageConfig] 没有找到保存的状态，使用默认值"),this.currentLogId=null,this.currentTaskId=null,this.isScriptRunning=!1,this.isScriptCompleted=!1)}catch(e){console.error("[UidFileMessageConfig] 恢复组件状态失败:",e)}},handleDeviceOffline(){console.log("[UidFileMessageConfig] 处理设备离线，重置状态"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"uidFileMessage",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}}),this.resetRealtimeStatus(),this.currentTaskId=null,this.currentLogId=null,this.saveComponentState(),console.log("[UidFileMessageConfig] 设备离线处理完成")}}},B=E,X=(0,u.A)(B,z,O,!1,null,"dcc6c5a6",null),N=X.exports,j=function(){var e=this,t=e._self._c;return t("div",{staticClass:"uid-file-manager"},[t("el-card",{staticClass:"file-manager-card"},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("UID文件管理")]),t("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text",loading:e.loading},on:{click:e.refreshFiles}},[e._v(" 刷新 ")])],1),t("div",{staticClass:"upload-section"},[t("el-upload",{staticClass:"upload-demo",attrs:{action:e.uploadUrl,headers:e.uploadHeaders,"on-success":e.handleUploadSuccess,"on-error":e.handleUploadError,"before-upload":e.beforeUpload,accept:".txt,.csv","show-file-list":!1,disabled:e.uploading}},[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-upload",loading:e.uploading}},[e._v(" "+e._s(e.uploading?"上传中...":"上传UID文件")+" ")]),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t("div",[e._v("支持.txt和.csv格式，每行一个UID，文件大小不超过10MB")]),t("div",{staticStyle:{color:"#E6A23C","margin-top":"5px"}},[e._v(" ⚠️ 不支持Excel文件(.xlsx/.xls)，请先转换为CSV格式 ")])])],1)],1),e.showAllocationConfig?t("div",{staticClass:"allocation-config-section"},[t("h4",[e._v("UID分配配置")]),t("el-form",{attrs:{model:e.allocationConfig,"label-width":"120px",size:"small"}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"选择UID文件"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择UID文件"},on:{change:e.onFileSelected},model:{value:e.allocationConfig.selectedFileId,callback:function(t){e.$set(e.allocationConfig,"selectedFileId",t)},expression:"allocationConfig.selectedFileId"}},e._l(e.availableFiles,function(s){return t("el-option",{key:s.id,attrs:{label:`${s.original_name} (${s.unused_count}/${s.total_uid_count})`,value:s.id}},[t("span",{staticStyle:{float:"left"}},[e._v(e._s(s.original_name))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(" "+e._s(s.unused_count)+"/"+e._s(s.total_uid_count)+" ")])])}),1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"每个设备UID数量"}},[t("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:e.selectedFileInfo?e.selectedFileInfo.unused_count:100},on:{change:e.onUidAllocationChange},model:{value:e.allocationConfig.uidsPerDevice,callback:function(t){e.$set(e.allocationConfig,"uidsPerDevice",t)},expression:"allocationConfig.uidsPerDevice"}})],1)],1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"总分配数量"}},[t("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:e.selectedFileInfo?e.selectedFileInfo.unused_count:100},on:{change:e.onUidAllocationChange},model:{value:e.allocationConfig.totalUidCount,callback:function(t){e.$set(e.allocationConfig,"totalUidCount",t)},expression:"allocationConfig.totalUidCount"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"设备数量"}},[t("el-input",{staticStyle:{width:"100%"},attrs:{value:e.selectedDeviceCount||0,readonly:""}}),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" "+e._s(e.selectedDeviceCount>0?`已选择 ${e.selectedDeviceCount} 个设备`:"请先选择设备")+" ")])],1)],1)],1)],1),e.selectedFileInfo?t("div",{staticClass:"selected-file-info"},[t("el-alert",{attrs:{title:`已选择文件: ${e.selectedFileInfo.original_name}`,type:"info",closable:!1,"show-icon":""}},[t("div",{attrs:{slot:"description"},slot:"description"},[t("p",[e._v("总UID数: "+e._s(e.selectedFileInfo.total_uid_count))]),t("p",[e._v("可用UID数: "+e._s(e.selectedFileInfo.unused_count))]),t("p",[e._v("已使用UID数: "+e._s(e.selectedFileInfo.used_count))]),t("p",[e._v("上传时间: "+e._s(e.formatTime(e.selectedFileInfo.upload_time)))])])])],1):e._e(),e.uidAllocationPreview.length>0?t("div",{staticClass:"allocation-preview"},[t("h4",[e._v("UID分配预览")]),t("el-table",{attrs:{data:e.uidAllocationPreview,size:"small"}},[t("el-table-column",{attrs:{label:"设备",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("div",[t("div",{staticStyle:{"font-weight":"bold"}},[e._v("设备 "+e._s(s.row.deviceIndex+1))]),t("div",{staticStyle:{"font-size":"12px",color:"#909399"}},[e._v(e._s(s.row.deviceId))])])]}}],null,!1,3500171715)}),t("el-table-column",{attrs:{prop:"uidCount",label:"UID数量",width:"100",align:"center"}}),t("el-table-column",{attrs:{prop:"uidPreview",label:"UID预览"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tooltip",{attrs:{content:s.row.uids.join(", "),placement:"top"}},[t("span",[e._v(e._s(s.row.uids.slice(0,3).join(", "))+e._s(s.row.uids.length>3?"...":""))])])]}}],null,!1,2828898136)})],1)],1):0===e.selectedDeviceCount?t("div",{staticClass:"no-devices-tip"},[t("el-alert",{attrs:{title:"请先选择设备",type:"warning",closable:!1,"show-icon":""}},[t("div",{attrs:{slot:"description"},slot:"description"},[e._v(" 请在左侧设备信息区域选择要执行UID私信的设备，系统将根据选择的设备数量自动分配UID。 ")])])],1):e._e()],1):e._e(),e.files.length>0?t("div",{staticClass:"file-list-section"},[t("h4",[e._v("已上传的UID文件")]),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.files,size:"small"}},[t("el-table-column",{attrs:{prop:"original_name",label:"文件名",width:"200"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tooltip",{attrs:{content:s.row.original_name,placement:"top"}},[t("span",[e._v(e._s(s.row.original_name.length>20?s.row.original_name.substring(0,20)+"...":s.row.original_name))])])]}}],null,!1,3633700671)}),t("el-table-column",{attrs:{prop:"total_uid_count",label:"总UID数",width:"100",align:"center"}}),t("el-table-column",{attrs:{label:"使用情况",width:"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tooltip",{attrs:{content:`已使用: ${s.row.used_count}, 未使用: ${s.row.unused_count}`,placement:"top"}},[t("el-progress",{attrs:{percentage:s.row.total_uid_count>0?Math.round(s.row.used_count/s.row.total_uid_count*100):0,color:e.getProgressColor(s.row.used_count,s.row.total_uid_count),"stroke-width":8}})],1)]}}],null,!1,581755558)}),t("el-table-column",{attrs:{prop:"upload_time",label:"上传时间",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatTime(t.row.upload_time))+" ")]}}],null,!1,3564387491)}),t("el-table-column",{attrs:{label:"操作",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.viewFileDetails(s.row)}}},[e._v("查看详情")]),t("el-button",{attrs:{size:"mini",type:"warning"},on:{click:function(t){return e.resetFileStatus(s.row)}}},[e._v("重置状态")]),t("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteFile(s.row)}}},[e._v("删除")])]}}],null,!1,3124156079)})],1)],1):e.loading?e._e():t("div",{staticClass:"empty-state"},[t("el-empty",{attrs:{description:"暂无UID文件，请上传文件"}})],1)]),t("el-dialog",{attrs:{title:"UID文件详情",visible:e.detailDialogVisible,width:"80%","before-close":e.closeDetailDialog},on:{"update:visible":function(t){e.detailDialogVisible=t}}},[e.currentFile?t("div",[t("el-descriptions",{attrs:{column:3,border:""}},[t("el-descriptions-item",{attrs:{label:"文件名"}},[e._v(e._s(e.currentFile.original_name))]),t("el-descriptions-item",{attrs:{label:"总UID数"}},[e._v(e._s(e.currentFile.total_uid_count))]),t("el-descriptions-item",{attrs:{label:"已使用"}},[e._v(e._s(e.currentFile.used_count))]),t("el-descriptions-item",{attrs:{label:"未使用"}},[e._v(e._s(e.currentFile.unused_count))]),t("el-descriptions-item",{attrs:{label:"上传时间"}},[e._v(e._s(e.formatTime(e.currentFile.upload_time)))]),t("el-descriptions-item",{attrs:{label:"文件大小"}},[e._v(e._s(e.formatFileSize(e.currentFile.file_size)))])],1),t("div",{staticStyle:{"margin-top":"20px"}},[t("div",{staticClass:"uid-list-header"},[t("h4",[e._v("UID列表")]),t("el-radio-group",{attrs:{size:"small"},on:{change:e.loadUidList},model:{value:e.uidFilter,callback:function(t){e.uidFilter=t},expression:"uidFilter"}},[t("el-radio-button",{attrs:{label:"all"}},[e._v("全部")]),t("el-radio-button",{attrs:{label:"unused"}},[e._v("未使用")]),t("el-radio-button",{attrs:{label:"used"}},[e._v("已使用")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.uidLoading,expression:"uidLoading"}],attrs:{data:e.uidList,size:"small","max-height":"400"}},[t("el-table-column",{attrs:{prop:"uid",label:"UID",width:"150"}}),t("el-table-column",{attrs:{prop:"is_used",label:"使用状态",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:s.row.is_used?"danger":"success",size:"small"}},[e._v(" "+e._s(s.row.is_used?"已使用":"未使用")+" ")])]}}],null,!1,1526430724)}),t("el-table-column",{attrs:{prop:"used_time",label:"使用时间",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.used_time?e.formatTime(t.row.used_time):"-")+" ")]}}],null,!1,3077658850)}),t("el-table-column",{attrs:{prop:"used_device_name",label:"使用设备",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.used_device_name||"-")+" ")]}}],null,!1,356250888)}),t("el-table-column",{attrs:{label:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.is_used?t("el-button",{attrs:{size:"mini",type:"warning"},on:{click:function(t){return e.resetUidStatus(s.row)}}},[e._v(" 重置 ")]):e._e()]}}],null,!1,2752002481)})],1),e.uidPagination.total>0?t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"current-page":e.uidPagination.page,"page-sizes":[20,50,100],"page-size":e.uidPagination.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.uidPagination.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1):e._e()],1)],1):e._e(),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.closeDetailDialog}},[e._v("关闭")]),e.currentFile?t("el-button",{attrs:{type:"warning"},on:{click:e.resetAllUidStatus}},[e._v("重置所有状态")]):e._e()],1)])],1)},W=[],J={name:"UidFileManager",props:{showAllocationConfig:{type:Boolean,default:!1},selectedDevices:{type:Array,default:()=>[]},selectedDeviceCount:{type:Number,default:0}},data(){return{files:[],loading:!1,uploading:!1,detailDialogVisible:!1,currentFile:null,uidList:[],uidLoading:!1,uidFilter:"all",uidPagination:{page:1,limit:50,total:0,totalPages:0},showAllocationConfig:!1,availableFiles:[],selectedFileInfo:null,uidAllocationPreview:[],allocationConfig:{selectedFileId:null,uidsPerDevice:5,totalUidCount:10},refreshTimer:null}},computed:{...(0,G.aH)({token:e=>e.auth.token}),uploadUrl(){return"/api/xiaohongshu/upload-uid-file"},uploadHeaders(){return{Authorization:`Bearer ${this.token}`}}},watch:{selectedDeviceCount:{handler(e,t){e!==t&&(console.log(`设备数量变化: ${t} -> ${e}`),this.updateUidAllocation())},immediate:!0},selectedDevices:{handler(e,t){console.log("选中设备变化:",e),this.updateUidAllocation()},deep:!0}},mounted(){this.loadFiles(),this.loadAvailableFiles(),this.refreshTimer=setInterval(()=>{this.loadFiles(),this.loadAvailableFiles(),this.detailDialogVisible&&this.currentFile&&this.loadUidList()},6e3)},beforeDestroy(){this.refreshTimer&&(clearInterval(this.refreshTimer),this.refreshTimer=null)},methods:{async loadFiles(){this.loading=!0;try{const e=await this.$http.get("/api/xiaohongshu/uid-files",{headers:{Authorization:`Bearer ${this.token}`}});e.data.success?this.files=e.data.data:this.$message.error("加载文件列表失败")}catch(e){console.error("加载文件列表失败:",e),this.$message.error("加载文件列表失败")}finally{this.loading=!1}},async refreshFiles(){await this.loadFiles(),await this.loadAvailableFiles()},async loadAvailableFiles(){try{const e=await this.$http.get("/api/xiaohongshu/uid-files",{headers:{Authorization:`Bearer ${this.token}`}});e.data.success?this.availableFiles=e.data.data.filter(e=>e.unused_count>0):this.$message.error("加载UID文件列表失败")}catch(e){console.error("加载UID文件列表失败:",e),this.$message.error("加载UID文件列表失败")}},onFileSelected(e){if(!e)return this.selectedFileInfo=null,this.uidAllocationPreview=[],void this.emitAllocationConfig();this.selectedFileInfo=this.availableFiles.find(t=>t.id===e),this.updateUidAllocation(),this.emitAllocationConfig()},onUidAllocationChange(){this.updateUidAllocation(),this.emitAllocationConfig()},updateUidAllocation(){if(!this.selectedFileInfo||!this.allocationConfig.uidsPerDevice||!this.allocationConfig.totalUidCount)return void(this.uidAllocationPreview=[]);const e=this.selectedDeviceCount;if(0===e)return void(this.uidAllocationPreview=[]);const t=Math.ceil(this.allocationConfig.totalUidCount/e),s=Math.min(this.allocationConfig.uidsPerDevice,t);this.allocationConfig.uidsPerDevice=s,this.uidAllocationPreview=[];let i=this.allocationConfig.totalUidCount;for(let o=0;o<e&&i>0;o++){const e=Math.min(s,i),t=this.generateMockUids(e);this.uidAllocationPreview.push({deviceIndex:o,deviceId:this.selectedDevices[o]||`device_${o+1}`,uidCount:e,uids:t}),i-=e}},generateMockUids(e){const t=[];for(let s=0;s<e;s++)t.push(`uid_${Date.now()}_${s}`);return t},emitAllocationConfig(){const e={selectedFileId:this.allocationConfig.selectedFileId,uidsPerDevice:this.allocationConfig.uidsPerDevice,totalUidCount:this.allocationConfig.totalUidCount,selectedDeviceCount:this.selectedDeviceCount};console.log("发出UID分配配置:",e),this.$emit("allocation-config-change",e)},beforeUpload(e){const t=e.name.toLowerCase().split(".").pop(),s=["txt","csv"];if(!s.includes(t))return this.$message.error("只支持 .txt 和 .csv 格式的文件!"),!1;const i=["text/plain","text/csv","application/csv","text/comma-separated-values"];if(e.type&&!i.includes(e.type)&&(e.type.includes("spreadsheet")||e.type.includes("excel")||e.type.includes("ms-excel")||e.type.includes("officedocument")))return this.$message.error("检测到Excel文件格式。请将Excel文件另存为CSV格式，或直接使用文本文件。"),!1;const o=e.size/1024/1024<10;return o?(this.uploading=!0,!0):(this.$message.error("文件大小不能超过 10MB!"),!1)},handleUploadSuccess(e,t){this.uploading=!1,e.success?(this.$message.success("UID文件上传成功"),this.loadFiles(),this.loadAvailableFiles(),this.$emit("file-uploaded",e.data)):this.$message.error(e.message||"上传失败")},handleUploadError(e,t){this.uploading=!1,console.error("上传失败:",e);let s="文件上传失败";if(e&&"string"===typeof e)try{const t=JSON.parse(e);t.message&&(s=t.message)}catch(i){s=e}else e&&e.message?s=e.message:e&&e.response&&e.response.data&&e.response.data.message&&(s=e.response.data.message);this.$message.error(s)},async deleteFile(e){try{await this.$confirm("确定要删除这个UID文件吗？删除后无法恢复。","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await this.$http.delete(`/api/xiaohongshu/uid-files/${e.id}`,{headers:{Authorization:`Bearer ${this.token}`}});t.data.success?(this.$message.success("文件删除成功"),this.loadFiles()):this.$message.error(t.data.message||"删除失败")}catch(t){"cancel"!==t&&(console.error("删除文件失败:",t),this.$message.error("删除文件失败"))}},async resetFileStatus(e){try{await this.$confirm("确定要重置这个文件的所有UID状态吗？","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await this.$http.post(`/api/xiaohongshu/uid-files/${e.id}/reset-status`,{},{headers:{Authorization:`Bearer ${this.token}`}});t.data.success?(this.$message.success("UID状态重置成功"),this.loadFiles(),this.detailDialogVisible&&this.loadUidList()):this.$message.error(t.data.message||"重置失败")}catch(t){"cancel"!==t&&(console.error("重置状态失败:",t),this.$message.error("重置状态失败"))}},viewFileDetails(e){this.currentFile=e,this.detailDialogVisible=!0,this.uidFilter="all",this.uidPagination.page=1,this.loadUidList()},closeDetailDialog(){this.detailDialogVisible=!1,this.currentFile=null,this.uidList=[]},async loadUidList(){if(this.currentFile){this.uidLoading=!0;try{const e={page:this.uidPagination.page,limit:this.uidPagination.limit};"all"!==this.uidFilter&&(e.status=this.uidFilter);const t=await this.$http.get(`/api/xiaohongshu/uid-files/${this.currentFile.id}/uids`,{params:e,headers:{Authorization:`Bearer ${this.token}`}});t.data.success?(this.uidList=t.data.data.uids,this.uidPagination=t.data.data.pagination):this.$message.error("加载UID列表失败")}catch(e){console.error("加载UID列表失败:",e),this.$message.error("加载UID列表失败")}finally{this.uidLoading=!1}}},async resetUidStatus(e){try{await this.$confirm("确定要重置这个UID的使用状态吗？","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await this.$http.post(`/api/xiaohongshu/uid-files/${this.currentFile.id}/reset-status`,{uidIds:[e.id]},{headers:{Authorization:`Bearer ${this.token}`}});t.data.success?(this.$message.success("UID状态重置成功"),this.loadUidList(),this.loadFiles()):this.$message.error(t.data.message||"重置失败")}catch(t){"cancel"!==t&&(console.error("重置UID状态失败:",t),this.$message.error("重置UID状态失败"))}},async resetAllUidStatus(){try{await this.$confirm("确定要重置这个文件的所有UID使用状态吗？","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await this.$http.post(`/api/xiaohongshu/uid-files/${this.currentFile.id}/reset-status`,{},{headers:{Authorization:`Bearer ${this.token}`}});e.data.success?(this.$message.success("所有UID状态重置成功"),this.loadUidList(),this.loadFiles()):this.$message.error(e.data.message||"重置失败")}catch(e){"cancel"!==e&&(console.error("重置所有UID状态失败:",e),this.$message.error("重置所有UID状态失败"))}},handleSizeChange(e){this.uidPagination.limit=e,this.loadUidList()},handleCurrentChange(e){this.uidPagination.page=e,this.loadUidList()},formatTime(e){if(!e)return"-";const t=new Date(e);return t.toLocaleString("zh-CN")},formatFileSize(e){if(0===e)return"0 B";const t=1024,s=["B","KB","MB","GB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]},getProgressColor(e,t){const s=t>0?e/t*100:0;return s<30?"#67C23A":s<70?"#E6A23C":"#F56C6C"}}},K=J,H=(0,u.A)(K,j,W,!1,null,"03df158f",null),q=H.exports,Q=function(){var e=this,t=e._self._c;return t("div",{staticClass:"video-file-manager"},[t("el-card",[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("视频文件管理")]),t("div",{staticClass:"header-actions"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-view",size:"small"},on:{click:e.showVideoListDialog}},[e._v(" 查看视频列表 ")]),t("el-button",{attrs:{type:"success",icon:"el-icon-share",size:"small"},on:{click:e.showVideoAssignDialog}},[e._v(" 视频分配 ")])],1)]),t("div",{staticClass:"upload-section"},[t("div",{staticClass:"upload-buttons"},[t("el-upload",{staticClass:"upload-demo",attrs:{action:e.uploadUrl,headers:e.uploadHeaders,"on-success":e.handleUploadSuccess,"on-error":e.handleUploadError,"before-upload":e.beforeUpload,accept:"video/*","show-file-list":!1,disabled:e.uploading,multiple:"",name:"videos"}},[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-upload",loading:e.uploading}},[e._v(" "+e._s(e.uploading?"上传中...":"选择视频文件")+" ")])],1),t("div",{staticClass:"folder-upload"},[t("input",{ref:"folderInput",staticStyle:{display:"none"},attrs:{type:"file",webkitdirectory:"",directory:"",multiple:"",accept:"video/*",disabled:e.uploading},on:{change:e.handleFolderSelect}}),t("el-button",{attrs:{size:"small",type:"success",icon:"el-icon-folder-add",disabled:e.uploading},on:{click:e.selectFolder}},[e._v(" 选择视频文件夹 ")])],1)],1),t("div",{staticClass:"upload-tips"},[t("div",{staticClass:"tip-item"},[t("i",{staticClass:"el-icon-document"}),t("span",[e._v("支持 MP4、AVI、MOV、WMV、FLV、MKV 等格式，单个文件不超过2GB")])]),t("div",{staticClass:"tip-item"},[t("i",{staticClass:"el-icon-folder"}),t("span",[e._v("选择文件夹可批量上传文件夹内所有视频文件，最多1000个文件")])]),t("div",{staticClass:"tip-item warning"},[t("i",{staticClass:"el-icon-warning"}),t("span",[e._v("系统会自动检测重复文件并跳过")])])]),e.selectedFolder?t("div",{staticClass:"folder-preview"},[t("div",{staticClass:"folder-info"},[t("h4",[t("i",{staticClass:"el-icon-folder-opened"}),e._v(" 已选择文件夹: "+e._s(e.selectedFolder.name)+" ")]),t("p",[e._v("找到 "+e._s(e.selectedFolder.videoFiles.length)+" 个视频文件")])]),e.selectedFolder.videoFiles.length>0?t("div",{staticClass:"file-list"},[t("div",{staticClass:"file-list-header"},[t("span",[e._v("视频文件列表:")]),t("el-button",{attrs:{size:"mini",type:"primary",disabled:e.uploading},on:{click:e.uploadFolderVideos}},[e._v(" 上传这些文件 ")])],1),t("div",{staticClass:"file-items"},[e._l(e.selectedFolder.videoFiles.slice(0,10),function(s,i){return t("div",{key:i,staticClass:"file-item"},[t("i",{staticClass:"el-icon-video-camera"}),t("span",{staticClass:"file-name"},[e._v(e._s(s.name))]),t("span",{staticClass:"file-size"},[e._v(e._s(e.formatFileSize(s.size)))])])}),e.selectedFolder.videoFiles.length>10?t("div",{staticClass:"more-files"},[e._v(" 还有 "+e._s(e.selectedFolder.videoFiles.length-10)+" 个文件... ")]):e._e()],2)]):t("div",{staticClass:"no-videos"},[t("i",{staticClass:"el-icon-warning"}),t("span",[e._v("该文件夹中没有找到视频文件")])])]):e._e(),e.uploading?t("div",{staticClass:"upload-progress"},[t("el-progress",{attrs:{percentage:e.uploadProgress,status:e.uploadStatus}}),t("p",[e._v(e._s(e.uploadMessage))]),e.uploadDetails.length>0?t("div",{staticClass:"upload-details"},[t("div",{staticClass:"detail-header"},[e._v("上传详情:")]),t("div",{staticClass:"detail-items"},e._l(e.uploadDetails.slice(-5),function(s,i){return t("div",{key:i,staticClass:"detail-item",class:s.status},[t("i",{class:e.getStatusIcon(s.status)}),t("span",[e._v(e._s(s.fileName))]),t("span",{staticClass:"status-text"},[e._v(e._s(s.message))])])}),0)]):e._e()],1):e._e()]),t("div",{staticClass:"upload-stats"},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.totalVideos))]),t("div",{staticClass:"stat-label"},[e._v("总视频数")])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.totalSize))]),t("div",{staticClass:"stat-label"},[e._v("总大小")])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.recentUploads))]),t("div",{staticClass:"stat-label"},[e._v("今日上传")])])])],1)],1)]),t("VideoListDialog",{on:{"videos-uploaded":e.handleDialogVideosUploaded,"show-transfer-info":e.showVideoTransferInfo},model:{value:e.videoListDialogVisible,callback:function(t){e.videoListDialogVisible=t},expression:"videoListDialogVisible"}}),t("el-dialog",{attrs:{title:"视频分配管理",visible:e.videoAssignDialogVisible,width:"70%","before-close":e.handleAssignDialogClose},on:{"update:visible":function(t){e.videoAssignDialogVisible=t}}},[t("div",{staticClass:"video-assign-content"},[t("el-card",{staticClass:"assign-config-card"},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("分配配置")])]),t("el-form",{attrs:{model:e.assignForm,"label-width":"120px"}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"参考设备数量"}},[t("el-input-number",{attrs:{min:1,max:Math.max(1,Math.min(e.onlineDevices.length,20)),placeholder:"设备数量"},on:{change:e.updateDeviceAndVideoCount},model:{value:e.assignForm.deviceCount,callback:function(t){e.$set(e.assignForm,"deviceCount",t)},expression:"assignForm.deviceCount"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("在线设备: "+e._s(e.onlineDevices.length)+"个")])],1),t("el-alert",{staticStyle:{"margin-top":"10px"},attrs:{title:"分配说明",type:"info",closable:!1,"show-icon":""}},[e._v(" 灵活分配模式：您可以自由选择任意数量的设备和对应数量的视频进行分配，每个设备分配一个视频 ")])],1)],1),t("el-form-item",{attrs:{label:"分配策略"}},[t("el-radio-group",{model:{value:e.assignForm.strategy,callback:function(t){e.$set(e.assignForm,"strategy",t)},expression:"assignForm.strategy"}},[t("el-radio",{attrs:{label:"average"}},[e._v("平均分配")]),t("el-radio",{attrs:{label:"random"}},[e._v("随机分配")]),t("el-radio",{attrs:{label:"sequential"}},[e._v("顺序分配")])],1)],1)],1),t("div",{staticClass:"assign-actions"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-check",disabled:!e.canExecuteAssignment,loading:e.assignmentLoading},on:{click:e.executeVideoAssignment}},[e._v(" "+e._s(e.assignmentLoading?"分配中...":"执行分配")+" ")]),t("el-button",{on:{click:e.resetAssignForm}},[e._v("重置")])],1)],1),e.assignForm.deviceCount>0?t("el-card",{staticClass:"device-selection-card"},[t("div",{attrs:{slot:"header"},slot:"header"},[t("div",{staticClass:"selection-header"},[t("div",{staticClass:"selection-info"},[t("span",[e._v("选择设备 ("+e._s(e.selectedDevices.length)+"个已选择)")]),t("el-tooltip",{attrs:{content:"支持Ctrl+点击多选，Shift+点击范围选择",placement:"top"}},[t("i",{staticClass:"el-icon-info",staticStyle:{"margin-left":"5px",color:"#909399"}})])],1),t("div",{staticClass:"selection-actions"},[t("el-button",{attrs:{size:"mini",type:"primary",disabled:e.selectedDevices.length>=e.onlineDevices.length},on:{click:e.selectAllDevices}},[e._v(" 全选 ")]),t("el-button",{attrs:{size:"mini",disabled:0===e.selectedDevices.length},on:{click:e.clearDeviceSelection}},[e._v(" 清空 ")])],1)])]),t("div",{staticClass:"device-grid"},e._l(e.onlineDevices,function(s,i){return t("div",{key:s.device_id,staticClass:"device-item",class:{selected:e.isDeviceSelected(s.device_id),disabled:!e.canSelectDevice(s.device_id)},on:{click:function(t){return e.handleDeviceClick(s.device_id,i,t)}}},[t("div",{staticClass:"device-info"},[t("div",{staticClass:"device-name",attrs:{title:s.device_name}},[e._v(" "+e._s(s.device_name||s.device_id)+" ")]),t("div",{staticClass:"device-details"},[t("el-tag",{attrs:{type:"online"===s.status?"success":"warning",size:"mini"}},[e._v(" "+e._s("online"===s.status?"在线":"忙碌")+" ")]),t("span",{staticClass:"device-ip",attrs:{title:e.getDeviceIP(s)}},[e._v(" "+e._s(e.getDeviceIP(s))+" ")])],1),t("div",{staticClass:"device-extra-info"},[t("span",{staticClass:"device-model",attrs:{title:e.getDeviceModel(s.device_info)}},[e._v(" "+e._s(e.getDeviceModel(s.device_info))+" ")]),t("span",{staticClass:"device-android",attrs:{title:e.getAndroidVersion(s.device_info)}},[e._v(" "+e._s(e.getAndroidVersion(s.device_info))+" ")])]),t("div",{staticClass:"device-connection-info"},[t("span",{staticClass:"last-seen"},[e._v(e._s(e.formatLastSeen(s.last_seen)))])])]),t("div",{staticClass:"device-checkbox",on:{click:function(e){e.stopPropagation()}}},[t("el-checkbox",{attrs:{value:e.isDeviceSelected(s.device_id),disabled:!e.canSelectDevice(s.device_id)},on:{change:function(t){return e.toggleDeviceSelection(s.device_id)}}})],1)])}),0)]):e._e(),e.assignForm.deviceCount>0?t("el-card",{staticClass:"video-selection-card"},[t("div",{attrs:{slot:"header"},slot:"header"},[t("div",{staticClass:"selection-header"},[t("div",{staticClass:"selection-info"},[t("span",[e._v("选择视频 ("+e._s(e.selectedVideos.length)+"/"+e._s(e.selectedDevices.length||"请先选择设备")+") - 每设备一视频")]),t("el-tooltip",{attrs:{content:"支持Ctrl+点击多选，Shift+点击范围选择。每个设备只能分配一个视频",placement:"top"}},[t("i",{staticClass:"el-icon-info",staticStyle:{"margin-left":"5px",color:"#909399"}})])],1),t("div",{staticClass:"selection-actions"},[t("el-button",{attrs:{size:"mini",type:"primary",disabled:0===e.selectedDevices.length||e.selectedVideos.length>=e.selectedDevices.length},on:{click:e.selectAllVideos}},[e._v(" 全选 ")]),t("el-button",{attrs:{size:"mini",disabled:0===e.selectedVideos.length},on:{click:e.clearVideoSelection}},[e._v(" 清空 ")]),t("el-button",{attrs:{size:"mini",type:"success",icon:"el-icon-magic-stick",disabled:0===e.selectedDevices.length||0===e.availableVideos.length,loading:e.smartSelectionLoading},on:{click:e.smartSelectVideos}},[e._v(" 智能选择 ")])],1)])]),t("div",{staticClass:"video-grid"},e._l(e.availableVideos,function(s,i){return t("div",{key:s.id,staticClass:"video-item",class:{selected:e.isVideoSelected(s.id),disabled:!e.canSelectVideo(s.id)},on:{click:function(t){return e.handleVideoClick(s.id,i,t)}}},[t("div",{staticClass:"video-thumbnail"},[s.thumbnail_path?t("img",{attrs:{src:e.getThumbnailUrl(s.thumbnail_path),alt:s.original_name}}):t("div",{staticClass:"no-thumbnail"},[t("i",{staticClass:"el-icon-video-camera"})])]),t("div",{staticClass:"video-info"},[t("div",{staticClass:"video-name",attrs:{title:s.original_name}},[e._v(e._s(s.original_name))]),t("div",{staticClass:"video-details"},[t("span",{staticClass:"video-size"},[e._v(e._s(e.formatFileSize(s.file_size)))]),s.duration?t("span",{staticClass:"video-duration"},[e._v(e._s(e.formatDuration(s.duration)))]):e._e()]),t("div",{staticClass:"video-extra-info"},[t("el-tag",{attrs:{size:"mini",type:"info"}},[e._v(e._s(e.getVideoFormat(s.original_name)))]),t("span",{staticClass:"upload-time"},[e._v(e._s(e.formatTime(s.created_at)))])],1),t("div",{staticClass:"video-transfer-info"},[t("div",{staticClass:"transfer-stats"},[t("span",{staticClass:"transfer-count"},[t("i",{staticClass:"el-icon-download"}),e._v(" 传输: "+e._s(s.total_transfer_count||0)+"次 ")]),t("span",{staticClass:"success-count"},[t("i",{staticClass:"el-icon-check"}),e._v(" 成功: "+e._s(s.successful_transfer_count||0)+"次 ")])]),e.getTransferredDevices(s.transferred_devices).length>0?t("div",{staticClass:"transferred-devices"},[t("span",{staticClass:"devices-label"},[e._v("已传输设备:")]),e._l(e.getTransferredDevices(s.transferred_devices).slice(0,3),function(s){return t("el-tag",{key:s.deviceId,attrs:{size:"mini",type:"success",title:`设备: ${s.deviceName||s.deviceId}, 传输时间: ${e.formatTime(s.transferTime)}`}},[e._v(" "+e._s(e.getDeviceDisplayName(s))+" ")])}),e.getTransferredDevices(s.transferred_devices).length>3?t("span",{staticClass:"more-devices"},[e._v(" +"+e._s(e.getTransferredDevices(s.transferred_devices).length-3)+"个设备 ")]):e._e()],2):e._e()])]),t("div",{staticClass:"video-checkbox",on:{click:function(e){e.stopPropagation()}}},[t("el-checkbox",{attrs:{value:e.isVideoSelected(s.id),disabled:!e.canSelectVideo(s.id)},on:{change:function(t){return e.toggleVideoSelection(s.id)}}})],1)])}),0)]):e._e(),e.assignmentPreview.length>0?t("el-card",{staticClass:"assignment-preview-card"},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("分配预览")])]),t("div",{staticClass:"preview-content"},e._l(e.assignmentPreview,function(s,i){return t("div",{key:i,staticClass:"assignment-item",class:{"no-video":"no_video"===s.status}},[t("div",{staticClass:"device-info"},[t("strong",[e._v(e._s(s.deviceName))]),s.videos.length>0?t("span",{staticClass:"video-count"},[e._v(" ("+e._s(s.videos.length)+" 个视频) ")]):t("span",{staticClass:"no-video-text"},[e._v(" (未分配视频) ")])]),s.videos.length>0?t("div",{staticClass:"video-list"},e._l(s.videos,function(s){return t("el-tag",{key:s.id,attrs:{size:"mini",type:"success"}},[e._v(" "+e._s(s.original_name)+" ")])}),1):t("div",{staticClass:"no-video-placeholder"},[t("el-tag",{attrs:{size:"mini",type:"warning"}},[e._v(" 需要更多视频 ")])],1)])}),0)]):e._e()],1)]),t("el-dialog",{attrs:{title:e.transferInfoDialogTitle,visible:e.transferInfoDialogVisible,width:"800px","close-on-click-modal":!1},on:{"update:visible":function(t){e.transferInfoDialogVisible=t}}},[e.currentVideoTransferInfo?t("div",[t("div",{staticClass:"video-transfer-info"},[t("div",{staticClass:"video-basic-info"},[t("h4",[e._v("📹 视频基本信息")]),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"info-label"},[e._v("文件名：")]),t("span",{staticClass:"info-value"},[e._v(e._s(e.currentVideoTransferInfo.original_name))])])]),t("el-col",{attrs:{span:12}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"info-label"},[e._v("文件大小：")]),t("span",{staticClass:"info-value"},[e._v(e._s(e.formatFileSize(e.currentVideoTransferInfo.file_size)))])])]),t("el-col",{attrs:{span:12}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"info-label"},[e._v("视频时长：")]),t("span",{staticClass:"info-value"},[e._v(e._s(e.formatDuration(e.currentVideoTransferInfo.video_duration)))])])]),t("el-col",{attrs:{span:12}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"info-label"},[e._v("上传时间：")]),t("span",{staticClass:"info-value"},[e._v(e._s(e.formatDateTime(e.currentVideoTransferInfo.upload_time)))])])])],1)],1),t("div",{staticClass:"transfer-statistics"},[t("h4",[e._v("📊 传输统计")]),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.transferStatistics.totalTransfers))]),t("div",{staticClass:"stat-label"},[e._v("总传输次数")])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.transferStatistics.uniqueDevices))]),t("div",{staticClass:"stat-label"},[e._v("传输设备数")])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.transferStatistics.successRate)+"%")]),t("div",{staticClass:"stat-label"},[e._v("成功率")])])])],1)],1),t("div",{staticClass:"transfer-records"},[t("h4",[e._v("📋 传输记录")]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.transferRecords,"max-height":"300","empty-text":"暂无数据"}},[t("el-table-column",{attrs:{prop:"device_name",label:"设备名称",width:"120"}}),t("el-table-column",{attrs:{prop:"device_id",label:"设备ID",width:"180"}}),t("el-table-column",{attrs:{prop:"device_ip",label:"设备IP",width:"120"}}),t("el-table-column",{attrs:{label:"传输类型",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:"manual"===s.row.transfer_type?"primary":"success",size:"mini"}},[e._v(" "+e._s("manual"===s.row.transfer_type?"手动传输":"脚本传输")+" ")])]}}],null,!1,1118882530)}),t("el-table-column",{attrs:{prop:"status",label:"传输状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:"completed"===s.row.status?"success":"failed"===s.row.status?"danger":"warning",size:"mini"}},[e._v(" "+e._s(e.getTransferStatusText(s.row.status))+" ")])]}}],null,!1,2972425199)}),t("el-table-column",{attrs:{prop:"transfer_progress",label:"进度",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.transfer_progress||0)+"% ")]}}],null,!1,685657409)}),t("el-table-column",{attrs:{prop:"transfer_speed",label:"传输速度",width:"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.transfer_speed&&s.row.transfer_speed>0?t("span",[e._v(" "+e._s(e.formatFileSize(1024*s.row.transfer_speed))+"/s ")]):t("span",[e._v("-")])]}}],null,!1,533311249)}),t("el-table-column",{attrs:{prop:"transfer_time",label:"开始时间",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatDateTime(t.row.transfer_time))+" ")]}}],null,!1,2653937919)}),t("el-table-column",{attrs:{prop:"completed_time",label:"完成时间",width:"160"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.completed_time?t("span",[e._v(" "+e._s(e.formatDateTime(s.row.completed_time))+" ")]):t("span",[e._v("-")])]}}],null,!1,1692561119)}),t("el-table-column",{attrs:{prop:"task_id",label:"任务ID",width:"120","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"error_message",label:"错误信息","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.error_message?t("span",{staticStyle:{color:"#F56C6C"}},[e._v(" "+e._s(s.row.error_message)+" ")]):t("span",{staticStyle:{color:"#67C23A"}},[e._v("正常")])]}}],null,!1,3579279995)})],1)],1)])]):e._e()])],1)},Y=[],Z=s(4335),ee=function(){var e=this,t=e._self._c;return t("el-dialog",{staticClass:"video-list-dialog",attrs:{title:"视频文件管理",visible:e.visible,width:"80%","before-close":e.handleClose},on:{"update:visible":function(t){e.visible=t}}},[t("div",{staticClass:"toolbar"},[t("div",{staticClass:"toolbar-left"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-refresh",size:"small",loading:e.loading},on:{click:e.loadVideoFiles}},[e._v(" 刷新列表 ")]),t("el-button",{attrs:{type:"success",icon:"el-icon-upload",size:"small"},on:{click:e.showUploadDialog}},[e._v(" 上传视频 ")]),t("el-button",{attrs:{type:"info",icon:"el-icon-check",size:"small"},on:{click:e.toggleSelectAll}},[e._v(" "+e._s(e.isAllSelected?"取消全选":"全选")+" ")]),t("el-button",{attrs:{type:"danger",icon:"el-icon-delete",disabled:0===e.selectedVideoIds.length,size:"small"},on:{click:e.batchDeleteVideos}},[e._v(" 批量删除 ("+e._s(e.selectedVideoIds.length)+") ")]),t("el-button",{attrs:{type:"warning",icon:"el-icon-delete-solid",size:"small"},on:{click:e.clearAllVideos}},[e._v(" 清空所有 ")]),t("el-button",{attrs:{type:"primary",icon:"el-icon-download",disabled:0===e.selectedVideoIds.length,size:"small"},on:{click:e.transferVideosToDevice}},[e._v(" 传输视频 ("+e._s(e.selectedVideoIds.length)+") ")])],1),t("div",{staticClass:"toolbar-right"},[t("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"搜索视频文件名",size:"small"},on:{input:e.filterVideoFiles},model:{value:e.searchKeyword,callback:function(t){e.searchKeyword=t},expression:"searchKeyword"}},[t("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1)]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"video-grid"},e._l(e.filteredVideoFiles,function(s){return t("div",{key:s.id,staticClass:"video-card",class:{selected:e.isVideoSelected(s.id)},on:{click:function(t){return e.previewVideo(s)}}},[t("div",{staticClass:"selection-checkbox"},[t("el-checkbox",{attrs:{value:e.isVideoSelected(s.id)},on:{change:function(t){return e.toggleVideoSelection(s.id)},click:function(e){e.stopPropagation()}}})],1),t("div",{staticClass:"video-thumbnail",on:{click:function(t){return e.previewVideo(s)}}},[s.thumbnail_path?t("img",{attrs:{src:e.getThumbnailUrl(s.thumbnail_path),alt:s.original_name},on:{error:e.handleImageError}}):t("div",{staticClass:"no-thumbnail"},[t("i",{staticClass:"el-icon-video-camera"}),t("span",[e._v("无预览")])]),s.video_duration>0?t("div",{staticClass:"duration-badge"},[e._v(" "+e._s(e.formatDuration(s.video_duration))+" ")]):e._e(),t("div",{staticClass:"play-overlay"},[t("i",{staticClass:"el-icon-video-play"})])]),t("div",{staticClass:"video-info"},[t("div",{staticClass:"video-title",attrs:{title:s.original_name}},[e._v(" "+e._s(s.original_name)+" ")]),t("div",{staticClass:"video-meta"},[t("span",{staticClass:"file-size"},[e._v(e._s(e.formatFileSize(s.file_size)))]),t("span",{staticClass:"video-format"},[e._v(e._s(s.video_format.toUpperCase()))]),s.video_resolution?t("span",{staticClass:"resolution"},[e._v(e._s(s.video_resolution))]):e._e()]),t("div",{staticClass:"video-stats"},[t("span",{staticClass:"upload-time"},[e._v(e._s(e.formatTime(s.upload_time)))]),t("span",{staticClass:"usage-stats"},[e._v(" 分配: "+e._s(s.assignment_count||0)+" | 完成: "+e._s(s.completed_count||0)+" ")])]),t("div",{staticClass:"transfer-stats"},[t("span",{staticClass:"transfer-count"},[t("i",{staticClass:"el-icon-download"}),e._v(" 传输次数: "+e._s(s.total_transfer_count||s.transfer_count||0)+" ")]),t("span",{staticClass:"success-count"},[t("i",{staticClass:"el-icon-check"}),e._v(" 成功传输: "+e._s(s.successful_transfer_count||0)+" ")]),s.last_transfer_time?t("span",{staticClass:"last-transfer"},[e._v(" 最后传输: "+e._s(e.formatTime(s.last_transfer_time))+" ")]):e._e()]),s.transferred_devices&&e.getTransferredDevices(s.transferred_devices).length>0?t("div",{staticClass:"transferred-devices"},[t("span",{staticClass:"devices-label"},[e._v("已传输设备:")]),e._l(e.getTransferredDevices(s.transferred_devices),function(s){return t("el-tag",{key:s.deviceId,attrs:{size:"mini",type:"success",title:`设备: ${s.deviceName} (${s.ipAddress}), 传输时间: ${e.formatTime(s.transferTime)}`}},[e._v(" "+e._s(s.ipAddress||s.deviceName)+" ")])})],2):e._e()]),t("div",{staticClass:"video-actions"},[t("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-view"},on:{click:function(t){return t.stopPropagation(),e.previewVideo(s)}}},[e._v(" 预览 ")]),t("el-button",{attrs:{size:"mini",type:"info",icon:"el-icon-data-line"},on:{click:function(t){return t.stopPropagation(),e.showTransferInfo(s)}}},[e._v(" 传输信息 ")]),t("el-button",{attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(t){return t.stopPropagation(),e.deleteVideo(s)}}},[e._v(" 删除 ")])],1)])}),0),e.pagination.total>0?t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"current-page":e.pagination.page,"page-sizes":[12,24,48,96],"page-size":e.pagination.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1):e._e(),t("el-dialog",{attrs:{title:"上传视频文件",visible:e.uploadDialogVisible,width:"600px","append-to-body":!0},on:{"update:visible":function(t){e.uploadDialogVisible=t}}},[t("div",{staticClass:"upload-section"},[t("div",{staticClass:"upload-buttons"},[t("el-upload",{staticClass:"upload-demo",attrs:{action:e.uploadUrl,headers:e.uploadHeaders,"on-success":e.handleUploadSuccess,"on-error":e.handleUploadError,"before-upload":e.beforeUpload,accept:"video/*","show-file-list":!0,disabled:e.uploading,multiple:"",name:"videos"}},[t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-upload",loading:e.uploading}},[e._v(" "+e._s(e.uploading?"上传中...":"选择视频文件")+" ")])],1),t("div",{staticClass:"folder-upload"},[t("input",{ref:"folderInput",staticStyle:{display:"none"},attrs:{type:"file",webkitdirectory:"",directory:"",multiple:"",accept:"video/*",disabled:e.uploading},on:{change:e.handleFolderSelect}}),t("el-button",{attrs:{size:"small",type:"success",icon:"el-icon-folder-add",disabled:e.uploading},on:{click:e.selectFolder}},[e._v(" 选择视频文件夹 ")])],1)],1),t("div",{staticClass:"upload-tips"},[t("div",[e._v("支持 MP4、AVI、MOV、WMV、FLV、MKV 等格式，单个文件不超过2GB")]),t("div",{staticStyle:{color:"#E6A23C","margin-top":"5px"}},[e._v(" ⚠️ 可选择文件夹批量上传（最多1000个文件），系统会自动检测重复文件 ")])]),e.uploading?t("div",{staticClass:"upload-progress"},[t("el-progress",{attrs:{percentage:e.uploadProgress,status:e.uploadStatus}}),t("p",[e._v(e._s(e.uploadMessage))])],1):e._e()])]),t("el-dialog",{attrs:{title:"视频预览",visible:e.previewDialogVisible,width:"70%","append-to-body":!0},on:{"update:visible":function(t){e.previewDialogVisible=t}}},[e.currentPreviewVideo?t("div",{staticClass:"video-preview"},[t("div",{staticClass:"preview-video"},[t("video",{ref:"previewPlayer",staticStyle:{width:"100%","max-height":"400px"},attrs:{src:e.getVideoUrl(e.currentPreviewVideo.file_path),controls:"",preload:"metadata"}},[e._v(" 您的浏览器不支持视频播放 ")])]),t("div",{staticClass:"preview-info"},[t("h3",[e._v(e._s(e.currentPreviewVideo.original_name))]),t("div",{staticClass:"info-grid"},[t("div",{staticClass:"info-item"},[t("label",[e._v("文件大小:")]),t("span",[e._v(e._s(e.formatFileSize(e.currentPreviewVideo.file_size)))])]),t("div",{staticClass:"info-item"},[t("label",[e._v("视频格式:")]),t("span",[e._v(e._s(e.currentPreviewVideo.video_format.toUpperCase()))])]),t("div",{staticClass:"info-item"},[t("label",[e._v("视频时长:")]),t("span",[e._v(e._s(e.formatDuration(e.currentPreviewVideo.video_duration)))])]),t("div",{staticClass:"info-item"},[t("label",[e._v("分辨率:")]),t("span",[e._v(e._s(e.currentPreviewVideo.video_resolution||"未知"))])]),t("div",{staticClass:"info-item"},[t("label",[e._v("上传时间:")]),t("span",[e._v(e._s(e.formatTime(e.currentPreviewVideo.upload_time)))])]),t("div",{staticClass:"info-item"},[t("label",[e._v("使用统计:")]),t("span",[e._v("分配 "+e._s(e.currentPreviewVideo.assignment_count||0)+" 次，完成 "+e._s(e.currentPreviewVideo.completed_count||0)+" 次")])])])])]):e._e()])],1)},te=[],se={name:"VideoListDialog",props:{value:{type:Boolean,default:!1}},data(){return{videoFiles:[],filteredVideoFiles:[],searchKeyword:"",loading:!1,uploading:!1,uploadProgress:0,uploadStatus:"",uploadMessage:"",uploadDialogVisible:!1,previewDialogVisible:!1,currentPreviewVideo:null,selectedVideoIds:[],pagination:{page:1,limit:24,total:0,totalPages:0}}},computed:{visible:{get(){return this.value},set(e){this.$emit("input",e)}},uploadUrl(){const{getApiBaseUrl:e}=s(9381);return`${e()}/api/xiaohongshu/upload-video-files`},uploadHeaders(){const e=this.$store.getters["auth/token"];return e?{Authorization:`Bearer ${e}`}:{}},isAllSelected(){return this.filteredVideoFiles.length>0&&this.selectedVideoIds.length===this.filteredVideoFiles.length}},watch:{visible(e){e&&this.loadVideoFiles()}},methods:{async loadVideoFiles(){this.loading=!0;try{const e=await Z.A.get("/api/xiaohongshu/video-files",{params:{page:this.pagination.page,limit:this.pagination.limit,status:"active"}});e.data.success&&(this.videoFiles=e.data.data.videos,this.pagination=e.data.data.pagination,console.log("📹 VideoListDialog: 加载的视频数据:",this.videoFiles),console.log("📊 VideoListDialog: 第一个视频的传输数据:",this.videoFiles[0]),this.filterVideoFiles())}catch(e){console.error("加载视频文件列表失败:",e),this.$message.error("加载视频文件列表失败")}finally{this.loading=!1}},filterVideoFiles(){if(this.searchKeyword.trim()){const e=this.searchKeyword.toLowerCase();this.filteredVideoFiles=this.videoFiles.filter(t=>t.original_name.toLowerCase().includes(e))}else this.filteredVideoFiles=this.videoFiles},showUploadDialog(){this.uploadDialogVisible=!0},beforeUpload(e){const t=e.type.startsWith("video/")||this.isVideoFile(e.name);if(!t)return this.$message.error("只能上传视频文件!"),!1;const s=e.size/1024/1024/1024<2;return s?(this.uploading=!0,this.uploadProgress=0,this.uploadStatus="",this.uploadMessage="准备上传...",!0):(this.$message.error(`视频文件 "${e.name}" 大小不能超过 2GB! 当前大小: ${this.formatFileSize(e.size)}`),!1)},isVideoFile(e){const t=[".mp4",".avi",".mov",".wmv",".flv",".mkv",".m4v",".3gp"],s=e.toLowerCase().substring(e.lastIndexOf("."));return t.includes(s)},handleUploadSuccess(e,t){if(this.uploading=!1,e.success){let t=e.message;e.data.duplicateCount>0&&(t+=`，其中 ${e.data.duplicateCount} 个重复文件已跳过`),this.$message.success(t),this.uploadDialogVisible=!1,this.loadVideoFiles(),this.$emit("videos-uploaded",e.data)}else this.$message.error(e.message||"上传失败")},handleUploadError(e){this.uploading=!1,this.$message.error("上传失败: "+e.message)},selectFolder(){this.$refs.folderInput.click()},async handleFolderSelect(e){const t=Array.from(e.target.files);if(0===t.length)return;const s=[".mp4",".avi",".mov",".wmv",".flv",".mkv",".m4v",".3gp"],i=[],o=[];if(t.forEach(e=>{const t=e.name.toLowerCase().substring(e.name.lastIndexOf("."));if(s.includes(t)){const t=e.size/1024/1024/1024;t<2?i.push(e):o.push({name:e.name,size:this.formatFileSize(e.size)})}}),o.length>0){const e=o.map(e=>`${e.name} (${e.size})`).join(", ");this.$message.warning(`以下文件超过2GB限制，已跳过: ${e}`)}if(0===i.length)return void this.$message.warning("选择的文件夹中没有找到视频文件");const n=t[0],a=n.webkitRelativePath.split("/"),l=a[0];this.$message.info(`正在上传文件夹 "${l}" 中的 ${i.length} 个视频文件...`),this.uploading=!0,this.uploadProgress=0,this.uploadMessage=`正在上传文件夹 "${l}" 中的视频...`;try{const e=new FormData;i.forEach(t=>{e.append("videos",t)}),e.append("description",`从文件夹 "${l}" 批量上传`),e.append("tags",`文件夹上传,${l}`);const t=await Z.A.post(this.uploadUrl,e,{headers:{...this.uploadHeaders},onUploadProgress:e=>{this.uploadProgress=Math.round(100*e.loaded/e.total)}});this.handleUploadSuccess(t.data),this.$refs.folderInput.value=""}catch(c){this.handleUploadError(c)}},previewVideo(e){this.currentPreviewVideo=e,this.previewDialogVisible=!0,this.$nextTick(()=>{this.$refs.previewPlayer&&this.$refs.previewPlayer.load()})},showTransferInfo(e){if(console.log("🎬🎬🎬 VideoListDialog: ===== 显示传输信息被调用 ===== 🎬🎬🎬"),console.log("🎬 VideoListDialog: 显示传输信息",e),console.log("🎬 VideoListDialog: 视频ID:",e.id,"视频名称:",e.original_name),console.log("🎬 VideoListDialog: 父组件:",this.$parent),console.log("🎬 VideoListDialog: 父组件构造函数名:",this.$parent.$options.name),console.log("🎬 VideoListDialog: 父组件是否有showVideoTransferInfo方法:",typeof this.$parent.showVideoTransferInfo),this.$parent&&"function"===typeof this.$parent.showVideoTransferInfo)return console.log("🎯 VideoListDialog: 直接调用父组件的showVideoTransferInfo方法"),void this.$parent.showVideoTransferInfo(e);console.log("📤 VideoListDialog: 发送show-transfer-info事件"),this.$emit("show-transfer-info",e),console.log("✅ VideoListDialog: 事件已发送"),setTimeout(()=>{console.log("⏰ VideoListDialog: 事件发送后1秒，检查是否有响应")},1e3)},isVideoSelected(e){return this.selectedVideoIds.includes(e)},toggleVideoSelection(e){const t=this.selectedVideoIds.indexOf(e);t>-1?this.selectedVideoIds.splice(t,1):this.selectedVideoIds.push(e)},toggleSelectAll(){this.isAllSelected?this.selectedVideoIds=[]:this.selectedVideoIds=this.filteredVideoFiles.map(e=>e.id)},async batchDeleteVideos(){if(0!==this.selectedVideoIds.length)try{await this.$confirm(`确定要删除选中的 ${this.selectedVideoIds.length} 个视频吗？`,"批量删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const e=this.selectedVideoIds.map(e=>this.deleteVideoById(e));await Promise.all(e),this.selectedVideoIds=[],this.$message.success(`成功删除 ${e.length} 个视频`),await this.loadVideoFiles()}catch(e){"cancel"!==e&&(console.error("批量删除失败:",e),this.$message.error("批量删除失败: "+e.message))}else this.$message.warning("请先选择要删除的视频")},async clearAllVideos(){if(0!==this.videoFiles.length)try{await this.$confirm(`确定要清空所有 ${this.videoFiles.length} 个视频吗？此操作不可恢复！`,"清空所有视频",{confirmButtonText:"确定清空",cancelButtonText:"取消",type:"error"});const e=this.videoFiles.map(e=>this.deleteVideoById(e.id));await Promise.all(e),this.selectedVideoIds=[],this.$message.success("成功清空所有视频"),await this.loadVideoFiles()}catch(e){"cancel"!==e&&(console.error("清空视频失败:",e),this.$message.error("清空视频失败: "+e.message))}else this.$message.warning("没有视频可以清空")},async deleteVideoById(e){const t=this.$store.getters["auth/token"],s=Z.A.create({baseURL:"",timeout:Z.A.defaults.timeout}),i=await s.delete(`/api/xiaohongshu/video-files/${e}`,{headers:t?{Authorization:`Bearer ${t}`}:{}});return i.data},async transferVideosToDevice(){if(0!==this.selectedVideoIds.length)try{const e=this.filteredVideoFiles.filter(e=>this.selectedVideoIds.includes(e.id)),t=await this.selectDevicesForTransfer();if(!t||0===t.length)return;this.$message.info(`开始传输 ${e.length} 个视频到 ${t.length} 个设备...`);const i=this.$store.getters["auth/token"],{getApiBaseUrl:o}=s(9381),n=await Z.A.post(`${o()}/api/xiaohongshu/transfer-videos`,{deviceIds:t,selectedVideoIds:this.selectedVideoIds,selectedVideos:e},{headers:{"Content-Type":"application/json",...i?{Authorization:`Bearer ${i}`}:{}}});n.data.success?(this.$message.success(n.data.message),console.log("传输结果:",n.data.data),this.showTransferResults(n.data.data)):this.$message.error("传输失败: "+n.data.message)}catch(e){console.error("传输视频失败:",e),this.$message.error("传输视频失败: "+e.message)}else this.$message.warning("请先选择要传输的视频")},async selectDevicesForTransfer(){return new Promise(e=>{this.$prompt("请输入设备ID（多个设备用逗号分隔）","选择传输设备",{confirmButtonText:"开始传输",cancelButtonText:"取消",inputValue:"test-device-1",inputValidator:e=>!(!e||""===e.trim())||"请输入设备ID"}).then(({value:t})=>{const s=t.split(",").map(e=>e.trim()).filter(e=>e);e(s)}).catch(()=>{e(null)})})},showTransferResults(e){const t=`\n        传输任务ID: ${e.transferTaskId}\n        视频数量: ${e.videoCount}\n        目标设备: ${e.deviceCount}\n\n        传输的视频:\n        ${e.videos.map(e=>`- ${e.name} (${this.formatFileSize(e.size)})`).join("\n")}\n\n        设备状态:\n        ${e.results.map(e=>`- ${e.deviceId}: ${"success"===e.status?"✅":"❌"} ${e.message}`).join("\n")}\n      `;this.$alert(t,"传输结果",{confirmButtonText:"确定",type:"info"})},async deleteVideo(e){try{await this.$confirm(`确定要删除视频文件 "${e.original_name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await Z.A.delete(`/api/xiaohongshu/video-files/${e.id}`);t.data.success?(this.$message.success("视频文件删除成功"),this.loadVideoFiles()):this.$message.error(t.data.message||"删除失败")}catch(t){"cancel"!==t&&(console.error("删除视频文件失败:",t),this.$message.error("删除视频文件失败"))}},handleSizeChange(e){this.pagination.limit=e,this.pagination.page=1,this.loadVideoFiles()},handleCurrentChange(e){this.pagination.page=e,this.loadVideoFiles()},handleClose(){this.visible=!1},handleImageError(e){try{if(e&&e.target){e.target.style.display="none";const t=e.target.parentNode;if(t){const e=t.querySelector(".no-thumbnail");e&&(e.style.display="flex")}}}catch(t){console.warn("处理图片错误时出现异常:",t)}},getThumbnailUrl(e){if(!e)return"";const{getApiBaseUrl:t}=s(9381);return`${t()}${e}`},getVideoUrl(e){if(!e)return"";if(e.startsWith("http"))return e;if(e.startsWith("/uploads/")){const{getApiBaseUrl:t}=s(9381);return`${t()}${e}`}const t=e.split("/").pop()||e.split("\\").pop()||e,{getApiBaseUrl:i}=s(9381);return`${i()}/uploads/videos/${t}`},formatFileSize(e){if(0===e)return"0 B";const t=1024,s=["B","KB","MB","GB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]},formatDuration(e){if(!e)return"0:00";const t=Math.floor(e/60),s=e%60;return`${t}:${s.toString().padStart(2,"0")}`},formatTime(e){return e?new Date(e).toLocaleString("zh-CN"):"-"},getTransferredDevices(e){if(!e)return[];try{const t="string"===typeof e?JSON.parse(e):e;return console.log("🔍 [VideoListDialog] 解析传输设备信息:",{"原始数据":e,"解析结果":t,"是否为数组":Array.isArray(t)}),Array.isArray(t)?t:[]}catch(t){return console.error("解析传输设备信息失败:",t),[]}}}},ie=se,oe=(0,u.A)(ie,ee,te,!1,null,"50fbfb02",null),ne=oe.exports,ae=s(9381),le={name:"VideoFileManager",components:{VideoListDialog:ne},data(){return{uploading:!1,uploadProgress:0,uploadStatus:"",uploadMessage:"",uploadDetails:[],videoListDialogVisible:!1,totalVideos:0,totalSize:"0 B",recentUploads:0,selectedFolder:null,videoAssignDialogVisible:!1,assignForm:{deviceCount:1,videoCount:1,strategy:"average"},onlineDevices:[],availableVideos:[],selectedDevices:[],selectedVideos:[],assignmentPreview:[],assignmentLoading:!1,smartSelectionLoading:!1,lastSelectedDeviceIndex:-1,lastSelectedVideoIndex:-1,transferInfoDialogVisible:!1,transferInfoDialogTitle:"",currentVideoTransferInfo:null,transferRecords:[],transferStatistics:{totalTransfers:0,uniqueDevices:0,successRate:0}}},computed:{uploadUrl(){const{getApiBaseUrl:e}=s(9381);return`${e()}/api/xiaohongshu/upload-video-files`},uploadHeaders(){const e=this.$store.getters["auth/token"];return e?{Authorization:`Bearer ${e}`}:{}},canExecuteAssignment(){return this.selectedDevices.length>0&&this.selectedVideos.length>0&&this.selectedVideos.length===this.selectedDevices.length}},mounted(){this.loadVideoStats()},methods:{getApiBaseUrl(){return(0,ae.getApiBaseUrl)()},async loadVideoStats(){try{const e=await Z.A.get("/api/xiaohongshu/video-stats");e.data.success&&(this.totalVideos=e.data.data.totalVideos,this.totalSize=this.formatFileSize(e.data.data.totalSize),this.recentUploads=e.data.data.todayUploads)}catch(e){console.error("加载视频统计失败:",e),this.totalVideos=0,this.totalSize="0 B",this.recentUploads=0}},showVideoListDialog(){this.videoListDialogVisible=!0},handleDialogVideosUploaded(e){this.loadVideoStats(),this.$emit("videos-uploaded",e)},async showVideoTransferInfo(e){try{console.log("🎯🎯🎯 [VideoFileManager] ===== 接收到show-transfer-info事件 ===== 🎯🎯🎯"),console.log("🔍 [VideoFileManager] 显示视频传输信息:",e),console.log("🔍 [VideoFileManager] 视频ID:",e.id,"视频名称:",e.original_name),this.transferInfoDialogVisible=!0,this.transferInfoDialogTitle=`视频传输信息 - ${e.original_name}`,this.currentVideoTransferInfo=e,console.log("🎯 [VideoFileManager] 弹出框已强制显示，标题:",this.transferInfoDialogTitle),console.log("📡 [VideoFileManager] 请求传输记录:",`/api/videos/${e.id}/transfer-records`);const t=localStorage.getItem("token"),s=await Z.A.get(`/api/videos/${e.id}/transfer-records`,{headers:{...t?{Authorization:`Bearer ${t}`}:{}}});console.log("📊 [VideoFileManager] 传输记录响应:",s.data),s.data.success?(this.transferRecords=s.data.records||[],this.transferStatistics=s.data.statistics||{totalTransfers:0,uniqueDevices:0,successRate:0},console.log("✅ [VideoFileManager] 传输记录设置完成:",this.transferRecords),console.log("📈 [VideoFileManager] 传输统计:",this.transferStatistics)):console.error("❌ [VideoFileManager] API返回失败:",s.data.message)}catch(t){console.error("❌ [VideoFileManager] 获取视频传输信息失败:",t),this.$message.error("获取传输信息失败: "+t.message)}},beforeUpload(e){const t=e.type.startsWith("video/")||this.isVideoFile(e.name);if(!t)return this.$message.error("只能上传视频文件!"),!1;const s=e.size/1024/1024/1024<2;return s?(this.uploading=!0,this.uploadProgress=0,this.uploadStatus="",this.uploadMessage="准备上传...",!0):(this.$message.error(`视频文件 "${e.name}" 大小不能超过 2GB! 当前大小: ${this.formatFileSize(e.size)}`),!1)},isVideoFile(e){const t=[".mp4",".avi",".mov",".wmv",".flv",".mkv",".m4v",".3gp"],s=e.toLowerCase().substring(e.lastIndexOf("."));return t.includes(s)},handleUploadSuccess(e,t){if(this.uploading=!1,e.success){let t=`成功上传 ${e.data.uploadCount} 个视频文件`;e.data.duplicateCount>0&&(t+=`，跳过 ${e.data.duplicateCount} 个重复文件`),this.$message.success(t),this.loadVideoStats(),this.$emit("videos-uploaded",e.data)}else this.$message.error(e.message||"上传失败")},handleUploadError(e){this.uploading=!1,this.$message.error("上传失败: "+e.message)},selectFolder(){this.$refs.folderInput.click()},handleFolderSelect(e){const t=Array.from(e.target.files);if(0===t.length)return;const s=[".mp4",".avi",".mov",".wmv",".flv",".mkv",".m4v",".3gp"],i=[],o=[];if(t.forEach(e=>{const t=e.name.toLowerCase().substring(e.name.lastIndexOf("."));if(s.includes(t)){const t=e.size/1024/1024/1024;t<2?i.push(e):o.push({name:e.name,size:this.formatFileSize(e.size)})}}),o.length>0){const e=o.map(e=>`${e.name} (${e.size})`).join(", ");this.$message.warning(`以下文件超过2GB限制，已跳过: ${e}`)}if(0===i.length)return void this.$message.warning("选择的文件夹中没有找到视频文件");const n=t[0],a=n.webkitRelativePath.split("/"),l=a[0];this.selectedFolder={name:l,videoFiles:i,totalSize:i.reduce((e,t)=>e+t.size,0)},this.$message.success(`已选择文件夹 "${l}"，找到 ${i.length} 个视频文件`)},async uploadFolderVideos(){if(this.selectedFolder&&0!==this.selectedFolder.videoFiles.length){this.uploading=!0,this.uploadProgress=0,this.uploadStatus="",this.uploadMessage="准备上传文件夹中的视频...",this.uploadDetails=[];try{const e=new FormData;this.selectedFolder.videoFiles.forEach(t=>{e.append("videos",t)}),e.append("description",`从文件夹 "${this.selectedFolder.name}" 批量上传`),e.append("tags",`文件夹上传,${this.selectedFolder.name}`),this.uploadMessage=`正在上传 ${this.selectedFolder.videoFiles.length} 个视频文件...`;const t=await Z.A.post(this.uploadUrl,e,{headers:{...this.uploadHeaders},timeout:6e5,onUploadProgress:e=>{this.uploadProgress=Math.round(100*e.loaded/e.total)}});this.handleUploadSuccess(t.data),this.selectedFolder=null,this.$refs.folderInput.value=""}catch(e){this.handleUploadError(e)}}else this.$message.warning("没有可上传的视频文件")},getStatusIcon(e){switch(e){case"success":return"el-icon-success";case"error":return"el-icon-error";case"uploading":return"el-icon-loading";default:return"el-icon-info"}},formatFileSize(e){if(0===e)return"0 B";const t=1024,s=["B","KB","MB","GB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]},formatTime(e){return e?new Date(e).toLocaleString("zh-CN"):"-"},async showVideoAssignDialog(){if(this.videoAssignDialogVisible=!0,await this.loadOnlineDevices(),await this.loadAvailableVideos(),this.onlineDevices.length>0&&1===this.assignForm.deviceCount){const e=Math.min(this.onlineDevices.length,this.availableVideos.length,3);this.assignForm.deviceCount=Math.max(1,e)}console.log("视频分配对话框已打开"),console.log("在线设备数量:",this.onlineDevices.length),console.log("可用视频数量:",this.availableVideos.length),console.log("默认设备数量:",this.assignForm.deviceCount),console.log("分配模式: 一对一（每个设备分配一个视频）")},handleAssignDialogClose(){this.resetAssignForm(),this.videoAssignDialogVisible=!1},async loadOnlineDevices(){try{const e=await Z.A.get("/api/device/list");if(e.data.success){const t=e.data.data.map(e=>({device_id:e.deviceId,device_name:e.deviceName,device_info:e.deviceInfo,status:e.status,last_seen:e.lastActiveTime,created_at:e.createdAt,ip_address:e.deviceIP||"未知",is_connected:e.isConnected}));this.onlineDevices=t.filter(e=>"online"===e.status),console.log("加载在线设备成功:",this.onlineDevices.length,"个设备"),this.onlineDevices.length>0&&(console.log("设备数据结构示例:",this.onlineDevices[0]),console.log("设备字段:",Object.keys(this.onlineDevices[0])))}}catch(e){console.error("加载在线设备失败:",e),this.$message.error("加载在线设备失败")}},async loadAvailableVideos(){try{const e=await Z.A.get("/api/xiaohongshu/video-files",{params:{status:"active",limit:100}});e.data.success&&(this.availableVideos=e.data.data.videos)}catch(e){console.error("加载可用视频失败:",e),this.$message.error("加载可用视频失败")}},updateDeviceAndVideoCount(){this.selectedVideos.length>this.selectedDevices.length&&this.selectedDevices.length>0&&(this.selectedVideos=this.selectedVideos.slice(0,this.selectedDevices.length)),this.lastSelectedDeviceIndex=-1,this.lastSelectedVideoIndex=-1,this.updateAssignmentPreview(),console.log("设备数量参考值已更新为:",this.assignForm.deviceCount),console.log("实际分配将根据用户选择的设备和视频数量进行（灵活分配）")},isDeviceSelected(e){return this.selectedDevices.includes(e)},toggleDeviceSelection(e){console.log("toggleDeviceSelection called with:",e),console.log("canSelectDevice:",this.canSelectDevice(e)),console.log("isDeviceSelected:",this.isDeviceSelected(e)),console.log("selectedDevices.length:",this.selectedDevices.length),console.log("assignForm.deviceCount:",this.assignForm.deviceCount),this.canSelectDevice(e)?(this.isDeviceSelected(e)?(console.log("Removing device from selection"),this.selectedDevices=this.selectedDevices.filter(t=>t!==e)):(console.log("Adding device to selection"),this.selectedDevices.push(e)),console.log("Updated selectedDevices:",this.selectedDevices),this.updateAssignmentPreview()):console.log("Cannot select device, returning")},handleDeviceClick(e,t,s){this.canSelectDevice(e)&&(s.ctrlKey||s.metaKey?(this.toggleDeviceSelection(e),this.lastSelectedDeviceIndex=t):s.shiftKey&&-1!==this.lastSelectedDeviceIndex?this.selectDeviceRange(this.lastSelectedDeviceIndex,t):(this.toggleDeviceSelection(e),this.lastSelectedDeviceIndex=t))},selectDeviceRange(e,t){const s=Math.min(e,t),i=Math.max(e,t);for(let o=s;o<=i;o++)if(o<this.onlineDevices.length){const e=this.onlineDevices[o].device_id;this.isDeviceSelected(e)||this.selectedDevices.push(e)}this.updateAssignmentPreview()},canSelectDevice(e){return!0},selectAllDevices(){this.selectedDevices=this.onlineDevices.map(e=>e.device_id),this.updateAssignmentPreview()},clearDeviceSelection(){this.selectedDevices=[],this.lastSelectedDeviceIndex=-1,this.updateAssignmentPreview()},getDeviceIP(e){return e.ip_address||e.deviceIP||e.device_ip||e.device_info&&"object"===typeof e.device_info&&e.device_info.ipAddress||e.device_info&&"object"===typeof e.device_info&&e.device_info.ip||"未知IP"},getDeviceModel(e){if(!e)return"未知型号";if("string"===typeof e)try{e=JSON.parse(e)}catch(s){return console.log("设备信息JSON解析失败:",e),"未知型号"}const t=e?.model||e?.brand||e?.manufacturer||e?.device||e?.product||"未知型号";return t},getAndroidVersion(e){if(!e)return"";if("string"===typeof e)try{e=JSON.parse(e)}catch(s){return""}const t=e?.release||e?.version||e?.android_version||e?.sdk_int;return t?"number"===typeof t?`API ${t}`:`Android ${t}`:""},formatLastSeen(e){if(!e)return"从未连接";const t=new Date,s=new Date(e),i=t-s,o=Math.floor(i/6e4);return o<1?"刚刚活跃":o<60?`${o}分钟前`:o<1440?`${Math.floor(o/60)}小时前`:`${Math.floor(o/1440)}天前`},isVideoSelected(e){return this.selectedVideos.some(t=>t.id===e)},toggleVideoSelection(e){if(console.log("toggleVideoSelection called with:",e),console.log("canSelectVideo:",this.canSelectVideo(e)),console.log("isVideoSelected:",this.isVideoSelected(e)),console.log("selectedVideos.length:",this.selectedVideos.length),console.log("assignForm.videoCount:",this.assignForm.videoCount),!this.canSelectVideo(e))return void console.log("Cannot select video, returning");const t=this.availableVideos.find(t=>t.id===e);t?(this.isVideoSelected(e)?(console.log("Removing video from selection"),this.selectedVideos=this.selectedVideos.filter(t=>t.id!==e)):this.selectedVideos.length<this.selectedDevices.length&&(console.log("Adding video to selection"),this.selectedVideos.push(t)),console.log("Updated selectedVideos:",this.selectedVideos),this.updateAssignmentPreview()):console.log("Video not found:",e)},handleVideoClick(e,t,s){this.canSelectVideo(e)&&(s.ctrlKey||s.metaKey?(this.toggleVideoSelection(e),this.lastSelectedVideoIndex=t):s.shiftKey&&-1!==this.lastSelectedVideoIndex?this.selectVideoRange(this.lastSelectedVideoIndex,t):(this.toggleVideoSelection(e),this.lastSelectedVideoIndex=t))},selectVideoRange(e,t){const s=Math.min(e,t),i=Math.max(e,t);for(let o=s;o<=i;o++)if(o<this.availableVideos.length){const e=this.availableVideos[o];!this.isVideoSelected(e.id)&&this.selectedVideos.length<this.selectedDevices.length&&this.selectedVideos.push(e)}this.updateAssignmentPreview()},canSelectVideo(e){return 0!==this.selectedDevices.length&&(this.isVideoSelected(e)||this.selectedVideos.length<this.selectedDevices.length)},selectAllVideos(){if(0===this.selectedDevices.length)return void this.$message.warning("请先选择设备");const e=Math.min(this.selectedDevices.length,this.availableVideos.length),t=this.availableVideos.slice(0,e);this.selectedVideos=t,this.updateAssignmentPreview()},clearVideoSelection(){this.selectedVideos=[],this.lastSelectedVideoIndex=-1,this.updateAssignmentPreview()},async smartSelectVideos(){if(0!==this.selectedDevices.length){this.smartSelectionLoading=!0;try{const e=this.$store.getters["auth/token"],t=await Z.A.post(`${this.getApiBaseUrl()}/api/xiaohongshu/smart-select-videos`,{deviceIds:this.selectedDevices,videoCount:this.selectedDevices.length},{headers:e?{Authorization:`Bearer ${e}`}:{}});if(t.data.success){const{selectedVideos:e,selectionStrategy:s}=t.data.data;this.selectedVideos=[];const i=[];e.forEach(e=>{const t=this.availableVideos.find(t=>t.id===e.id);t?i.push(t):i.push({id:e.id,original_name:e.original_name,file_size:e.file_size,total_transfer_count:e.total_transfers,successful_transfer_count:e.successful_transfers,transferred_devices:e.transferred_devices,last_transfer_time:e.last_transfer_time})}),this.selectedVideos=i,this.updateAssignmentPreview(),this.$message({type:"success",message:`智能选择完成！选择了 ${e.length} 个视频`,duration:3e3}),this.$notify({title:"智能选择策略",message:`\n              ${s.strategy}\n\n              统计信息：\n              • 从未传输的视频：${s.neverTransferredCount} 个\n              • 未传输到选中设备的视频：${s.notToSelectedDevicesCount} 个\n              • 总可用视频：${s.totalVideos} 个\n            `,type:"info",duration:8e3}),console.log("智能选择结果:",{selectedVideos:i,selectionStrategy:s,selectedDevices:this.selectedDevices})}else this.$message.error("智能选择失败: "+t.data.message)}catch(e){console.error("智能选择视频失败:",e),this.$message.error("智能选择视频失败: "+e.message)}finally{this.smartSelectionLoading=!1}}else this.$message.warning("请先选择设备")},getVideoFormat(e){const t=e.split(".").pop()?.toLowerCase();return t?t.toUpperCase():"VIDEO"},updateAssignmentPreview(){if(0===this.selectedDevices.length||0===this.selectedVideos.length)return void(this.assignmentPreview=[]);const e=[];for(let t=0;t<this.selectedDevices.length;t++){const s=this.selectedDevices[t],i=this.onlineDevices.find(e=>e.device_id===s);if(t<this.selectedVideos.length){const o=this.selectedVideos[t];e.push({deviceId:s,deviceName:i?i.device_name:s,videos:[o]})}else e.push({deviceId:s,deviceName:i?i.device_name:s,videos:[],status:"no_video"})}this.assignmentPreview=e},resetAssignForm(){this.assignForm={deviceCount:1,strategy:"one_to_one"},this.selectedDevices=[],this.selectedVideos=[],this.assignmentPreview=[],this.lastSelectedDeviceIndex=-1,this.lastSelectedVideoIndex=-1},getThumbnailUrl(e){if(!e)return"";const{getApiBaseUrl:t}=s(9381);return`${t()}${e}`},async executeVideoAssignment(){if(this.canExecuteAssignment){this.assignmentLoading=!0;try{const e=`video_assign_${Date.now()}`,t=this.selectedVideos.map(e=>e.id),s=await Z.A.post("/api/xiaohongshu/assign-videos",{videoIds:t,deviceIds:this.selectedDevices,taskId:e,strategy:this.assignForm.strategy});s.data.success?(this.$message.success(`视频分配成功！共分配了 ${s.data.data.assignments} 个任务`),this.syncAssignmentToVideoPublish(),this.$alert(`\n分配完成详情：\n• 任务ID: ${e}\n• 设备数量: ${this.selectedDevices.length}\n• 视频数量: ${this.selectedVideos.length}\n• 分配策略: 灵活分配（每个设备一个视频）\n\n分配详情:\n${this.assignmentPreview.map(e=>`• ${e.deviceName}: ${e.videos.length} 个视频`).join("\n")}\n\n✅ 已自动同步到视频发布功能：\n• 已选中分配的设备\n• 已为每个设备配置对应的视频\n          `,"分配成功",{confirmButtonText:"确定",type:"success"}),this.resetAssignForm(),this.videoAssignDialogVisible=!1,this.loadVideoStats()):this.$message.error("分配失败: "+s.data.message)}catch(e){console.error("执行视频分配失败:",e),this.$message.error("执行视频分配失败: "+e.message)}finally{this.assignmentLoading=!1}}else this.$message.warning("请完善分配配置")},syncAssignmentToVideoPublish(){try{console.log("🔄 开始同步视频分配结果到视频发布功能"),console.log("分配预览:",this.assignmentPreview);const e={selectedDevices:this.selectedDevices,deviceVideoAssignments:{}};this.assignmentPreview.forEach(t=>{t.videos&&t.videos.length>0&&(e.deviceVideoAssignments[t.deviceId]={deviceId:t.deviceId,deviceName:t.deviceName,assignedVideo:t.videos[0],videoInfo:{id:t.videos[0].id,original_name:t.videos[0].original_name,file_path:t.videos[0].file_path,file_size:t.videos[0].file_size,thumbnail_path:t.videos[0].thumbnail_path}})}),console.log("构建的分配数据:",e),this.$emit("video-assignment-completed",e),this.$root.$emit("xiaohongshu-video-assignment-sync",e),console.log("✅ 视频分配结果同步事件已发送")}catch(e){console.error("❌ 同步视频分配结果失败:",e),this.$message.warning("同步分配结果到视频发布功能时出现问题，请手动配置")}},formatFileSize(e){if(!e)return"0 B";const t=1024,s=["B","KB","MB","GB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]},getTransferredDevices(e){if(!e)return[];try{const t="string"===typeof e?JSON.parse(e):e;return Array.isArray(t)?t:[]}catch(t){return console.error("解析传输设备信息失败:",t),[]}},getDeviceDisplayName(e){return e?e.ipAddress?e.ipAddress:e.deviceName&&e.deviceName!==e.deviceId?e.deviceName:e.deviceId||"":""},formatDuration(e){if(!e)return"-";const t=Math.floor(e/60),s=e%60;return`${t}:${s.toString().padStart(2,"0")}`},formatDateTime(e){return e?new Date(e).toLocaleString("zh-CN"):"-"},getTransferStatusText(e){const t={pending:"等待中",transferring:"传输中",in_progress:"传输中",completed:"已完成",failed:"失败",success:"成功",error:"错误"};return t[e]||e}}},ce=le,re=(0,u.A)(ce,Q,Y,!1,null,"055babea",null),de=re.exports,ue=function(){var e=this,t=e._self._c;return t("div",{staticClass:"video-publish-config"},[t("el-card",[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("小红书发布视频配置")])]),e.isScriptRunning||e.isScriptCompleted?t("div",{staticClass:"execution-status"},[t("el-alert",{attrs:{title:e.currentStatus,type:e.isScriptCompleted?"success":"info",closable:!1,"show-icon":""}},[t("div",[t("p",[e._v("已发布视频: "+e._s(e.publishedVideoCount)+" / "+e._s(e.totalVideoCount))]),t("p",[e._v("当前步骤: "+e._s(e.currentStep))]),e.errorMessage?t("p",{staticStyle:{color:"#F56C6C"}},[e._v("错误信息: "+e._s(e.errorMessage))]):e._e()])])],1):e._e(),t("div",{staticClass:"video-selection-section"},[t("h4",[e._v("选择要发布的视频")]),t("div",{staticClass:"video-actions"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-view",disabled:e.selectedVideos.length>=1},on:{click:e.showVideoSelectionDialog}},[e._v(" "+e._s(e.selectedVideos.length>=1?"已选择视频":"选择视频文件")+" ")]),t("el-button",{attrs:{type:"info",icon:"el-icon-folder-opened"},on:{click:e.showVideoListDialog}},[e._v(" 查看视频列表 ")])],1),e.selectedVideos.length>0?t("div",{staticClass:"selected-videos-preview"},[t("h5",[e._v("已选择的视频 ("+e._s(e.selectedVideos.length)+")")]),t("div",{staticClass:"video-preview-grid"},[e._l(e.selectedVideos.slice(0,6),function(s){return t("div",{key:s.id,staticClass:"video-preview-item"},[t("div",{staticClass:"video-thumbnail"},[s.thumbnail_path?t("img",{attrs:{src:e.getThumbnailUrl(s.thumbnail_path),alt:s.original_name}}):t("div",{staticClass:"no-thumbnail"},[t("i",{staticClass:"el-icon-video-camera"})]),s.video_duration>0?t("div",{staticClass:"duration-badge"},[e._v(" "+e._s(e.formatDuration(s.video_duration))+" ")]):e._e()]),t("div",{staticClass:"video-name",attrs:{title:s.original_name}},[e._v(" "+e._s(s.original_name)+" ")]),t("div",{staticClass:"video-actions"},[t("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-view",title:"查看传输信息"},on:{click:function(t){return e.showVideoTransferInfo(s)}}},[e._v(" 传输信息 ")])],1)])}),e.selectedVideos.length>6?t("div",{staticClass:"more-videos"},[t("div",{staticClass:"more-count"},[e._v(" +"+e._s(e.selectedVideos.length-6)+" ")]),t("div",{staticClass:"more-text"},[e._v("更多视频")])]):e._e()],2),t("div",{staticClass:"selection-summary"},[t("el-tag",{attrs:{type:"primary"}},[e._v(" 总计: "+e._s(e.selectedVideos.length)+" 个视频， "+e._s(e.formatFileSize(e.totalSelectedSize))+" ")]),t("el-button",{attrs:{size:"mini",type:"text"},on:{click:e.clearVideoSelection}},[e._v(" 清空选择 ")]),t("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:e.showAllVideosTransferInfo}},[e._v(" 查看传输记录 ")])],1)]):t("div",{staticClass:"no-videos-selected"},[t("i",{staticClass:"el-icon-video-camera"}),t("p",[e._v("请先选择要发布的视频文件")])])]),t("el-form",{staticClass:"config-form",attrs:{model:e.config,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"小红书应用"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择要使用的小红书应用"},on:{change:e.onAppSelectionChange},model:{value:e.config.selectedApp,callback:function(t){e.$set(e.config,"selectedApp",t)},expression:"config.selectedApp"}},e._l(e.xiaohongshuApps,function(s){return t("el-option",{key:s.text,attrs:{label:s.text,value:s.text}},[t("span",{staticStyle:{float:"left"}},[e._v(e._s(s.text))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(" "+e._s("keyword"===s.method?"关键词":"正则")+" ")])])}),1)],1),t("el-form-item",{attrs:{label:"视频标题模板"}},[t("el-input",{attrs:{placeholder:"输入视频标题模板，支持变量 {index}, {filename}"},on:{input:e.onInputChange},model:{value:e.config.titleTemplate,callback:function(t){e.$set(e.config,"titleTemplate",t)},expression:"config.titleTemplate"}}),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 支持变量：{index} - 序号，{filename} - 文件名 ")])],1),t("el-form-item",{attrs:{label:"视频描述"}},[t("el-input",{attrs:{type:"textarea",placeholder:"输入视频描述内容",rows:3},on:{input:e.onInputChange},model:{value:e.config.videoDescription,callback:function(t){e.$set(e.config,"videoDescription",t)},expression:"config.videoDescription"}})],1),t("el-form-item",{attrs:{label:"话题标签"}},[t("el-input",{attrs:{placeholder:"输入话题标签，用逗号分隔，如：#美食,#生活,#分享"},on:{input:e.onInputChange},model:{value:e.config.hashtags,callback:function(t){e.$set(e.config,"hashtags",t)},expression:"config.hashtags"}})],1),t("el-form-item",{attrs:{label:"发布设置"}},[t("el-checkbox-group",{model:{value:e.config.publishOptions,callback:function(t){e.$set(e.config,"publishOptions",t)},expression:"config.publishOptions"}},[t("el-checkbox",{attrs:{label:"allowComment"}},[e._v("允许评论")]),t("el-checkbox",{attrs:{label:"allowShare"}},[e._v("允许分享")]),t("el-checkbox",{attrs:{label:"showLocation"}},[e._v("显示位置")]),t("el-checkbox",{attrs:{label:"addToStory"}},[e._v("添加到动态")])],1)],1),t("el-form-item",{attrs:{label:"操作间隔"}},[t("el-input-number",{attrs:{min:3,max:30,placeholder:"秒"},on:{change:e.onInputChange},model:{value:e.config.operationDelay,callback:function(t){e.$set(e.config,"operationDelay",t)},expression:"config.operationDelay"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("每个操作步骤间隔时间（秒）")])],1),t("el-form-item",{attrs:{label:"失败重试"}},[t("el-input-number",{attrs:{min:0,max:5},on:{change:e.onInputChange},model:{value:e.config.retryCount,callback:function(t){e.$set(e.config,"retryCount",t)},expression:"config.retryCount"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("发布失败时的重试次数")])],1),t("el-form-item",{attrs:{label:"脚本控制"}},[t("el-button",{attrs:{type:"danger",size:"small",disabled:!e.isScriptRunning,icon:"el-icon-video-pause"},on:{click:e.stopScript}},[e._v(" 停止脚本 ")]),e.isScriptRunning?t("span",{staticStyle:{"margin-left":"10px",color:"#67C23A","font-size":"12px"}},[e._v(" 脚本正在执行中... ")]):"正在停止..."===e.currentStatus?t("span",{staticStyle:{"margin-left":"10px",color:"#E6A23C","font-size":"12px"}},[e._v(" 正在停止脚本... ")]):e.isScriptCompleted?t("span",{staticStyle:{"margin-left":"10px",color:"#409EFF","font-size":"12px"}},[e._v(" 脚本执行完成 ")]):"已停止"===e.currentStatus?t("span",{staticStyle:{"margin-left":"10px",color:"#F56C6C","font-size":"12px"}},[e._v(" 脚本已停止 ")]):e._e()],1)],1),t("div",{staticClass:"realtime-status-section"},[t("h4",[e._v("📊 实时状态")]),t("div",{staticClass:"status-grid"},[t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("已发布视频数：")]),t("span",{staticClass:"status-value"},[e._v(e._s(e.publishedVideoCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("总视频数：")]),t("span",{staticClass:"status-value"},[e._v(e._s(e.totalVideoCount))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("当前步骤：")]),t("span",{staticClass:"status-value"},[e._v(e._s(e.currentStep))])]),t("div",{staticClass:"status-item"},[t("span",{staticClass:"status-label"},[e._v("当前状态：")]),t("span",{staticClass:"status-value",class:e.getStatusClass(e.currentStatus)},[e._v(e._s(e.currentStatus))])]),e.errorMessage?t("div",{staticClass:"status-item error"},[t("span",{staticClass:"status-label"},[e._v("错误信息：")]),t("span",{staticClass:"status-value"},[e._v(e._s(e.errorMessage))])]):e._e()]),t("div",{staticClass:"progress-section"},[t("el-progress",{attrs:{percentage:e.getProgressPercentage(),status:e.getProgressStatus(),"show-text":!0}})],1),t("div",{staticClass:"transfer-progress-section"},[t("h4",[e._v("📤 视频传输进度")]),t("div",{staticClass:"transfer-info"},[t("div",{staticClass:"transfer-item"},[t("span",{staticClass:"transfer-label"},[e._v("当前视频：")]),t("span",{staticClass:"transfer-value"},[e._v(e._s(e.videoTransferProgress.currentVideoName||"准备中..."))])]),t("div",{staticClass:"transfer-item"},[t("span",{staticClass:"transfer-label"},[e._v("传输状态：")]),t("span",{staticClass:"transfer-value"},[t("el-tag",{attrs:{type:"completed"===e.videoTransferProgress.status?"success":"failed"===e.videoTransferProgress.status?"danger":"transferring"===e.videoTransferProgress.status?"warning":"info",size:"mini"}},[e._v(" "+e._s("completed"===e.videoTransferProgress.status?"传输完成":"failed"===e.videoTransferProgress.status?"传输失败":"transferring"===e.videoTransferProgress.status?"传输中":"准备中")+" ")]),t("span",{staticStyle:{"margin-left":"10px","font-size":"12px",color:"#999"}},[e._v(" (状态值: "+e._s(e.videoTransferProgress.status)+") ")])],1)]),t("div",{staticClass:"transfer-item"},[t("span",{staticClass:"transfer-label"},[e._v("设备ID：")]),t("span",{staticClass:"transfer-value"},[e._v(e._s(e.videoTransferProgress.deviceId||"未设置"))])])]),e.downloadProgress.isDownloading?t("div",{staticClass:"download-progress",staticStyle:{"margin-bottom":"20px"}},[t("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"8px"}},[t("span",[e._v("📥 下载进度")]),t("span",[e._v(e._s(e.downloadProgress.percentage)+"%")])]),t("el-progress",{attrs:{percentage:e.downloadProgress.percentage,"show-text":!1,"stroke-width":"12",color:"#67C23A"}}),t("div",{staticClass:"progress-details"},[t("span",{staticClass:"size-info"},[e._v(e._s(e.downloadProgress.downloadedSize)+" / "+e._s(e.downloadProgress.totalSize))]),t("span",{staticClass:"speed-info"},[e._v(e._s(e.downloadProgress.speed))]),t("span",{staticClass:"time-info"},[e._v(e._s(e.downloadProgress.message))])])],1):e._e(),t("div",{staticClass:"video-progress"},[t("el-progress",{attrs:{percentage:e.videoTransferProgress.currentVideoProgress,"show-text":!0,"stroke-width":"12",status:"failed"===e.videoTransferProgress.status?"exception":"completed"===e.videoTransferProgress.status?"success":null}}),t("div",{staticClass:"progress-details"},[t("span",{staticClass:"size-info"},[e._v(" "+e._s(e.formatFileSize(e.videoTransferProgress.currentVideoSize))+" / "+e._s(e.formatFileSize(e.videoTransferProgress.currentVideoTotalSize))+" ")]),e.videoTransferProgress.transferSpeed>0?t("span",{staticClass:"speed-info"},[e._v(" "+e._s(e.formatFileSize(e.videoTransferProgress.transferSpeed))+"/s ")]):e._e(),e.videoTransferProgress.estimatedTime>0?t("span",{staticClass:"time-info"},[e._v(" 剩余 "+e._s(e.formatTime(e.videoTransferProgress.estimatedTime))+" ")]):e._e()])],1),t("div",{staticClass:"transfer-tips"},[0===e.videoTransferProgress.currentVideoProgress&&"pending"===e.videoTransferProgress.status?t("el-alert",{attrs:{title:"正在准备传输...",type:"info",closable:!1,"show-icon":""}},[t("template",{slot:"description"},[e._v(" 大文件传输可能需要较长时间，请耐心等待。如果长时间无进度，请检查网络连接。 ")])],2):e._e(),"failed"===e.videoTransferProgress.status?t("el-alert",{attrs:{title:"传输失败",type:"error",closable:!1,"show-icon":""}},[t("template",{slot:"description"},[e._v(" 视频传输失败，请检查网络连接和设备状态后重试。 ")])],2):e._e()],1)])]),t("el-alert",{staticStyle:{"margin-top":"20px"},attrs:{title:"使用提醒",type:"info",closable:!1,"show-icon":""}},[t("div",[e._v(" • 请确保小红书应用已安装并已登录账号"),t("br"),e._v(" • 视频文件将自动传输到手机设备"),t("br"),e._v(" • 建议选择高质量的视频内容以提高发布成功率"),t("br"),e._v(" • 发布过程中请勿手动操作手机"),t("br"),e._v(" • 大量发布时请注意平台限制，避免账号异常 ")])])],1),t("VideoListDialog",{on:{"videos-uploaded":e.handleVideosUploaded,"show-transfer-info":e.showVideoTransferInfo},model:{value:e.videoListDialogVisible,callback:function(t){e.videoListDialogVisible=t},expression:"videoListDialogVisible"}}),t("VideoSelectionDialog",{attrs:{"pre-selected-videos":e.selectedVideos},on:{"videos-selected":e.handleVideosSelected,"show-transfer-info":e.showVideoTransferInfo},model:{value:e.videoSelectionDialogVisible,callback:function(t){e.videoSelectionDialogVisible=t},expression:"videoSelectionDialogVisible"}}),t("el-dialog",{attrs:{title:e.transferInfoDialogTitle,visible:e.transferInfoDialogVisible,width:"800px","close-on-click-modal":!1},on:{"update:visible":function(t){e.transferInfoDialogVisible=t}}},[e.currentVideoTransferInfo?t("div",[t("div",{staticClass:"video-transfer-info"},[t("div",{staticClass:"video-basic-info"},[t("h4",[e._v("📹 视频基本信息")]),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"info-label"},[e._v("文件名：")]),t("span",{staticClass:"info-value"},[e._v(e._s(e.currentVideoTransferInfo.original_name))])])]),t("el-col",{attrs:{span:12}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"info-label"},[e._v("文件大小：")]),t("span",{staticClass:"info-value"},[e._v(e._s(e.formatFileSize(e.currentVideoTransferInfo.file_size)))])])]),t("el-col",{attrs:{span:12}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"info-label"},[e._v("视频时长：")]),t("span",{staticClass:"info-value"},[e._v(e._s(e.formatDuration(e.currentVideoTransferInfo.video_duration)))])])]),t("el-col",{attrs:{span:12}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"info-label"},[e._v("上传时间：")]),t("span",{staticClass:"info-value"},[e._v(e._s(e.formatDateTime(e.currentVideoTransferInfo.created_at)))])])])],1)],1),t("div",{staticClass:"transfer-statistics"},[t("h4",[e._v("📊 传输统计")]),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.transferStatistics.totalTransfers))]),t("div",{staticClass:"stat-label"},[e._v("总传输次数")])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.transferStatistics.uniqueDevices))]),t("div",{staticClass:"stat-label"},[e._v("传输设备数")])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.transferStatistics.successRate)+"%")]),t("div",{staticClass:"stat-label"},[e._v("成功率")])])])],1)],1),t("div",{staticClass:"transfer-records"},[t("h4",[e._v("📋 传输记录")]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.transferRecords,"max-height":"300"}},[t("el-table-column",{attrs:{prop:"device_name",label:"设备名称",width:"120"}}),t("el-table-column",{attrs:{prop:"device_id",label:"设备ID",width:"180"}}),t("el-table-column",{attrs:{prop:"device_ip",label:"IP地址",width:"120"}}),t("el-table-column",{attrs:{label:"传输类型",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:"manual"===s.row.transfer_type?"primary":"success",size:"mini"}},[e._v(" "+e._s("manual"===s.row.transfer_type?"手动传输":"脚本传输")+" ")])]}}],null,!1,1118882530)}),t("el-table-column",{attrs:{prop:"status",label:"传输状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:"completed"===s.row.status?"success":"failed"===s.row.status?"danger":"warning",size:"mini"}},[e._v(" "+e._s(e.getTransferStatusText(s.row.status))+" ")])]}}],null,!1,2972425199)}),t("el-table-column",{attrs:{prop:"transfer_progress",label:"进度",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.transfer_progress||0)+"% ")]}}],null,!1,685657409)}),t("el-table-column",{attrs:{prop:"transfer_speed",label:"传输速度",width:"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.transfer_speed&&s.row.transfer_speed>0?t("span",[e._v(" "+e._s(e.formatFileSize(1024*s.row.transfer_speed))+"/s ")]):t("span",[e._v("-")])]}}],null,!1,533311249)}),t("el-table-column",{attrs:{prop:"transfer_time",label:"开始时间",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatDateTime(t.row.transfer_time))+" ")]}}],null,!1,2653937919)}),t("el-table-column",{attrs:{prop:"completed_time",label:"完成时间",width:"160"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.completed_time?t("span",[e._v(" "+e._s(e.formatDateTime(s.row.completed_time))+" ")]):t("span",[e._v("-")])]}}],null,!1,1692561119)}),t("el-table-column",{attrs:{prop:"error_message",label:"备注","show-overflow-tooltip":""}})],1)],1)])]):e.allVideosTransferInfo?t("div",[t("div",{staticClass:"all-videos-transfer-info"},[t("div",{staticClass:"summary-statistics"},[t("h4",[e._v("📊 传输统计汇总")]),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:6}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.allTransferStatistics.totalVideos))]),t("div",{staticClass:"stat-label"},[e._v("视频总数")])])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.allTransferStatistics.totalTransfers))]),t("div",{staticClass:"stat-label"},[e._v("总传输次数")])])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.allTransferStatistics.uniqueDevices))]),t("div",{staticClass:"stat-label"},[e._v("传输设备数")])])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.allTransferStatistics.successRate)+"%")]),t("div",{staticClass:"stat-label"},[e._v("平均成功率")])])])],1)],1),t("div",{staticClass:"videos-transfer-list"},[t("h4",[e._v("📋 视频传输详情")]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.allVideosTransferInfo,"max-height":"400"}},[t("el-table-column",{attrs:{prop:"original_name",label:"视频名称",width:"200","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"file_size",label:"文件大小",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatFileSize(t.row.file_size))+" ")]}}])}),t("el-table-column",{attrs:{prop:"transfer_count",label:"传输次数",width:"100"}}),t("el-table-column",{attrs:{prop:"device_count",label:"设备数",width:"80"}}),t("el-table-column",{attrs:{prop:"success_rate",label:"成功率",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.success_rate)+"% ")]}}])}),t("el-table-column",{attrs:{prop:"last_transfer_time",label:"最后传输时间",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatDateTime(t.row.last_transfer_time))+" ")]}}])}),t("el-table-column",{attrs:{label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(t){return e.showVideoTransferInfo(s.row)}}},[e._v(" 查看详情 ")])]}}])})],1)],1)])]):e._e(),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.transferInfoDialogVisible=!1}}},[e._v("关闭")])],1)]),t("el-dialog",{attrs:{title:"视频传输记录",visible:e.transferRecordsDialogVisible,width:"80%","close-on-click-modal":!1},on:{"update:visible":function(t){e.transferRecordsDialogVisible=t}}},[t("div",{staticClass:"transfer-records-content"},[t("div",{staticClass:"filter-section"},[t("el-form",{attrs:{inline:!0,size:"small"}},[t("el-form-item",{attrs:{label:"视频文件名:"}},[t("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"输入视频文件名"},on:{input:e.loadTransferRecords},model:{value:e.transferRecordsFilter.videoFilename,callback:function(t){e.$set(e.transferRecordsFilter,"videoFilename",t)},expression:"transferRecordsFilter.videoFilename"}})],1),t("el-form-item",{attrs:{label:"设备ID:"}},[t("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"输入设备ID"},on:{input:e.loadTransferRecords},model:{value:e.transferRecordsFilter.deviceId,callback:function(t){e.$set(e.transferRecordsFilter,"deviceId",t)},expression:"transferRecordsFilter.deviceId"}})],1),t("el-form-item",{attrs:{label:"状态:"}},[t("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"选择状态"},on:{change:e.loadTransferRecords},model:{value:e.transferRecordsFilter.status,callback:function(t){e.$set(e.transferRecordsFilter,"status",t)},expression:"transferRecordsFilter.status"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"等待中",value:"pending"}}),t("el-option",{attrs:{label:"传输中",value:"in_progress"}}),t("el-option",{attrs:{label:"已完成",value:"completed"}}),t("el-option",{attrs:{label:"失败",value:"failed"}})],1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary",icon:"el-icon-refresh"},on:{click:e.loadTransferRecords}},[e._v("刷新")])],1)],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.transferRecordsLoading,expression:"transferRecordsLoading"}],staticStyle:{width:"100%"},attrs:{data:e.transferRecordsList,stripe:"",border:"","max-height":"400"}},[t("el-table-column",{attrs:{prop:"id",label:"记录ID",width:"80"}}),t("el-table-column",{attrs:{prop:"video_id",label:"视频ID",width:"80"}}),t("el-table-column",{attrs:{prop:"video_filename",label:"视频文件名",width:"200","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"device_id",label:"设备ID",width:"180"}}),t("el-table-column",{attrs:{prop:"device_name",label:"设备名称",width:"120"}}),t("el-table-column",{attrs:{prop:"device_ip",label:"设备IP",width:"120"}}),t("el-table-column",{attrs:{label:"传输类型",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:"manual"===s.row.transfer_type?"primary":"success",size:"mini"}},[e._v(" "+e._s("manual"===s.row.transfer_type?"手动":"脚本")+" ")])]}}])}),t("el-table-column",{attrs:{label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:"completed"===s.row.status?"success":"failed"===s.row.status?"danger":"in_progress"===s.row.status?"warning":"info",size:"mini"}},[e._v(" "+e._s("completed"===s.row.status?"已完成":"failed"===s.row.status?"失败":"in_progress"===s.row.status?"传输中":"等待中")+" ")])]}}])}),t("el-table-column",{attrs:{prop:"transfer_progress",label:"进度",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.transfer_progress||0)+"% ")]}}])}),t("el-table-column",{attrs:{prop:"file_size",label:"文件大小",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatFileSize(t.row.file_size))+" ")]}}])}),t("el-table-column",{attrs:{prop:"transfer_speed",label:"传输速度",width:"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.transfer_speed&&s.row.transfer_speed>0?t("span",[e._v(" "+e._s(e.formatFileSize(1024*s.row.transfer_speed))+"/s ")]):t("span",[e._v("-")])]}}])}),t("el-table-column",{attrs:{prop:"transfer_time",label:"开始时间",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatDateTime(t.row.transfer_time))+" ")]}}])}),t("el-table-column",{attrs:{prop:"completed_time",label:"完成时间",width:"160"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.completed_time?t("span",[e._v(" "+e._s(e.formatDateTime(s.row.completed_time))+" ")]):t("span",[e._v("-")])]}}])}),t("el-table-column",{attrs:{prop:"task_id",label:"任务ID",width:"120","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"error_message",label:"错误信息","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.error_message?t("span",{staticStyle:{color:"#F56C6C"}},[e._v(" "+e._s(s.row.error_message)+" ")]):t("span",{staticStyle:{color:"#67C23A"}},[e._v("正常")])]}}])})],1),t("div",{staticClass:"pagination-section",staticStyle:{"margin-top":"20px","text-align":"center"}},[t("el-pagination",{attrs:{"current-page":e.transferRecordsPagination.currentPage,"page-sizes":[10,20,50,100],"page-size":e.transferRecordsPagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.transferRecordsPagination.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.transferRecordsDialogVisible=!1}}},[e._v("关闭")])],1)])],1)},he=[],ge=function(){var e=this,t=e._self._c;return t("el-dialog",{staticClass:"video-selection-dialog",attrs:{title:"选择要发布的视频",visible:e.visible,width:"80%","before-close":e.handleClose},on:{"update:visible":function(t){e.visible=t}}},[t("div",{staticClass:"toolbar"},[t("div",{staticClass:"toolbar-left"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-refresh",size:"small",loading:e.loading},on:{click:e.loadVideoFiles}},[e._v(" 刷新列表 ")]),t("el-button",{attrs:{type:"warning",size:"small",disabled:0===e.selectedVideos.length},on:{click:e.clearSelection}},[e._v(" 清空选择 ")]),e.selectedVideos.length>0?t("el-tag",{attrs:{type:"warning",size:"small"}},[e._v(" 限制：每个设备只能选择一个视频 ")]):e._e()],1),t("div",{staticClass:"toolbar-right"},[t("span",{staticClass:"selection-count"},[e._v(" 已选择: "+e._s(e.selectedVideos.length)+" / "+e._s(e.filteredVideoFiles.length)+" ")]),t("el-input",{staticStyle:{width:"250px","margin-left":"15px"},attrs:{placeholder:"搜索视频文件名",size:"small"},on:{input:e.filterVideoFiles},model:{value:e.searchKeyword,callback:function(t){e.searchKeyword=t},expression:"searchKeyword"}},[t("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1)]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"video-grid"},e._l(e.filteredVideoFiles,function(s){return t("div",{key:s.id,staticClass:"video-card",class:{selected:e.isSelected(s.id)},on:{click:function(t){return e.toggleSelection(s)}}},[t("div",{staticClass:"selection-indicator"},[t("el-checkbox",{attrs:{value:e.isSelected(s.id)},on:{change:function(t){return e.toggleSelection(s)},click:function(e){e.stopPropagation()}}})],1),t("div",{staticClass:"video-thumbnail",on:{click:function(t){return t.stopPropagation(),e.previewVideo(s)}}},[s.thumbnail_path?t("img",{attrs:{src:e.getThumbnailUrl(s.thumbnail_path),alt:s.original_name},on:{error:e.handleImageError}}):t("div",{staticClass:"no-thumbnail"},[t("i",{staticClass:"el-icon-video-camera"}),t("span",[e._v("无预览")])]),s.video_duration>0?t("div",{staticClass:"duration-badge"},[e._v(" "+e._s(e.formatDuration(s.video_duration))+" ")]):e._e(),t("div",{staticClass:"play-overlay"},[t("i",{staticClass:"el-icon-video-play"})]),e.isSelected(s.id)?t("div",{staticClass:"selected-overlay"},[t("i",{staticClass:"el-icon-check"})]):e._e()]),t("div",{staticClass:"video-info"},[t("div",{staticClass:"video-title",attrs:{title:s.original_name}},[e._v(" "+e._s(s.original_name)+" ")]),t("div",{staticClass:"video-meta"},[t("span",{staticClass:"file-size"},[e._v(e._s(e.formatFileSize(s.file_size)))]),t("span",{staticClass:"video-format"},[e._v(e._s(s.video_format.toUpperCase()))]),s.video_resolution?t("span",{staticClass:"resolution"},[e._v(e._s(s.video_resolution))]):e._e()]),t("div",{staticClass:"video-stats"},[t("span",{staticClass:"upload-time"},[e._v(e._s(e.formatTime(s.upload_time)))])]),t("div",{staticClass:"video-actions"},[t("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-data-line",title:"查看传输信息"},on:{click:function(t){return t.stopPropagation(),e.showTransferInfo(s)}}},[e._v(" 传输信息 ")])],1)])])}),0),e.pagination.total>0?t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"current-page":e.pagination.page,"page-sizes":[12,24,48,96],"page-size":e.pagination.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1):e._e(),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("div",{staticClass:"footer-info"},[t("span",[e._v("已选择 "+e._s(e.selectedVideos.length)+" 个视频文件")]),e.selectedVideos.length>0?t("span",{staticClass:"total-size"},[e._v(" 总大小: "+e._s(e.formatFileSize(e.totalSelectedSize))+" ")]):e._e()]),t("div",{staticClass:"footer-actions"},[t("el-button",{on:{click:e.handleClose}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",disabled:0===e.selectedVideos.length},on:{click:e.confirmSelection}},[e._v(" 确定选择 ("+e._s(e.selectedVideos.length)+") ")])],1)]),t("el-dialog",{attrs:{title:"视频预览",visible:e.previewDialogVisible,width:"70%","append-to-body":!0},on:{"update:visible":function(t){e.previewDialogVisible=t}}},[e.currentPreviewVideo?t("div",{staticClass:"video-preview"},[t("div",{staticClass:"preview-video"},[t("video",{ref:"previewPlayer",staticStyle:{width:"100%","max-height":"400px"},attrs:{src:e.getVideoUrl(e.currentPreviewVideo.file_path),controls:"",preload:"metadata"}},[e._v(" 您的浏览器不支持视频播放 ")])]),t("div",{staticClass:"preview-info"},[t("h3",[e._v(e._s(e.currentPreviewVideo.original_name))]),t("div",{staticClass:"info-grid"},[t("div",{staticClass:"info-item"},[t("label",[e._v("文件大小:")]),t("span",[e._v(e._s(e.formatFileSize(e.currentPreviewVideo.file_size)))])]),t("div",{staticClass:"info-item"},[t("label",[e._v("视频格式:")]),t("span",[e._v(e._s(e.currentPreviewVideo.video_format.toUpperCase()))])]),t("div",{staticClass:"info-item"},[t("label",[e._v("视频时长:")]),t("span",[e._v(e._s(e.formatDuration(e.currentPreviewVideo.video_duration)))])]),t("div",{staticClass:"info-item"},[t("label",[e._v("分辨率:")]),t("span",[e._v(e._s(e.currentPreviewVideo.video_resolution||"未知"))])]),t("div",{staticClass:"info-item"},[t("label",[e._v("上传时间:")]),t("span",[e._v(e._s(e.formatTime(e.currentPreviewVideo.upload_time)))])])])])]):e._e()])],1)},pe=[],fe={name:"VideoSelectionDialog",props:{value:{type:Boolean,default:!1},preSelectedVideos:{type:Array,default:()=>[]}},data(){return{videoFiles:[],filteredVideoFiles:[],selectedVideos:[],searchKeyword:"",loading:!1,previewDialogVisible:!1,currentPreviewVideo:null,pagination:{page:1,limit:24,total:0,totalPages:0}}},computed:{visible:{get(){return this.value},set(e){this.$emit("input",e)}},totalSelectedSize(){return this.selectedVideos.reduce((e,t)=>e+t.file_size,0)}},watch:{visible(e){e&&(this.loadVideoFiles(),this.selectedVideos=[...this.preSelectedVideos])},preSelectedVideos:{handler(e){this.selectedVideos=[...e]},deep:!0}},methods:{getApiBaseUrl(){return(0,ae.getApiBaseUrl)()},async loadVideoFiles(){this.loading=!0;try{const e=await Z.A.get("/api/xiaohongshu/video-files",{params:{page:this.pagination.page,limit:this.pagination.limit,status:"active"}});e.data.success&&(this.videoFiles=e.data.data.videos,this.pagination=e.data.data.pagination,this.filterVideoFiles())}catch(e){console.error("加载视频文件列表失败:",e),this.$message.error("加载视频文件列表失败")}finally{this.loading=!1}},filterVideoFiles(){if(this.searchKeyword.trim()){const e=this.searchKeyword.toLowerCase();this.filteredVideoFiles=this.videoFiles.filter(t=>t.original_name.toLowerCase().includes(e))}else this.filteredVideoFiles=this.videoFiles},isSelected(e){return this.selectedVideos.some(t=>t.id===e)},toggleSelection(e){const t=this.selectedVideos.findIndex(t=>t.id===e.id);if(t>-1)this.selectedVideos.splice(t,1);else{if(this.selectedVideos.length>=1)return void this.$message.warning("每个设备只能选择一个视频进行发布");this.selectedVideos.push(e)}},clearSelection(){this.selectedVideos=[]},confirmSelection(){this.$emit("videos-selected",this.selectedVideos),this.handleClose()},handleClose(){this.visible=!1},handleSizeChange(e){this.pagination.limit=e,this.pagination.page=1,this.loadVideoFiles()},handleCurrentChange(e){this.pagination.page=e,this.loadVideoFiles()},handleImageError(e){try{if(e&&e.target){e.target.style.display="none";const t=e.target.parentNode;if(t){const e=t.querySelector(".no-thumbnail");e&&(e.style.display="flex")}}}catch(t){console.warn("处理图片错误时出现异常:",t)}},previewVideo(e){this.currentPreviewVideo=e,this.previewDialogVisible=!0,this.$nextTick(()=>{this.$refs.previewPlayer&&this.$refs.previewPlayer.load()})},showTransferInfo(e){if(console.log("🎬 VideoSelectionDialog: 显示传输信息",e),console.log("🎬 VideoSelectionDialog: 视频ID:",e.id,"视频名称:",e.original_name),this.$parent&&"function"===typeof this.$parent.showVideoTransferInfo)return console.log("✅ VideoSelectionDialog: 找到父组件的showVideoTransferInfo方法，直接调用"),void this.$parent.showVideoTransferInfo(e);console.log("🔍 VideoSelectionDialog: 直接调用失败，尝试事件方式"),this.$emit("show-transfer-info",e),console.log("📤 VideoSelectionDialog: 已发送show-transfer-info事件")},getVideoUrl(e){if(!e)return"";if(e.startsWith("http"))return e;if(e.startsWith("/uploads/"))return`${this.getApiBaseUrl()}${e}`;const t=e.split("/").pop()||e.split("\\").pop()||e;return`${this.getApiBaseUrl()}/uploads/videos/${t}`},getThumbnailUrl(e){return e?`${this.getApiBaseUrl()}${e}`:""},formatFileSize(e){if(0===e)return"0 B";const t=1024,s=["B","KB","MB","GB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]},formatDuration(e){if(!e)return"0:00";const t=Math.floor(e/60),s=e%60;return`${t}:${s.toString().padStart(2,"0")}`},formatTime(e){return e?new Date(e).toLocaleString("zh-CN"):"-"}}},ve=fe,me=(0,u.A)(ve,ge,pe,!1,null,"cb82907c",null),Ce=me.exports,Se={name:"VideoPublishConfig",components:{VideoListDialog:ne,VideoSelectionDialog:Ce},mixins:[c],props:{value:{type:Object,default:()=>({})},deviceId:{type:String,default:""},selectedDevices:{type:Array,default:()=>[]}},data(){return{config:{selectedApp:"",titleTemplate:"{filename}",videoDescription:"",hashtags:"",publishOptions:["allowComment","allowShare"],operationDelay:5,retryCount:2},selectedVideos:[],videoListDialogVisible:!1,videoSelectionDialogVisible:!1,isScriptRunning:!1,isScriptCompleted:!1,currentLogId:null,currentTaskId:null,publishedVideoCount:0,totalVideoCount:0,currentStep:"等待开始",currentStatus:"等待开始",errorMessage:"",socket:null,transferInfoDialogVisible:!1,transferInfoDialogTitle:"",currentVideoTransferInfo:null,allVideosTransferInfo:null,transferRecords:[],transferStatistics:{totalTransfers:0,uniqueDevices:0,successRate:0},videoTransferProgress:{isTransferring:!1,currentVideoIndex:0,totalVideos:0,currentVideoName:"",currentVideoProgress:0,currentVideoSize:0,currentVideoTotalSize:0,transferSpeed:0,estimatedTime:0,taskId:"",deviceId:"",status:"pending"},allTransferStatistics:{totalVideos:0,totalTransfers:0,uniqueDevices:0,successRate:0},transferRecordsDialogVisible:!1,transferRecordsList:[],transferRecordsLoading:!1,transferRecordsFilter:{videoFilename:"",deviceId:"",status:""},transferRecordsPagination:{currentPage:1,pageSize:20,total:0},downloadProgress:{isDownloading:!1,percentage:0,downloadedSize:"0MB",totalSize:"0MB",speed:"0MB/s",message:"准备下载..."},downloadStartTime:null}},computed:{canExecute(){return this.selectedVideos.length>0&&this.config.titleTemplate.trim()&&this.config.selectedApp},totalSelectedSize(){return this.selectedVideos.reduce((e,t)=>e+t.file_size,0)}},watch:{deviceId:{handler(e,t){console.log("📋 [VideoPublishConfig] 设备ID变化:",{oldDeviceId:t,newDeviceId:e}),e&&e!==t&&this._isMounted&&(console.log("🔄 [VideoPublishConfig] 设备ID已更新，重新恢复状态"),this.restoreProgressInfo())},immediate:!1}},mounted(){console.log("🔄 [VideoPublishConfig] 组件已挂载，开始初始化"),console.log("📋 [VideoPublishConfig] 当前设备ID:",this.deviceId),console.log("📋 [VideoPublishConfig] 传入的配置:",this.value),console.log("📊 [VideoPublishConfig] 初始状态:",{isScriptRunning:this.isScriptRunning,currentStatus:this.currentStatus,downloadProgress:this.downloadProgress,videoTransferProgress:this.videoTransferProgress}),this.initSocketConnection(),this.initTaskEventListener(),this.$nextTick(()=>{this.restoreExecutionState(),this.initConfig(),this.$nextTick(()=>{console.log("📊 [VideoPublishConfig] 最终状态:",{isScriptRunning:this.isScriptRunning,currentStatus:this.currentStatus,downloadProgress:this.downloadProgress,videoTransferProgress:this.videoTransferProgress}),console.log("✅ [VideoPublishConfig] 组件初始化完成")})})},beforeDestroy(){this.socket&&this.socket.disconnect(),this.$root.$off("xiaohongshu-task-started"),this.$root.$off("xiaohongshu-task-stopped"),this.$root.$off("xiaohongshu-script-completed"),this.$root.$off("xiaohongshu-video-publish-select-video"),this.$root.$off("xiaohongshu-video-publish-clear-video"),this.$root.$off("device-offline"),console.log("🗑️ [VideoPublishConfig] 已移除任务事件监听")},methods:{initConfig(){if(console.log("📋 [配置初始化] 开始初始化配置，设备ID:",this.deviceId),this.value&&Object.keys(this.value).length>0){const e=this.deviceId?`videoPublish_config_${this.deviceId}`:"videoPublish_config",t=localStorage.getItem(e);console.log("📋 [配置初始化] 检查配置键:",e),console.log("📋 [配置初始化] 是否有已恢复的配置:",!!t),t?console.log("📋 [配置初始化] 跳过传入配置，使用已恢复的配置"):(this.config={...this.config,...this.value},console.log("📋 [配置初始化] 使用传入的配置参数:",this.value))}else console.log("📋 [配置初始化] 没有传入的配置参数");this.emitConfigUpdate()},showVideoListDialog(){this.videoListDialogVisible=!0},showVideoSelectionDialog(){this.selectedVideos.length>=1?this.$message.warning("每个设备只能选择一个视频，请先清空当前选择"):this.videoSelectionDialogVisible=!0},handleVideosUploaded(e){this.$message.success(`成功上传 ${e.uploadCount} 个视频文件`),e.duplicateCount>0&&this.$message.warning(`其中 ${e.duplicateCount} 个重复文件已跳过`)},handleVideosSelected(e){e.length>1?(this.$message.warning("每个设备只能选择一个视频进行发布"),this.selectedVideos=[e[0]],this.totalVideoCount=1):(this.selectedVideos=e,this.totalVideoCount=e.length),this.saveVideosToStorage(),this.emitConfigUpdate(),this.selectedVideos.length>0&&this.$message.success(`已选择视频文件: ${this.selectedVideos[0].original_name}`)},clearVideoSelection(){this.selectedVideos=[],this.totalVideoCount=0,this.saveVideosToStorage(),this.emitConfigUpdate()},saveVideosToStorage(){try{if(this.deviceId){const e=`videoPublish_selectedVideos_${this.deviceId}`;localStorage.setItem(e,JSON.stringify(this.selectedVideos)),console.log(`💾 [视频保存] 设备 ${this.deviceId} 的视频选择已保存`)}else localStorage.setItem("videoPublish_selectedVideos",JSON.stringify(this.selectedVideos)),console.log("💾 [视频保存] 通用视频选择已保存")}catch(e){console.error("❌ [视频保存] 保存视频选择失败:",e)}},showTransferResults(e){const t=`\n传输任务ID: ${e.transferTaskId}\n视频数量: ${e.videoCount}\n目标设备: ${e.deviceCount}\n\n传输的视频:\n${e.videos.map(e=>`• ${e.name} (${this.formatFileSize(e.size)})`).join("\n")}\n\n设备状态:\n${e.results.map(e=>`• ${e.deviceId}: ${"success"===e.status?"✅":"❌"} ${e.message}`).join("\n")}\n      `;this.$alert(t,"视频传输结果",{confirmButtonText:"确定",type:"info"})},getThumbnailUrl(e){if(!e)return"";const{getApiBaseUrl:t}=s(9381),i=t();return`${i}${e}`},onInputChange(){this.emitConfigUpdate(),this.saveConfigToStorage()},saveConfigToStorage(){try{if(this.deviceId){const e=`videoPublish_config_${this.deviceId}`;localStorage.setItem(e,JSON.stringify(this.config)),console.log(`💾 [配置保存] 设备 ${this.deviceId} 的配置已保存`)}else localStorage.setItem("videoPublish_config",JSON.stringify(this.config)),console.log("💾 [配置保存] 通用配置已保存")}catch(e){console.error("❌ [配置保存] 保存配置失败:",e)}},onAppSelectionChange(){this.emitConfigUpdate()},emitConfigUpdate(){const e={...this.config,selectedVideoIds:this.selectedVideos.map(e=>e.id),selectedVideos:this.selectedVideos};this.$emit("input",e),this.$emit("update",e)},initSocketConnection(){this.socket=(0,l.Ay)({transports:["websocket"],upgrade:!1}),this.socket.on("connect",()=>{console.log("✅ [VideoPublishConfig] Socket连接成功")}),this.socket.on("disconnect",()=>{console.log("❌ [VideoPublishConfig] Socket连接断开")}),this.socket.on("xiaohongshu_realtime_status",e=>{console.log("🎯 [VideoPublishConfig] 收到实时状态更新:",e),this.handleRealtimeStatus(e)}),this.socket.on("xiaohongshu_video_publish_progress",e=>{e.deviceId===this.deviceId&&this.handleProgressUpdate(e)}),this.socket.on("xiaohongshu_video_publish_completed",e=>{e.deviceId===this.deviceId&&this.handleExecutionCompleted(e)}),this.socket.on("video_transfer_progress",e=>{console.log("📊 [VideoPublishConfig] 收到视频传输进度:",e),this.handleTransferProgress(e)})},handleRealtimeStatus(e){console.log("🔄 [VideoPublishConfig] 收到实时状态数据:",e),console.log("📋 [VideoPublishConfig] 当前组件taskId:",this.currentTaskId),console.log("📋 [VideoPublishConfig] 数据中的taskId:",e.taskId),!this.currentTaskId&&e.taskId&&(console.log("🔧 [VideoPublishConfig] currentTaskId为空，从实时状态数据中设置:",e.taskId),this.currentTaskId=e.taskId,this.currentLogId=e.taskId,this.isScriptRunning=!0,this.isScriptCompleted=!1,this.currentStatus="脚本正在执行中...",this.saveExecutionState()),this.currentTaskId&&e.taskId===this.currentTaskId?(console.log("✅ [VideoPublishConfig] taskId匹配，更新实时状态"),void 0!==e.publishedVideoCount&&(this.publishedVideoCount=e.publishedVideoCount),void 0!==e.totalVideoCount&&(this.totalVideoCount=e.totalVideoCount),e.currentStep&&(this.currentStep=e.currentStep),e.currentStatus&&(this.currentStatus=e.currentStatus),e.errorMessage&&(this.errorMessage=e.errorMessage),this.parseDownloadProgress(e),e.currentStatus&&("finished"===e.currentStatus?(this.isScriptRunning=!1,this.isScriptCompleted=!0,console.log("✅ [VideoPublishConfig] 脚本执行完成 - currentStatus: finished")):"error"===e.currentStatus?(this.isScriptRunning=!1,this.isScriptCompleted=!0,console.log("❌ [VideoPublishConfig] 脚本执行出错 - currentStatus: error")):console.log("🔍 [VideoPublishConfig] 中间状态，不设置完成 - currentStatus:",e.currentStatus,"currentStep:",e.currentStep)),console.log("📊 [VideoPublishConfig] 状态已更新:",{publishedVideoCount:this.publishedVideoCount,totalVideoCount:this.totalVideoCount,currentStep:this.currentStep,currentStatus:this.currentStatus,isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted}),this.saveExecutionState()):console.log("⚠️ [VideoPublishConfig] taskId不匹配，忽略状态更新")},initTaskEventListener(){console.log("🎯 [VideoPublishConfig] 初始化任务事件监听，设备ID:",this.deviceId),this.$root.$on("xiaohongshu-task-started",e=>{console.log("🚀 [VideoPublishConfig] 收到任务开始事件:",e),console.log("📋 [VideoPublishConfig] 当前设备ID:",this.deviceId),console.log("📋 [VideoPublishConfig] 事件设备ID:",e.deviceId),console.log("📋 [VideoPublishConfig] 事件功能类型:",e.functionType),e.deviceId===this.deviceId&&"videoPublish"===e.functionType?(console.log("✅ [VideoPublishConfig] 设备ID和功能类型匹配，处理任务开始事件"),this.handleTaskStarted(e)):(console.log("⚠️ [VideoPublishConfig] 设备ID不匹配或非视频发布任务，忽略事件"),console.log("📋 [VideoPublishConfig] 匹配条件:",{deviceIdMatch:e.deviceId===this.deviceId,functionTypeMatch:"videoPublish"===e.functionType,expectedDeviceId:this.deviceId,actualDeviceId:e.deviceId,expectedFunctionType:"videoPublish",actualFunctionType:e.functionType}))}),this.$root.$on("xiaohongshu-video-publish-select-video",e=>{console.log("🎬 [VideoPublishConfig] 收到视频选择事件:",e),console.log("📋 [VideoPublishConfig] 当前设备ID:",this.deviceId),console.log("📋 [VideoPublishConfig] 事件设备ID:",e.deviceId),e.deviceId===this.deviceId?(console.log("✅ [VideoPublishConfig] 设备ID匹配，处理视频选择事件"),this.handleVideoAssignmentSelect(e)):console.log("⚠️ [VideoPublishConfig] 设备ID不匹配，忽略视频选择事件")}),this.$root.$on("xiaohongshu-video-publish-clear-video",e=>{console.log("🗑️ [VideoPublishConfig] 收到视频清除事件:",e),console.log("📋 [VideoPublishConfig] 当前设备ID:",this.deviceId),console.log("📋 [VideoPublishConfig] 事件设备ID:",e.deviceId),e.deviceId===this.deviceId?(console.log("✅ [VideoPublishConfig] 设备ID匹配，清除视频选择"),this.clearVideoSelection()):console.log("⚠️ [VideoPublishConfig] 设备ID不匹配，忽略视频清除事件")}),this.$root.$on("xiaohongshu-task-stopped",e=>{console.log("[VideoPublishConfig] 收到任务停止事件:",e);const t="string"===typeof e?e:e.functionType,s=e.reason||"manual",i="videoPublish"===t&&("batch_stop"===s||!this.deviceId||e.deviceId===this.deviceId);i&&(console.log(`[VideoPublishConfig] 视频发布任务停止，原因: ${s}`),this.handleTaskStopped(e),"batch_stop"===s&&this.$message.info("视频发布功能已被批量停止"))}),this.$root.$on("xiaohongshu-script-completed",e=>{console.log("[VideoPublishConfig] 收到脚本完成事件:",e),"videoPublish"!==e.functionType||this.deviceId&&e.deviceId!==this.deviceId||(console.log("[VideoPublishConfig] 视频发布脚本完成，状态:",e.status),this.handleScriptCompleted(e))}),this.$root.$on("device-offline",e=>{e.deviceId===this.deviceId&&(console.log("[VideoPublishConfig] 当前设备离线，重置状态"),this.handleDeviceOffline())})},handleVideoAssignmentSelect(e){try{console.log("🎯 [VideoPublishConfig] 处理视频分配选择:",e),console.log("📋 [VideoPublishConfig] 当前已选视频:",this.selectedVideos);const{video:t,videoInfo:s}=e;this.selectedVideos=[t],this.totalVideoCount=1,console.log("✅ [VideoPublishConfig] 已设置新分配的视频:",t.original_name),this.saveVideosToStorage(),this.emitConfigUpdate(),this.$forceUpdate(),console.log("✅ [VideoPublishConfig] 视频分配选择完成:",t.original_name),this.$message.success(`已为设备分配视频: ${t.original_name}`)}catch(t){console.error("❌ [VideoPublishConfig] 处理视频分配选择失败:",t),this.$message.error("设置分配视频失败: "+t.message)}},handleTaskStarted(e){console.log("🎬 [VideoPublishConfig] 处理任务开始事件:",e),this.currentTaskId=e.taskId,this.currentLogId=e.logId||e.taskId,this.isScriptRunning=!0,this.isScriptCompleted=!1,this.currentStatus="任务已下发，等待设备获取脚本...",this.currentStep="等待脚本获取",this.publishedVideoCount=0,this.errorMessage="",console.log("📊 [VideoPublishConfig] 任务状态已设置:",{currentTaskId:this.currentTaskId,currentLogId:this.currentLogId,isScriptRunning:this.isScriptRunning,currentStatus:this.currentStatus}),this.saveExecutionState(),this.$message.success(`设备 ${this.deviceId} 视频发布任务已下发`)},handleTaskStopped(e){console.log("[VideoPublishConfig] 处理任务停止事件:",e),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.currentStatus="已停止",this.currentStep="任务已停止",this.errorMessage="",this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"videoPublish",stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:this.config}}),this.saveExecutionState(),console.log("[VideoPublishConfig] 任务停止处理完成，状态已更新为已停止")},handleScriptCompleted(e){console.log("[VideoPublishConfig] 处理脚本完成事件:",e),"stopped"===e.status?(console.log("[VideoPublishConfig] 视频发布脚本被手动停止"),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.currentStatus="已停止",this.currentStep="脚本已停止",this.errorMessage=""):"success"===e.status?(console.log("[VideoPublishConfig] 视频发布脚本执行成功"),this.isScriptRunning=!1,this.isScriptCompleted=!0,this.currentStatus="执行完成",this.currentStep="视频发布完成",this.errorMessage=""):"error"!==e.status&&"failed"!==e.status||(console.log("[VideoPublishConfig] 视频发布脚本执行失败"),this.isScriptRunning=!1,this.isScriptCompleted=!0,this.currentStatus="执行失败",this.currentStep="脚本出错",this.errorMessage=e.message||"脚本执行失败"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"videoPublish",stateData:{isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,config:this.config}}),this.saveExecutionState(),console.log("[VideoPublishConfig] 脚本完成处理完成，最终状态:",{isScriptRunning:this.isScriptRunning,currentStatus:this.currentStatus,currentStep:this.currentStep})},handleProgressUpdate(e){this.publishedVideoCount=e.publishedCount||0,this.currentStep=e.currentStep||"执行中",this.currentStatus=`发布视频中 (${this.publishedVideoCount}/${this.totalVideoCount})`,e.errorMessage&&(this.errorMessage=e.errorMessage)},handleExecutionCompleted(e){this.isScriptRunning=!1,this.isScriptCompleted=!0,this.publishedVideoCount=e.publishedCount||0,this.currentStatus=e.success?"发布完成":"发布失败",e.errorMessage&&(this.errorMessage=e.errorMessage),setTimeout(()=>{this.resetExecutionState()},6e4)},async transferVideosToDevice(){try{console.log("📤 [视频传输] 开始传输视频到设备:",this.deviceId),console.log("📤 [视频传输] 传输的视频:",this.selectedVideos.map(e=>({id:e.id,name:e.original_name}))),this.initTransferProgress(),this.downloadProgress={isDownloading:!1,percentage:0,downloadedSize:"0MB",totalSize:"0MB",speed:"0MB/s",message:"准备下载..."},this.downloadStartTime=null,this.videoTransferProgress.taskId=this.currentTaskId||`video_transfer_${Date.now()}`,this.videoTransferProgress.deviceId=this.deviceId;const e=this.$store.getters["auth/token"];this.startTransferProgressMonitoring();const t=await this.$http.post("/api/xiaohongshu/transfer-videos",{deviceIds:[this.deviceId],selectedVideoIds:this.selectedVideos.map(e=>e.id),selectedVideos:this.selectedVideos},{headers:{"Content-Type":"application/json",...e?{Authorization:`Bearer ${e}`}:{}}});return console.log("📤 [视频传输] 传输响应:",t.data),this.stopTransferProgressMonitoring(),t.data.success&&console.log("✅ [视频传输] 传输成功，脚本将自动上报传输状态"),t.data}catch(e){throw console.error("❌ [视频传输] 传输视频失败:",e),this.stopTransferProgressMonitoring(),console.log("❌ [视频传输] 传输失败，脚本将自动上报失败状态"),e}},async recordVideoTransfers(e="completed",t=null){try{console.log("📝 [视频传输记录] 开始记录传输记录");const s=localStorage.getItem("token");for(const i of this.selectedVideos){console.log(`📝 [视频传输记录] 记录视频 ${i.id} 传输到设备 ${this.deviceId}`);const o=await this.$http.post("/api/videos/transfer-record",{videoId:i.id,deviceId:this.deviceId,status:e,errorMessage:t},{headers:{"Content-Type":"application/json",...s?{Authorization:`Bearer ${s}`}:{}}});o.data.success?console.log(`✅ [视频传输记录] 视频 ${i.id} 传输记录保存成功`):console.error(`❌ [视频传输记录] 视频 ${i.id} 传输记录保存失败:`,o.data.message)}console.log("✅ [视频传输记录] 所有传输记录保存完成")}catch(s){console.error("❌ [视频传输记录] 记录传输失败:",s)}},initTransferProgress(){this.videoTransferProgress={isTransferring:!0,currentVideoIndex:0,totalVideos:this.selectedVideos.length,currentVideoName:this.selectedVideos[0]?.original_name||"准备中...",currentVideoProgress:0,currentVideoSize:0,currentVideoTotalSize:this.selectedVideos[0]?.file_size||0,transferSpeed:0,estimatedTime:0,status:"pending",taskId:"",deviceId:""},console.log("📊 [传输进度] 初始化传输进度:",this.videoTransferProgress)},startTransferProgressMonitoring(){console.log("📊 [传输进度] 开始监控传输进度"),console.log("📊 [传输进度] 任务ID:",this.videoTransferProgress.taskId),console.log("📊 [传输进度] 设备ID:",this.videoTransferProgress.deviceId),console.log("📊 [传输进度] WebSocket监听器已在初始化时设置"),this.transferTimeoutCheck=setTimeout(()=>{this.videoTransferProgress.isTransferring&&0===this.videoTransferProgress.currentVideoProgress&&(console.log("⚠️ [传输进度] 传输可能卡住，显示提示信息"),this.$message.warning("视频传输可能需要较长时间，请耐心等待..."))},1e4)},handleTransferProgress(e){if(console.log("📊 [传输进度] 接收到进度数据:",e),console.log("📊 [传输进度] 当前组件deviceId:",this.deviceId),console.log("📊 [传输进度] 当前videoTransferProgress.deviceId:",this.videoTransferProgress.deviceId),e.deviceId===this.deviceId){console.log("📊 [传输进度] 设备ID匹配，开始更新进度"),console.log("📊 [传输进度] 当前isTransferring状态:",this.videoTransferProgress.isTransferring),this.videoTransferProgress.isTransferring||(console.log("📊 [传输进度] 自动开始传输监控"),this.videoTransferProgress.isTransferring=!0),this.$set(this.videoTransferProgress,"deviceId",e.deviceId),this.$set(this.videoTransferProgress,"currentVideoName",e.videoName),this.$set(this.videoTransferProgress,"currentVideoProgress",parseInt(e.progress)||0),this.$set(this.videoTransferProgress,"currentVideoSize",parseInt(e.transferredBytes)||0),this.$set(this.videoTransferProgress,"currentVideoTotalSize",parseInt(e.totalBytes)||0),this.$set(this.videoTransferProgress,"transferSpeed",parseInt(e.transferSpeed)||0),this.$set(this.videoTransferProgress,"status",e.status);const t=this.videoTransferProgress.currentVideoTotalSize-this.videoTransferProgress.currentVideoSize;this.videoTransferProgress.estimatedTime=this.videoTransferProgress.transferSpeed>0?t/this.videoTransferProgress.transferSpeed:0,console.log("📊 [传输进度] 进度已更新:",{progress:e.progress+"%",speed:this.formatFileSize(e.transferSpeed)+"/s",status:e.status,currentVideoName:e.videoName,deviceId:e.deviceId}),console.log("📊 [传输进度] 当前videoTransferProgress状态:",JSON.stringify(this.videoTransferProgress,null,2)),this.$forceUpdate(),"completed"!==e.status&&"failed"!==e.status||(console.log("📊 [传输进度] 传输结束，状态:",e.status),setTimeout(()=>{this.stopTransferProgressMonitoring()},5e3))}else console.log("📊 [传输进度] 设备ID不匹配，忽略进度数据"),console.log("📊 [传输进度] 期望设备ID:",this.deviceId),console.log("📊 [传输进度] 接收到设备ID:",e.deviceId)},stopTransferProgressMonitoring(){console.log("📊 [传输进度] 停止监控传输进度"),this.transferTimeoutCheck&&(clearTimeout(this.transferTimeoutCheck),this.transferTimeoutCheck=null),console.log("📊 [传输进度] 保持WebSocket监听器，停止当前传输监控"),this.videoTransferProgress.isTransferring=!1,console.log("📊 [传输进度] 传输监控已停止，最终状态:",{status:this.videoTransferProgress.status,progress:this.videoTransferProgress.currentVideoProgress+"%",videoName:this.videoTransferProgress.currentVideoName})},restoreExecutionState(){try{if(console.log("🔄 [状态恢复] 检查设备执行状态，设备ID:",this.deviceId),!this.deviceId)return void console.log("⚠️ [状态恢复] 设备ID为空，跳过状态恢复");const e=localStorage.getItem(`videoPublish_executionState_${this.deviceId}`);if(e){const t=JSON.parse(e);console.log("📋 [状态恢复] 找到保存的执行状态:",t);const s=Date.now()-t.timestamp;s<72e5?(console.log("✅ [状态恢复] 执行状态仍然有效，恢复状态"),this.isScriptRunning=t.isScriptRunning||!1,this.isScriptCompleted=t.isScriptCompleted||!1,this.currentTaskId=t.currentTaskId||null,this.currentLogId=t.currentLogId||null,this.isScriptRunning?(this.currentStatus=t.currentStatus||"等待开始",this.currentStep=t.currentStep||"等待开始",this.publishedVideoCount=t.publishedVideoCount||0,this.totalVideoCount=t.totalVideoCount||0,this.errorMessage=t.errorMessage||"",console.log("🔄 [状态恢复] 脚本正在执行中，恢复执行状态并检查状态"),this.checkExecutionLogStatus()):(this.currentStatus="等待开始",this.currentStep="等待开始",this.publishedVideoCount=0,this.totalVideoCount=0,this.errorMessage="",console.log("📊 [状态恢复] 脚本未运行，已重置显示状态为初始值")),console.log("📊 [状态恢复] 执行状态已恢复:",{isScriptRunning:this.isScriptRunning,currentStatus:this.currentStatus,currentTaskId:this.currentTaskId,currentLogId:this.currentLogId})):(console.log("⏰ [状态恢复] 执行状态已过期，清除保存的状态"),this.clearDeviceExecutionState())}else console.log("📋 [状态恢复] 没有找到保存的执行状态");this.restoreProgressInfo();const t=localStorage.getItem(`videoPublish_config_${this.deviceId}`);if(t){const e=JSON.parse(t);console.log("📋 [状态恢复] 找到保存的配置:",e),console.log("📋 [状态恢复] 恢复前的配置:",this.config),this.config={...this.config,...e},console.log("✅ [状态恢复] 配置参数已恢复:",this.config)}else console.log("📋 [状态恢复] 没有找到保存的配置，键名:",`videoPublish_config_${this.deviceId}`);const s=localStorage.getItem(`videoPublish_selectedVideos_${this.deviceId}`);if(s){const e=JSON.parse(s);console.log("📋 [状态恢复] 找到保存的视频选择:",e),console.log("📋 [状态恢复] 恢复前的视频选择:",this.selectedVideos),this.selectedVideos=e,this.totalVideoCount=e.length,console.log("✅ [状态恢复] 视频选择已恢复:",this.selectedVideos)}else console.log("📋 [状态恢复] 没有找到保存的视频选择，键名:",`videoPublish_selectedVideos_${this.deviceId}`);this.$nextTick(()=>{this.emitConfigUpdate(),console.log("✅ [状态恢复] 配置更新事件已触发")})}catch(e){console.error("❌ [状态恢复] 恢复状态失败:",e),localStorage.removeItem("videoPublish_executionState"),localStorage.removeItem("videoPublish_config"),localStorage.removeItem("videoPublish_selectedVideos")}},saveExecutionState(){try{const e={deviceId:this.deviceId,isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,currentTaskId:this.currentTaskId,currentLogId:this.currentLogId,currentStatus:this.currentStatus,currentStep:this.currentStep,publishedVideoCount:this.publishedVideoCount,totalVideoCount:this.totalVideoCount,errorMessage:this.errorMessage,timestamp:Date.now()};localStorage.setItem(`videoPublish_executionState_${this.deviceId}`,JSON.stringify(e)),localStorage.setItem(`videoPublish_config_${this.deviceId}`,JSON.stringify(this.config)),localStorage.setItem(`videoPublish_selectedVideos_${this.deviceId}`,JSON.stringify(this.selectedVideos));const t={...this.videoTransferProgress,timestamp:Date.now()};localStorage.setItem(`videoPublish_transferProgress_${this.deviceId}`,JSON.stringify(t));const s={...this.downloadProgress,timestamp:Date.now()};localStorage.setItem(`videoPublish_downloadProgress_${this.deviceId}`,JSON.stringify(s)),console.log("💾 [状态保存] 执行状态、配置参数、视频选择、传输进度和下载进度已保存")}catch(e){console.error("❌ [状态保存] 保存状态失败:",e)}},clearExecutionState(){try{if(this.deviceId)this.clearDeviceExecutionState();else{const e=Object.keys(localStorage);e.forEach(e=>{e.startsWith("videoPublish_")&&localStorage.removeItem(e)}),console.log("🗑️ [状态清除] 所有视频发布相关状态已清除")}}catch(e){console.error("❌ [状态清除] 清除状态失败:",e)}},clearDeviceExecutionState(){try{if(!this.deviceId)return void console.warn("⚠️ [设备状态清除] 设备ID为空，无法清除");localStorage.removeItem(`videoPublish_executionState_${this.deviceId}`),localStorage.removeItem(`videoPublish_config_${this.deviceId}`),localStorage.removeItem(`videoPublish_selectedVideos_${this.deviceId}`),localStorage.removeItem(`videoPublish_transferProgress_${this.deviceId}`),localStorage.removeItem(`videoPublish_downloadProgress_${this.deviceId}`),console.log(`🗑️ [设备状态清除] 设备 ${this.deviceId} 的所有状态已清除`)}catch(e){console.error("❌ [设备状态清除] 清除设备状态失败:",e)}},async stopScript(){if(console.log("[VideoPublishConfig] 停止脚本开始"),console.log("[VideoPublishConfig] 当前状态:",{deviceId:this.deviceId,currentLogId:this.currentLogId,currentTaskId:this.currentTaskId,isScriptRunning:this.isScriptRunning}),!this.deviceId)return console.error("[VideoPublishConfig] 设备ID为空，无法停止脚本"),void this.$message.error("设备ID为空，无法停止脚本");this.currentLogId||this.currentTaskId||console.warn("[VideoPublishConfig] logId和taskId都为空，但仍尝试停止"),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.currentStatus="正在停止...",this.currentStep="发送停止命令",this.errorMessage="",this.saveExecutionState();try{const e={taskId:this.currentTaskId,deviceId:this.deviceId,logId:this.currentLogId};console.log("[VideoPublishConfig] 直接调用停止API，参数:",e),console.log("[VideoPublishConfig] API URL: /api/xiaohongshu/stop");const t=await this.$http.post("/api/xiaohongshu/stop",e);t.data.success?(console.log("[VideoPublishConfig] 停止命令发送成功"),this.$message.success("停止命令已发送"),this.currentStatus="正在停止...",this.currentStep="脚本正在停止"):(console.error("[VideoPublishConfig] 停止命令发送失败:",t.data.message),this.$message.error(t.data.message||"停止失败"),this.isScriptRunning=!0,this.currentStatus="执行中")}catch(e){console.error("[VideoPublishConfig] 停止脚本失败:",e),this.$message.error("停止失败: "+(e.response?.data?.message||e.message)),this.isScriptRunning=!0,this.currentStatus="执行中"}this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:"videoPublish",stateData:{isScriptRunning:this.isScriptRunning,isScriptCompleted:!1,config:this.config}}),this.saveExecutionState()},resetExecutionState(){console.log("[VideoPublishConfig] 重置执行状态"),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.currentStatus="等待开始",this.currentStep="等待开始",this.publishedVideoCount=0,this.errorMessage="",this.currentLogId=null,this.currentTaskId=null,this.videoTransferProgress={isTransferring:!1,currentVideoIndex:0,totalVideos:0,currentVideoName:"",currentVideoProgress:0,currentVideoSize:0,currentVideoTotalSize:0,transferSpeed:0,estimatedTime:0,taskId:"",deviceId:"",status:"pending"},this.downloadProgress={isDownloading:!1,percentage:0,downloadedSize:"0MB",totalSize:"0MB",speed:"0MB/s",message:"准备下载..."},this.clearDeviceExecutionState(),console.log("[VideoPublishConfig] 执行状态重置完成")},restoreProgressInfo(){if(console.log("📊 [进度恢复] 开始恢复进度信息，设备ID:",this.deviceId),!this.deviceId)return void console.log("⚠️ [进度恢复] 设备ID为空，跳过进度恢复");const e=localStorage.getItem(`videoPublish_transferProgress_${this.deviceId}`);if(e)try{const t=JSON.parse(e);console.log("📋 [进度恢复] 找到保存的传输进度:",t);const s=Date.now()-t.timestamp;if(s<36e5){const{timestamp:e,...s}=t;this.videoTransferProgress={...this.videoTransferProgress,...s},console.log("✅ [进度恢复] 视频传输进度已恢复:",this.videoTransferProgress)}else console.log("⏰ [进度恢复] 传输进度已过期，清除保存的进度"),localStorage.removeItem(`videoPublish_transferProgress_${this.deviceId}`)}catch(s){console.error("❌ [进度恢复] 恢复传输进度失败:",s)}else console.log("📋 [进度恢复] 没有找到保存的传输进度");const t=localStorage.getItem(`videoPublish_downloadProgress_${this.deviceId}`);if(t)try{const e=JSON.parse(t);console.log("📋 [进度恢复] 找到保存的下载进度:",e);const s=Date.now()-e.timestamp;if(s<36e5){const{timestamp:t,...s}=e;this.downloadProgress={...this.downloadProgress,...s},console.log("✅ [进度恢复] 下载进度已恢复:",this.downloadProgress)}else console.log("⏰ [进度恢复] 下载进度已过期，清除保存的进度"),localStorage.removeItem(`videoPublish_downloadProgress_${this.deviceId}`)}catch(s){console.error("❌ [进度恢复] 恢复下载进度失败:",s)}else console.log("📋 [进度恢复] 没有找到保存的下载进度");console.log("📊 [进度恢复] 进度信息恢复完成")},async checkExecutionLogStatus(){if(console.log("[VideoPublishConfig] 检查执行日志状态"),!this.currentLogId)return console.warn("[VideoPublishConfig] 没有logId，无法检查执行日志"),void this.verifyDeviceExecutionState();try{const e=await this.$http.get(`/api/xiaohongshu/logs/${this.currentLogId}`),t=e.data.data;t&&t.status?(console.log(`[VideoPublishConfig] 执行日志状态: ${t.status}`),"completed"===t.status||"failed"===t.status?(console.log(`[VideoPublishConfig] 脚本已完成，状态: ${t.status}，重置为初始状态`),this.resetExecutionState(),this.clearDeviceExecutionState()):(console.log(`[VideoPublishConfig] 脚本仍在执行，状态: ${t.status}`),this.verifyDeviceExecutionState())):(console.warn("[VideoPublishConfig] 未找到执行日志，验证设备状态"),this.verifyDeviceExecutionState())}catch(e){console.error("[VideoPublishConfig] 检查执行日志失败:",e),this.verifyDeviceExecutionState()}},verifyDeviceExecutionState(){if(console.log("[VideoPublishConfig] 验证设备执行状态"),!this.deviceId)return void console.warn("[VideoPublishConfig] 设备ID为空，无法验证状态");const e=this.$store.getters["device/devices"],t=e.find(e=>e.device_id===this.deviceId);return t?(console.log(`[VideoPublishConfig] 设备 ${this.deviceId} 当前状态:`,t.status),"busy"!==t.status?(console.warn(`[VideoPublishConfig] 设备 ${this.deviceId} 状态为 ${t.status}，不是忙碌状态`),void("offline"===t.status?(console.warn("[VideoPublishConfig] 设备离线，重置执行状态"),this.resetExecutionState()):console.log(`[VideoPublishConfig] 设备状态为 ${t.status}，保持当前执行状态`))):(console.log(`[VideoPublishConfig] 设备 ${this.deviceId} 状态验证通过，恢复监听`),void this.startProgressMonitoring())):(console.warn(`[VideoPublishConfig] 未找到设备 ${this.deviceId}，可能已离线`),void this.handleDeviceOffline())},handleDeviceOffline(){console.log(`[VideoPublishConfig] 处理设备 ${this.deviceId} 离线`),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.currentStatus="设备离线",this.currentStep="设备已断开连接",this.errorMessage="设备已离线",this.saveExecutionState(),this.$message.warning(`设备 ${this.deviceId} 已离线，执行状态已重置`)},startProgressMonitoring(){console.log("[VideoPublishConfig] 开始进度监控"),this.socket?console.log("[VideoPublishConfig] Socket已连接，监听进度事件"):(console.log("[VideoPublishConfig] 重新初始化Socket连接"),this.initSocketConnection())},formatFileSize(e){const t=parseInt(e)||0;if(0===t)return"0 B";const s=1024,i=["B","KB","MB","GB"],o=Math.floor(Math.log(t)/Math.log(s));return parseFloat((t/Math.pow(s,o)).toFixed(2))+" "+i[o]},formatTime(e){return e?new Date(e).toLocaleString("zh-CN"):"-"},formatDuration(e){if(!e)return"0:00";const t=Math.floor(e/60),s=e%60;return`${t}:${s.toString().padStart(2,"0")}`},getStatusClass(e){return e.includes("error")||e.includes("失败")?"status-error":e.includes("completed")||e.includes("完成")?"status-success":e.includes("warning")||e.includes("警告")?"status-warning":"status-normal"},getProgressPercentage(){return 0===this.totalVideoCount?0:Math.round(this.publishedVideoCount/this.totalVideoCount*100)},getProgressStatus(){return this.errorMessage?"exception":this.publishedVideoCount===this.totalVideoCount&&this.totalVideoCount>0?"success":null},async showVideoTransferInfo(e){try{console.log("🎯🎯🎯 [VideoPublishConfig] ===== 接收到show-transfer-info事件 ===== 🎯🎯🎯"),console.log("🔍 [VideoPublishConfig] 显示视频传输信息:",e),console.log("🔍 [VideoPublishConfig] 视频ID:",e.id,"视频名称:",e.original_name),console.log("🔍 [VideoPublishConfig] 方法被调用，开始处理..."),console.log("🔍 [VideoPublishConfig] 当前弹出框状态:",this.transferInfoDialogVisible),this.transferInfoDialogVisible=!0,this.transferInfoDialogTitle=`视频传输信息 - ${e.original_name}`,console.log("🎯 [VideoPublishConfig] 弹出框已强制显示，标题:",this.transferInfoDialogTitle),console.log("🎯 [VideoPublishConfig] 弹出框状态已设置为:",this.transferInfoDialogVisible),this.currentVideoTransferInfo=e,this.allVideosTransferInfo=null,console.log("📡 [VideoPublishConfig] 请求传输记录:",`/api/videos/${e.id}/transfer-records`);const t=localStorage.getItem("token"),s=await this.$http.get(`/api/videos/${e.id}/transfer-records`,{headers:{...t?{Authorization:`Bearer ${t}`}:{}}});console.log("📊 [VideoPublishConfig] 传输记录响应:",s.data),s.data.success?(this.transferRecords=s.data.records||[],this.transferStatistics=s.data.statistics||{totalTransfers:0,uniqueDevices:0,successRate:0},console.log("✅ [VideoPublishConfig] 传输记录设置完成:",this.transferRecords),console.log("📈 [VideoPublishConfig] 传输统计:",this.transferStatistics)):console.error("❌ [VideoPublishConfig] API返回失败:",s.data.message),console.log("🎯 [VideoPublishConfig] 准备显示弹出框，当前状态:",this.transferInfoDialogVisible),this.$nextTick(()=>{console.log("🎯 [VideoPublishConfig] nextTick - 弹出框应该已显示")})}catch(t){console.error("❌ [VideoPublishConfig] 获取视频传输信息失败:",t),this.$message.error("获取传输信息失败: "+t.message)}},async showAllVideosTransferInfo(){try{this.transferInfoDialogTitle="所有视频传输信息汇总",this.currentVideoTransferInfo=null;const e=this.selectedVideos.map(e=>e.id),t=await this.$http.post("/api/videos/transfer-summary",{videoIds:e});t.data.success&&(this.allVideosTransferInfo=t.data.videos||[],this.allTransferStatistics=t.data.statistics||{totalVideos:0,totalTransfers:0,uniqueDevices:0,successRate:0}),this.transferInfoDialogVisible=!0}catch(e){console.error("获取视频传输汇总信息失败:",e),this.$message.error("获取传输汇总信息失败: "+e.message)}},getTransferStatusText(e){const t={pending:"等待中",transferring:"传输中",in_progress:"传输中",completed:"已完成",failed:"失败",success:"成功",error:"错误"};return t[e]||e},formatDateTime(e){if(!e)return"-";const t=new Date(e);return t.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},async loadTransferRecords(){try{this.transferRecordsLoading=!0;const e=localStorage.getItem("token"),t={page:this.transferRecordsPagination.currentPage,pageSize:this.transferRecordsPagination.pageSize,videoFilename:this.transferRecordsFilter.videoFilename,deviceId:this.transferRecordsFilter.deviceId,status:this.transferRecordsFilter.status},s=await this.$http.get("/api/xiaohongshu/transfer-records",{params:t,headers:{...e?{Authorization:`Bearer ${e}`}:{}}});s.data.success?(this.transferRecordsList=s.data.data.records,this.transferRecordsPagination.total=s.data.data.total,console.log("✅ [传输记录] 加载成功，共",s.data.data.total,"条记录")):this.$message.error("加载传输记录失败: "+s.data.message)}catch(e){console.error("❌ [传输记录] 加载失败:",e),this.$message.error("加载传输记录失败: "+e.message)}finally{this.transferRecordsLoading=!1}},handleSizeChange(e){this.transferRecordsPagination.pageSize=e,this.transferRecordsPagination.currentPage=1,this.loadTransferRecords()},handleCurrentChange(e){this.transferRecordsPagination.currentPage=e,this.loadTransferRecords()},parseDownloadProgress(e){try{if("下载文件"!==e.currentStep&&"下载视频文件"!==e.currentStep&&"连接服务器"!==e.currentStep||!e.message)"下载文件"!==e.currentStep&&this.downloadProgress.isDownloading&&(this.downloadProgress.isDownloading=!1,console.log("📥 [下载进度] 隐藏下载进度条"));else{const t=e.message.match(/正在下载:\s*([0-9.]+)MB\s*\/\s*([0-9.]+)MB/);if(t){const e=parseFloat(t[1]),s=parseFloat(t[2]),i=s>0?Math.floor(e/s*100):0;this.downloadProgress={isDownloading:!0,percentage:i,downloadedSize:e.toFixed(1)+"MB",totalSize:s.toFixed(1)+"MB",speed:this.calculateDownloadSpeed(e,s),message:`正在下载: ${e.toFixed(1)}MB / ${s.toFixed(1)}MB`},console.log("📥 [下载进度] 更新下载进度:",this.downloadProgress)}}}catch(t){console.error("❌ [下载进度] 解析下载进度失败:",t)}},calculateDownloadSpeed(e,t){try{if(!this.downloadStartTime)return this.downloadStartTime=Date.now(),"计算中...";const t=(Date.now()-this.downloadStartTime)/1e3;if(t>0){const s=e/t;return s>=1?s.toFixed(1)+"MB/s":(1024*s).toFixed(0)+"KB/s"}return"计算中..."}catch(s){return"未知"}},getConfig(){console.log("📋 [VideoPublishConfig] 获取配置，设备ID:",this.deviceId),console.log("📋 [VideoPublishConfig] 当前配置:",this.config);const e={selectedApp:this.config.selectedApp,titleTemplate:this.config.titleTemplate,videoDescription:this.config.videoDescription,hashtags:this.config.hashtags,publishOptions:[...this.config.publishOptions],operationDelay:this.config.operationDelay,retryCount:this.config.retryCount};return console.log("📋 [VideoPublishConfig] 返回配置副本:",e),e},setConfig(e){console.log("📋 [VideoPublishConfig] 设置配置，设备ID:",this.deviceId),console.log("📋 [VideoPublishConfig] 接收到的配置:",e),console.log("📋 [VideoPublishConfig] 设置前的配置:",this.config),e&&(this.config={...this.config,selectedApp:e.selectedApp||this.config.selectedApp,titleTemplate:e.titleTemplate||this.config.titleTemplate,videoDescription:e.videoDescription||this.config.videoDescription,hashtags:e.hashtags||this.config.hashtags,publishOptions:e.publishOptions?[...e.publishOptions]:this.config.publishOptions,operationDelay:void 0!==e.operationDelay?e.operationDelay:this.config.operationDelay,retryCount:void 0!==e.retryCount?e.retryCount:this.config.retryCount},console.log("📋 [VideoPublishConfig] 设置后的配置:",this.config),this.$forceUpdate(),this.$nextTick(()=>{this.onInputChange(),console.log("✅ [VideoPublishConfig] 配置已更新并保存")}))}}},_e=Se,Ie=(0,u.A)(_e,ue,he,!1,null,"7d780ba2",null),be=Ie.exports,ye=s(2036),ke={name:"XiaohongshuAutomation",components:{ProfileConfig:g,SearchGroupChatConfig:S,GroupMessageConfigOriginal:$,ArticleCommentConfig:A,UidMessageConfig:R,UidFileMessageConfig:N,UidFileManager:q,VideoFileManager:de,VideoPublishConfig:be,DeviceInfo:ye.A},data(){return{selectedFunction:"",selectedDevices:[],executing:!1,functionConfig:{},showRealtimeStatus:!1,currentExecutionStatus:"等待开始",executingDevices:[],realtimeStatusData:{},deviceConfigs:{},scheduleConfig:{mode:"immediate",scheduledTime:"",interval:30},logs:[],uidAllocationConfig:null,functions:[{key:"profile",name:"修改资料",description:"修改小红书昵称和个人简介",icon:"el-icon-user"},{key:"searchGroupChat",name:"搜索加群",description:"搜索关键词群聊并自动加群发消息",icon:"el-icon-chat-dot-round"},{key:"groupMessage",name:"循环群发",description:"循环群发消息到所有群聊",icon:"el-icon-message"},{key:"articleComment",name:"文章评论",description:"搜索关键词文章并自动评论",icon:"el-icon-chat-line-round"},{key:"uidMessage",name:"手动输入UID私信",description:"手动输入UID列表并发送私信",icon:"el-icon-edit"},{key:"uidFileMessage",name:"文件上传UID私信",description:"上传UID文件并批量发送私信",icon:"el-icon-upload2"},{key:"videoPublish",name:"发布视频",description:"批量上传和发布视频到小红书",icon:"el-icon-video-camera"}],loadDevicesTimer:null,activeDeviceTab:""}},computed:{onlineDevices(){return this.$store.getters["device/onlineDevices"]},showUidFileManager(){return"uidMessage"===this.selectedFunction}},async mounted(){console.log("小红书自动化页面: mounted生命周期开始"),await this.loadDevices(),this.handleRetryExecution(),await this.restorePageState(),await this.checkAndRestoreRunningTasks(),await this.setupWebSocketListeners(),this.initGlobalEventListeners(),this.initDebugMessageListener()},beforeDestroy(){this.savePageState(),this.cleanupSocketListeners(),this.cleanupGlobalEventListeners(),this.loadDevicesTimer&&(clearTimeout(this.loadDevicesTimer),this.loadDevicesTimer=null)},methods:{async loadDevices(){await this.$store.dispatch("device/fetchDevices")},handleUidAllocationConfigChange(e){console.log("UID分配配置变化:",e),this.uidAllocationConfig=e,this.selectedDevices.forEach(t=>{this.deviceConfigs[t]&&(this.deviceConfigs[t]={...this.deviceConfigs[t],selectedFileId:e.selectedFileId,totalUidCount:e.totalUidCount,uidsPerDevice:e.uidsPerDevice})})},handleVideosUploaded(e){console.log("视频上传完成:",e);let t=`成功上传 ${e.uploadCount} 个视频文件`;e.duplicateCount>0&&(t+=`，跳过 ${e.duplicateCount} 个重复文件`),this.$message.success(t)},handleRetryExecution(){const e=this.$route.query;if("true"===e.retry){if(console.log("检测到重试执行请求:",e),e.functionType&&(this.selectedFunction=e.functionType,console.log("已设置功能类型:",this.selectedFunction)),e.deviceId&&(this.selectedDevices=[e.deviceId],console.log("已设置设备选择:",this.selectedDevices)),e.config)try{this.functionConfig=JSON.parse(e.config),console.log("已恢复配置参数:",this.functionConfig)}catch(t){console.warn("解析配置参数失败:",t)}this.$message.info(`已从执行日志恢复"${this.getFunctionTypeName(e.functionType)}"功能的配置`),this.$router.replace({name:"XiaohongshuAutomation",query:{}})}},getFunctionTypeName(e){const t={profile:"修改资料",searchGroupChat:"搜索加群",groupMessage:"循环群发",articleComment:"文章评论",uidMessage:"手动输入UID私信",uidFileMessage:"文件上传UID私信",videoPublish:"发布视频"};return t[e]||e},getFunctionRunningCount(e){try{const t=this.$store.getters["xiaohongshu/getFunctionState"](e);if(t&&t.isScriptRunning&&t.selectedDevices)return console.log(`[${e}] Vuex状态显示正在运行，设备数量:`,t.selectedDevices.length),t.selectedDevices.length;const s=this.$store.getters["device/onlineDevices"]||[],i=this.$store.getters["device/busyDevices"]||[],o=[...s,...i];let n=0;return o.forEach(t=>{const s=this.$store.getters["xiaohongshu/getDeviceTasks"](t.device_id)||[],i=s.some(s=>s.functionType===e&&("running"===s.status||"busy"===t.status));i&&n++}),console.log(`[${e}] 通过设备状态统计的运行数量:`,n),n}catch(t){return console.warn(`获取${e}运行状态失败:`,t),0}},async batchStopFunction(e){try{const t=this.getFunctionTypeName(e),s=this.getFunctionRunningCount(e);if(0===s)return void this.$message.warning(`当前没有正在执行的${t}任务`);const i=await this.$confirm(`确定要停止所有正在执行的${t}任务吗？\n当前有 ${s} 个设备正在执行此功能。`,"批量停止确认",{confirmButtonText:"确定停止",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}).catch(()=>!1);if(!i)return;console.log(`开始批量停止${e}功能的所有任务`);const o=await this.$http.post("/api/xiaohongshu/stop-by-function",{functionType:e});if(o.data.success){const{stoppedDevices:t,updatedTasks:s}=o.data.data;this.$message.success(`批量停止成功！已向 ${t} 个设备发送停止命令，更新了 ${s} 个执行记录`),console.log(`批量停止${e}成功:`,o.data.data)}else this.$message.error(`批量停止失败: ${o.data.message}`)}catch(t){console.error(`批量停止${e}失败:`,t),this.$message.error(`批量停止失败: ${t.message}`)}},debouncedLoadDevices(){this.loadDevicesTimer&&clearTimeout(this.loadDevicesTimer),this.loadDevicesTimer=setTimeout(()=>{this.loadDevices()},300)},async setupWebSocketListeners(){try{console.log("🔧 [XiaohongshuAutomation] 设置WebSocket事件监听...");const{getWebSocketManager:e}=await Promise.resolve().then(s.bind(s,6006));this.wsManager=e(),this.wsManager.isConnected||(console.log("🔧 [XiaohongshuAutomation] 等待WebSocket连接就绪..."),await new Promise(e=>{const t=()=>{this.wsManager.isConnected?e():setTimeout(t,100)};t()})),this.initSocketListeners(),this.checkSocketConnection(),console.log("✅ [XiaohongshuAutomation] WebSocket事件监听设置完成")}catch(e){console.error("❌ [XiaohongshuAutomation] WebSocket事件监听设置失败:",e),this.$message.error("WebSocket事件监听设置失败，请刷新页面重试")}},checkSocketConnection(){const e=this.$store.getters["socket/socket"];console.log("🔧 [XiaohongshuAutomation] 检查Socket连接状态"),console.log("Socket对象:",e),console.log("Socket连接状态:",e?e.connected:"无Socket对象"),console.log("Socket ID:",e?e.id:"无Socket ID"),e&&e.connected?(console.log("✅ [XiaohongshuAutomation] Socket连接正常"),console.log("Socket事件监听器数量:",Object.keys(e._callbacks||{}).length),e.emit("test_connection",{clientType:"xiaohongshu_automation",timestamp:(new Date).toISOString()}),console.log("📡 [XiaohongshuAutomation] 已发送测试连接事件")):console.warn("⚠️ [XiaohongshuAutomation] Socket对象不存在或未连接，无法建立连接")},initGlobalEventListeners(){console.log("小红书自动化页面: 初始化全局事件监听"),this.$root.$on("xiaohongshu-stop-script",this.handleStopScript),this.$root.$on("xiaohongshu-video-assignment-sync",this.handleVideoAssignmentSync)},handleStopScript(e){console.log("小红书自动化页面: 收到停止脚本事件",e);const{functionType:t,deviceId:s,logId:i,taskId:o}=e,n=this.$store.getters["socket/socket"];n?(n.emit("xiaohongshu_stop_script",{functionType:t,deviceId:s,logId:i,taskId:o}),console.log("已通过Socket发送停止脚本命令")):(console.error("Socket未连接，无法发送停止脚本命令"),this.$message.error("网络连接异常，无法停止脚本"))},async handleVideoAssignmentSync(e){try{console.log("🎬 收到视频分配同步事件:",e),this.selectedFunction="videoPublish",console.log("✅ 已切换到视频发布功能"),this.selectedDevices=[...e.selectedDevices],console.log("✅ 已选中分配的设备:",this.selectedDevices),await this.$nextTick();const t=e.deviceVideoAssignments;console.log("🗑️ 开始清除所有设备的视频选择");for(const e of this.selectedDevices)this.$root.$emit("xiaohongshu-video-publish-clear-video",{deviceId:e});setTimeout(()=>{for(const e of this.selectedDevices)if(t[e]){const s=t[e];console.log(`🎯 为设备 ${e} 配置视频:`,s.assignedVideo),this.deviceConfigs[e]||(this.deviceConfigs[e]={title:"",description:"",hashtags:"",publishOptions:["allowComment","allowShare"],operationDelay:5,retryCount:2}),this.$root.$emit("xiaohongshu-video-publish-select-video",{deviceId:e,video:s.assignedVideo,videoInfo:s.videoInfo})}},100),this.savePageState(),this.$message.success(`视频分配同步完成！已为 ${this.selectedDevices.length} 个设备配置对应视频`),console.log("✅ 视频分配同步处理完成")}catch(t){console.error("❌ 处理视频分配同步失败:",t),this.$message.error("同步视频分配失败: "+t.message)}},initSocketListeners(){const e=this.$store.getters["socket/socket"];console.log("小红书自动化页面: 检查Socket连接状态",e?"已连接":"未连接"),e?(e.on("device_status_changed",this.handleDeviceStatusChanged),e.on("device_offline",this.handleDeviceOffline),e.on("device_status_update",this.handleDeviceStatusUpdate),e.on("xiaohongshu_all_tasks_stopped",this.handleAllTasksStopped),e.on("xiaohongshu_function_tasks_stopped",this.handleFunctionTasksStopped),e.on("xiaohongshu_task_update",this.handleTaskUpdate),e.on("xiaohongshu_status_update",this.handleScriptStatusUpdate),e.on("xiaohongshu_debug_log",this.handleDebugLog),e.on("xiaohongshu_execution_completed",e=>{console.log("🎯 [XiaohongshuAutomation] Socket收到脚本执行完成事件:",e),this.handleExecutionCompleted(e)}),e.on("xiaohongshu_script_completed",this.handleScriptCompleted),e.on("xiaohongshu_task_started",this.handleTaskStartedFromBackend),e.on("xiaohongshu_realtime_status",this.handleMainRealtimeStatus),e.on("server_prepare_shutdown",e=>{console.log("📢 [XiaohongshuAutomation] Socket收到服务器准备关闭事件:",e),this.handleServerPrepareShutdown(e)}),e.on("server_shutdown",e=>{console.log("📢 [XiaohongshuAutomation] Socket收到服务器关闭事件:",e),this.handleServerShutdown(e)}),e.on("device_script_status",e=>{console.log("🛑 [XiaohongshuAutomation] Socket收到设备脚本状态事件:",e),this.handleDeviceScriptStatus(e)}),e.on("device_app_status",e=>{console.log("📱 [XiaohongshuAutomation] Socket收到设备应用状态事件:",e),this.handleDeviceAppStatus(e)}),e.on("disconnect",this.handleSocketDisconnect),e.on("xiaohongshu_script_reset",this.handleScriptReset),e.on("xiaohongshu_vuex_state_update",this.handleVuexStateUpdate),e.on("xiaohongshu_force_refresh_vuex",this.handleForceRefreshVuex),e.on("test_event",e=>{console.log("🧪 收到测试事件:",e)}),console.log("✅ [XiaohongshuAutomation] Socket事件监听已初始化"),console.log("📋 [XiaohongshuAutomation] 监听的事件包括:",["device_offline","device_status_update","xiaohongshu_all_tasks_stopped","xiaohongshu_task_update","xiaohongshu_status_update","xiaohongshu_debug_log","xiaohongshu_execution_completed","xiaohongshu_script_reset","xiaohongshu_vuex_state_update","xiaohongshu_force_refresh_vuex","test_event"]),console.log("📡 [XiaohongshuAutomation] 使用WebSocketManager的全局客户端连接"),setTimeout(()=>{console.log("🧪 [XiaohongshuAutomation] 发送测试事件到服务器"),e.emit("client_test",{message:"来自小红书自动化页面的测试消息",timestamp:(new Date).toISOString()})},1e3)):console.warn("⚠️ [XiaohongshuAutomation] Socket未连接，无法初始化事件监听")},cleanupSocketListeners(){const e=this.$store.getters["socket/socket"];e&&(e.off("device_status_changed",this.handleDeviceStatusChanged),e.off("device_offline",this.handleDeviceOffline),e.off("device_status_update",this.handleDeviceStatusUpdate),e.off("xiaohongshu_all_tasks_stopped",this.handleAllTasksStopped),e.off("xiaohongshu_function_tasks_stopped",this.handleFunctionTasksStopped),e.off("xiaohongshu_task_update",this.handleTaskUpdate),e.off("xiaohongshu_status_update",this.handleScriptStatusUpdate),e.off("xiaohongshu_debug_log",this.handleDebugLog),e.off("xiaohongshu_execution_completed"),e.off("xiaohongshu_script_completed",this.handleScriptCompleted),e.off("xiaohongshu_realtime_status",this.handleMainRealtimeStatus),e.off("server_prepare_shutdown"),e.off("server_shutdown"),e.off("device_script_status"),e.off("device_app_status"),e.off("disconnect",this.handleSocketDisconnect),console.log("小红书自动化页面: Socket事件监听已清理"))},cleanupGlobalEventListeners(){console.log("小红书自动化页面: 清理全局事件监听"),this.$root.$off("xiaohongshu-stop-script",this.handleStopScript),this.$root.$off("xiaohongshu-video-assignment-sync",this.handleVideoAssignmentSync)},handleDeviceStatusChanged(e){console.log("🟡 [XiaohongshuAutomation] 收到设备状态变化事件:",e);const{type:t,deviceId:s,status:i}=e;s&&i&&this.$store.dispatch("device/updateDeviceStatus",{deviceId:s,status:i,...e}),"device_disconnected"===t&&(console.log("🟡 [XiaohongshuAutomation] 设备断开连接，调用handleDeviceDisconnected"),this.handleDeviceDisconnected(s))},async handleDeviceOffline(e){console.log("🔴 [XiaohongshuAutomation] 收到设备离线事件:",e);const{deviceId:t}=e;console.log("🔴 [XiaohongshuAutomation] 处理设备离线，deviceId:",t),console.log("🔴 [XiaohongshuAutomation] 当前选中设备:",this.selectedDevices),console.log("🔴 [XiaohongshuAutomation] 当前设备配置:",Object.keys(this.deviceConfigs)),await this.stopDeviceScriptsAndUpdateStatus(t),this.handleDeviceDisconnected(t)},handleDeviceDisconnected(e){console.log(`🔴 [XiaohongshuAutomation] 处理设备断开连接: ${e}`),console.log("🔴 [XiaohongshuAutomation] 当前选中设备列表:",this.selectedDevices),console.log("🔴 [XiaohongshuAutomation] 当前设备配置键:",Object.keys(this.deviceConfigs));let t=e,s=this.selectedDevices.indexOf(e);if(-1===s){console.log("🔴 [XiaohongshuAutomation] 直接匹配失败，尝试其他格式");const i=this.$store.getters["device/devices"]||[];console.log("🔴 [XiaohongshuAutomation] 所有设备:",i.map(e=>({id:e.device_id,name:e.device_name})));const o=i.find(t=>t.device_id===e||t.device_name===e||t.ip_address&&e.includes(t.ip_address.replace(/\./g,"_")));o&&(t=o.device_id,s=this.selectedDevices.indexOf(t),console.log(`🔴 [XiaohongshuAutomation] 通过匹配找到设备: ${t}`))}console.log(`🔴 [XiaohongshuAutomation] 最终使用的设备ID: ${t}`),console.log("🔴 [XiaohongshuAutomation] 设备在选中列表中的索引:",s),-1!==s?(this.selectedDevices.splice(s,1),console.log("🔴 [XiaohongshuAutomation] 已从选中列表移除设备，新列表:",this.selectedDevices),this.addLog("系统",`设备 ${t} 已断开连接，已从选中列表移除`,"warning")):(console.log(`🔴 [XiaohongshuAutomation] 设备 ${e} 不在选中列表中`),console.log("🔴 [XiaohongshuAutomation] 选中设备详情:",this.selectedDevices)),console.log(`🔴 [XiaohongshuAutomation] 检查设备配置，deviceId: ${t}`),console.log("🔴 [XiaohongshuAutomation] 设备配置存在:",!!this.deviceConfigs[t]),this.deviceConfigs[t]?(this.$delete(this.deviceConfigs,t),console.log(`🔴 [XiaohongshuAutomation] 已清理设备 ${t} 的配置参数`),console.log("🔴 [XiaohongshuAutomation] 清理后的配置键:",Object.keys(this.deviceConfigs)),this.addLog("系统",`已清理设备 ${t} 的配置参数`,"info")):console.log(`🔴 [XiaohongshuAutomation] 设备 ${t} 没有配置参数需要清理`),this.activeDeviceTab===t&&(this.selectedDevices.length>0?(this.activeDeviceTab=this.selectedDevices[0],console.log(`活跃标签页已切换到: ${this.activeDeviceTab}`)):(this.activeDeviceTab="",console.log("已清空活跃标签页"))),console.log(`🔴 [XiaohongshuAutomation] 开始清理设备执行状态: ${t}`),this.clearDeviceExecutionState(t),console.log("🔴 [XiaohongshuAutomation] 强制清理Vuex中设备的功能状态"),this.forceCleanDeviceFromVuex(t),this.$root.$emit("device-offline",{deviceId:t}),this.$forceUpdate(),this.debouncedLoadDevices(),console.log("🔴 [XiaohongshuAutomation] 设备离线处理完成，当前状态:"),console.log("🔴 [XiaohongshuAutomation] - 选中设备:",this.selectedDevices),console.log("🔴 [XiaohongshuAutomation] - 设备配置:",Object.keys(this.deviceConfigs)),console.log("🔴 [XiaohongshuAutomation] - 活跃标签:",this.activeDeviceTab)},handleServerShutdown(e){console.log("🔴 [XiaohongshuAutomation] 收到服务器关闭事件:",e),this.$message.error("服务器即将关闭"),this.addLog("系统","服务器即将关闭","error"),console.log("🔴 [XiaohongshuAutomation] 服务器关闭事件处理完成")},handleDeviceScriptStatus(e){console.log("🛑 [XiaohongshuAutomation] 收到设备脚本状态:",e),"script_stopped"===e.status&&(console.log(`🛑 [XiaohongshuAutomation] 设备 ${e.deviceId} 脚本已停止`),this.addLog("系统",`设备 ${e.deviceId} 脚本已停止: ${e.reason}`,"warning"),this.$message.warning(`设备脚本已停止: ${e.reason}`))},handleDeviceAppStatus(e){console.log("📱 [XiaohongshuAutomation] 收到设备应用状态:",e),"app_closed"===e.status&&(console.log(`📱 [XiaohongshuAutomation] 设备 ${e.deviceId} 小红书应用已关闭`),this.addLog("系统",`设备 ${e.deviceId} 小红书应用已关闭: ${e.reason}`,"info"),this.$message.info(`设备应用已关闭: ${e.reason}`))},handleServerPrepareShutdown(e){console.log("🔄 [XiaohongshuAutomation] 收到服务器准备关闭事件:",e),console.log("🔄 [XiaohongshuAutomation] 开始重置前端状态");const t=[...this.selectedDevices];console.log("🔄 [XiaohongshuAutomation] 需要清理的设备:",t),t.forEach(e=>{console.log(`🔄 [XiaohongshuAutomation] 清理设备: ${e}`),this.handleDeviceDisconnected(e)}),this.selectedDevices=[],this.deviceConfigs={},this.activeDeviceTab="",this.$store.dispatch("xiaohongshu/resetGlobalExecutionState"),this.$root.$emit("force-reset-all",{reason:"server_prepare_shutdown",timestamp:(new Date).toISOString()}),this.$forceUpdate(),this.$message.warning("服务器准备关闭，正在重置状态..."),this.addLog("系统","服务器准备关闭，已重置所有状态","warning"),console.log("🔄 [XiaohongshuAutomation] 前端状态重置完成"),console.log("🔄 [XiaohongshuAutomation] 重置后状态 - 选中设备:",this.selectedDevices),console.log("🔄 [XiaohongshuAutomation] 重置后状态 - 设备配置:",Object.keys(this.deviceConfigs))},async handleSocketDisconnect(e){console.log("🔌 [XiaohongshuAutomation] Socket连接断开:",e),console.log("🔌 [XiaohongshuAutomation] 当前选中设备:",this.selectedDevices),console.log("🔌 [XiaohongshuAutomation] 当前设备配置:",Object.keys(this.deviceConfigs)),console.log("🔌 [XiaohongshuAutomation] 当前执行状态:",this.executing),console.log("🔌 [XiaohongshuAutomation] 服务器不可用，清理所有设备状态");const t=[...this.selectedDevices];console.log("🔌 [XiaohongshuAutomation] 需要清理的设备:",t);for(const s of t)console.log(`🔌 [XiaohongshuAutomation] 停止设备脚本: ${s}`),await this.stopDeviceScriptsAndUpdateStatus(s);t.forEach(e=>{console.log(`🔌 [XiaohongshuAutomation] 清理设备: ${e}`),this.handleDeviceDisconnected(e)}),console.log("🔌 [XiaohongshuAutomation] 强制清空所有状态"),this.selectedDevices=[],this.deviceConfigs={},this.activeDeviceTab="",this.$store.dispatch("xiaohongshu/resetGlobalExecutionState"),this.$root.$emit("server-disconnect",{reason:e,timestamp:(new Date).toISOString()}),this.$message.warning("服务器连接已断开，所有设备状态已清理"),this.addLog("系统",`服务器连接断开 (${e})，已清理所有设备状态`,"warning"),console.log("🔌 [XiaohongshuAutomation] Socket断开处理完成"),console.log("🔌 [XiaohongshuAutomation] 清理后状态 - 选中设备:",this.selectedDevices),console.log("🔌 [XiaohongshuAutomation] 清理后状态 - 设备配置:",Object.keys(this.deviceConfigs))},async forceResetAllStates(){console.log("🔄 [XiaohongshuAutomation] 用户触发强制重置所有状态"),this.$confirm("确定要强制重置所有设备状态和配置吗？这将停止所有脚本并清除所有选中的设备和配置参数。","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{console.log("🔄 [XiaohongshuAutomation] 开始强制重置");const e=[...this.selectedDevices];console.log("🔄 [XiaohongshuAutomation] 需要清理的设备:",e);for(const t of e)console.log(`🔄 [XiaohongshuAutomation] 停止设备脚本: ${t}`),await this.stopDeviceScriptsAndUpdateStatus(t);e.forEach(e=>{console.log(`🔄 [XiaohongshuAutomation] 清理设备: ${e}`),this.handleDeviceDisconnected(e)}),this.selectedDevices=[],this.deviceConfigs={},this.activeDeviceTab="",this.$store.dispatch("xiaohongshu/resetGlobalExecutionState"),this.$root.$emit("force-reset-all",{timestamp:(new Date).toISOString()}),this.$forceUpdate(),this.$message.success("所有状态已强制重置"),this.addLog("系统","用户手动强制重置所有状态","info"),console.log("🔄 [XiaohongshuAutomation] 强制重置完成"),console.log("🔄 [XiaohongshuAutomation] 重置后状态 - 选中设备:",this.selectedDevices),console.log("🔄 [XiaohongshuAutomation] 重置后状态 - 设备配置:",Object.keys(this.deviceConfigs))}).catch(()=>{console.log("🔄 [XiaohongshuAutomation] 用户取消强制重置")})},async stopDeviceScriptsAndUpdateStatus(e){console.log(`🛑 [XiaohongshuAutomation] 停止设备脚本并更新状态: ${e}`),console.log(`🛑 [XiaohongshuAutomation] 原始设备ID: "${e}"`),console.log("🛑 [XiaohongshuAutomation] 设备ID类型: "+typeof e),console.log(`🛑 [XiaohongshuAutomation] 设备ID长度: ${e?e.length:"undefined"}`);try{console.log("🛑 [XiaohongshuAutomation] 调用停止设备任务API"),console.log("🛑 [XiaohongshuAutomation] API请求参数:",{deviceId:e,reason:"设备离线"});const t=await this.$http.post("/api/xiaohongshu/stop-device-tasks",{deviceId:e,reason:"设备离线"});console.log("🛑 [XiaohongshuAutomation] API响应:",t.data),t.data.success?(console.log(`🛑 [XiaohongshuAutomation] ✅ 执行日志状态更新成功，更新了 ${t.data.updatedCount} 条记录`),this.addLog("系统",`设备 ${e} 的 ${t.data.updatedCount} 个任务已停止`,"warning")):(console.error("🛑 [XiaohongshuAutomation] ❌ 执行日志状态更新失败:",t.data.message),this.addLog("系统",`停止设备任务失败: ${t.data.message}`,"error")),console.log(`🛑 [XiaohongshuAutomation] 发送停止脚本命令到设备: ${e}`);const s=this.$store.getters["socket/socket"];s&&s.connected?(s.emit("script_command",{type:"stop_script",deviceId:e,reason:"设备离线"}),console.log("🛑 [XiaohongshuAutomation] ✅ 停止脚本命令已发送")):console.log("🛑 [XiaohongshuAutomation] ❌ Socket未连接，无法发送停止脚本命令"),console.log("🛑 [XiaohongshuAutomation] 更新Vuex状态");const i=this.$store.getters["xiaohongshu/getRunningTasks"];console.log("🛑 [XiaohongshuAutomation] 当前运行任务:",i);for(const o of i)console.log(`🛑 [XiaohongshuAutomation] 停止功能 ${o} 的Vuex状态`),this.$store.dispatch("xiaohongshu/stopTask",{functionType:o,reason:"device_offline"}),this.$root.$emit("xiaohongshu-task-stopped",{functionType:o,deviceId:e,reason:"device_offline",message:"设备离线，任务已停止"});this.clearDeviceExecutionState(e),console.log(`🛑 [XiaohongshuAutomation] ✅ 设备 ${e} 脚本停止和状态更新完成`)}catch(t){console.error("🛑 [XiaohongshuAutomation] ❌ 停止设备脚本失败:",t),console.error("🛑 [XiaohongshuAutomation] 错误详情:",t.response?.data||t.message),this.addLog("系统",`停止设备脚本失败: ${t.message}`,"error")}},initDebugMessageListener(){window.addEventListener("message",e=>{e.data&&"DEBUG_GET_STATE"===e.data.type&&"debug-tool"===e.data.source&&(console.log("🔧 [XiaohongshuAutomation] 收到调试工具状态查询请求"),window.postMessage({type:"DEBUG_STATE_RESPONSE",state:{selectedFunction:this.selectedFunction,selectedDevices:this.selectedDevices,deviceConfigs:Object.keys(this.deviceConfigs),activeDeviceTab:this.activeDeviceTab,executing:this.executing,allDevices:this.$store.getters["device/devices"]?.map(e=>({id:e.device_id,name:e.device_name,status:e.status}))||[]}},"*"),console.log("🔧 [XiaohongshuAutomation] 已发送状态响应给调试工具"))})},clearDeviceExecutionState(e){console.log(`清理设备 ${e} 的执行状态`),this.$store.dispatch("xiaohongshu/clearDeviceExecutionState",e);const t=this.getAllDevices()||[],s=t.some(t=>"busy"===t.status&&t.device_id!==e);s||(console.log("没有其他设备在执行，重置全局执行状态"),this.$store.dispatch("xiaohongshu/resetGlobalExecutionState")),this.addLog("系统",`已清理设备 ${e} 的执行状态`,"info"),console.log("🔴 [XiaohongshuAutomation] ✅ 设备执行状态清理完成")},forceCleanDeviceFromVuex(e){console.log(`🔴 [XiaohongshuAutomation] 强制清理设备 ${e} 在Vuex中的状态`);const t=["profile_modification","group_search_join","group_message_loop","article_comment","uid_private_message","uid_file_message"];t.forEach(t=>{console.log(`🔴 [XiaohongshuAutomation] 检查功能 ${t} 中的设备状态`);const s=this.$store.getters["xiaohongshu/getFunctionState"](t);if(s&&s.selectedDevices&&s.selectedDevices.includes(e)){console.log(`🔴 [XiaohongshuAutomation] 在功能 ${t} 中找到设备 ${e}，开始清理`);const i=s.selectedDevices.filter(t=>t!==e);0===i.length?(console.log(`🔴 [XiaohongshuAutomation] 功能 ${t} 没有其他设备，重置功能状态`),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:t,stateData:{isScriptRunning:!1,isScriptCompleted:!1,taskId:null,config:{},selectedDevices:[],startTime:null,progress:0,status:"idle",logs:[],lastResult:null,executionStatus:"stopped",executionMessage:"设备断开连接"}})):(console.log(`🔴 [XiaohongshuAutomation] 功能 ${t} 还有其他设备，只移除断开的设备`),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:t,stateData:{...s,selectedDevices:i}})),console.log(`🔴 [XiaohongshuAutomation] ✅ 功能 ${t} 中设备 ${e} 状态清理完成`)}}),console.log(`🔴 [XiaohongshuAutomation] ✅ 设备 ${e} 在Vuex中的所有功能状态清理完成`)},handleDeviceStatusUpdate(e){console.log("🟢 [XiaohongshuAutomation] 收到设备状态更新事件:",e),e&&e.deviceId&&(this.$store.dispatch("device/updateDeviceStatus",e),"offline"===e.status&&(console.log("🟢 [XiaohongshuAutomation] 检测到设备状态变为离线，触发设备离线处理"),this.handleDeviceDisconnected(e.deviceId)),this.debouncedLoadDevices())},handleAllTasksStopped(e){console.log("🛑 [XiaohongshuAutomation] 收到所有任务停止事件:",e),this.addLog("系统","服务器通知: 所有任务已停止","warning"),this.executing=!1,this.functionConfig={},this.selectedFunction="",this.$root.$emit("xiaohongshu-task-stopped",{functionType:"all"}),console.log("🛑 [XiaohongshuAutomation] 所有任务停止处理完成，保留设备选择状态"),this.addLog("系统","所有任务已停止，执行状态已重置","info")},handleFunctionTasksStopped(e){console.log("小红书自动化页面: 收到功能批量停止事件",e);const{functionType:t,stoppedDevices:s,updatedTasks:i,deviceList:o}=e,n=this.getFunctionTypeName(t);this.addLog("系统",`服务器通知: ${n}功能的所有任务已停止 (${s}个设备)`,"warning"),this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:t,stateData:{isScriptRunning:!1,isScriptCompleted:!1,executionStatus:"stopped",executionMessage:"批量停止",progress:0,deviceStates:{}}}),this.$root.$emit("xiaohongshu-task-stopped",{functionType:t,reason:"batch_stop",stoppedDevices:s,deviceList:o}),this.addLog("系统",`${n}功能批量停止完成，已更新${i}个执行记录`,"info")},handleTaskUpdate(e){const{deviceId:t,status:s,message:i}=e;if("device_disconnected"===s){const e=this.selectedDevices.indexOf(t);return void(-1!==e&&(this.selectedDevices.splice(e,1),this.addLog("系统",`设备 ${t} 已断开连接`,"error")))}"stopped"===s&&(this.addLog("系统",`设备 ${t} 任务已停止`,"warning"),this.$root.$emit("xiaohongshu-task-stopped",{functionType:this.selectedFunction,deviceId:t}))},handleScriptStatusUpdate(e){console.log("🔥 小红书自动化页面: 收到脚本状态更新",e);const{deviceId:t,status:s,message:i,stage:o}=e;if("starting"===o||"completed"===o||"error"===o||"stopped"===o){const e="starting"===o?"等待开始":"completed"===o?"完成":"error"===o?"脚本出错":"stopped"===o?"已停止":s;if(this.addLog("系统",`设备 ${t}: ${e} - ${i}`,"脚本出错"===e?"error":"info"),"starting"===o)console.log(`=== 脚本状态更新：${o}，等待后端发送任务开始事件 ===`);else if("completed"===o){const e=i&&(i.includes("脚本执行完成")||i.includes("执行完成")||i.includes("完成"));e?(console.log(`=== 脚本执行完成：${o}，立即发送脚本完成事件 ===`),this.$root.$emit("xiaohongshu-task-stopped",{functionType:this.selectedFunction,deviceId:t}),this.addLog("系统","脚本执行完成，状态已重置","success")):(console.log(`=== 步骤完成：${o}，但不发送脚本完成事件，等待真正的脚本完成信号 ===`),setTimeout(()=>{this.$root.$emit("xiaohongshu-task-stopped",{functionType:this.selectedFunction,deviceId:t}),this.addLog("系统","状态已自动重置，可以重新执行","info")},6e4))}else"error"!==o&&"stopped"!==o||this.$root.$emit("xiaohongshu-task-stopped",{functionType:this.selectedFunction,deviceId:t})}},handleDebugLog(e){console.log("📝 小红书自动化页面: 收到调试日志",e),console.log("📝 当前时间:",(new Date).toLocaleTimeString());const{deviceId:t,message:s,level:i}=e,o=this.onlineDevices.find(e=>e.device_id===t),n=o?o.device_name:t;let a="info";"error"===i?a="error":"warning"!==i&&"warn"!==i||(a="warning"),this.addLog(`${n}`,s,a)},handleExecutionCompleted(e){console.log("🎯 [XiaohongshuAutomation] 收到脚本执行完成事件:",e),console.log("🎯 [XiaohongshuAutomation] 当前选中功能:",this.selectedFunction),console.log("🎯 [XiaohongshuAutomation] 当前选中设备:",this.selectedDevices);try{const t={profile:"修改资料",searchGroupChat:"搜索加群",groupMessage:"循环群发",articleComment:"文章评论"};let s=this.selectedFunction;if(!s){const e=this.$store.getters["xiaohongshu/getRunningTasks"];e.length>0&&(s=e[0])}if(s){console.log(`[小红书自动化] 处理 ${s} 功能的执行完成事件`),this.$store.dispatch("xiaohongshu/stopTask",{functionType:s,reason:"success"===e.status?"completed":"failed"}),this.$root.$emit("xiaohongshu-task-stopped",{functionType:s,reason:"success"===e.status?"completed":"failed",message:e.message,timestamp:e.timestamp}),"success"===e.status&&this.$root.$emit("xiaohongshu-script-completed",{functionType:s,message:e.message,timestamp:e.timestamp});const i="success"===e.status?"success":"error",o="success"===e.status?"脚本执行完成":"脚本执行失败";this.addLog("系统",`${t[s]||s}: ${o}`,i),console.log(`[小红书自动化] ${s} 功能状态已重置`)}else console.warn("[小红书自动化] 无法确定功能类型，跳过状态重置")}catch(t){console.error("[小红书自动化] 处理执行完成事件失败:",t)}},handleScriptCompleted(e){console.log("[小红书自动化] 收到脚本停止完成事件:",e);try{const{functionType:t,deviceId:s,taskId:i,status:o,message:n}=e;this.$root.$emit("xiaohongshu-script-completed",{functionType:t,deviceId:s,taskId:i,status:o,message:n}),t&&this.$store.dispatch("xiaohongshu/setFunctionState",{functionType:t,stateData:{isScriptRunning:!1,isScriptCompleted:"stopped"===o,config:{}}});const a="stopped"===o?"warning":"info",l="stopped"===o?"脚本已停止":"脚本已完成";this.addLog("系统",`${t}: ${l} - ${n}`,a),console.log(`[小红书自动化] ${t} 脚本停止事件已处理`)}catch(t){console.error("[小红书自动化] 处理脚本停止完成事件失败:",t)}},handleTaskStartedFromBackend(e){try{console.log("[小红书自动化] 收到后端任务开始事件:",e),console.log("[小红书自动化] WebSocket连接状态:",this.socket?this.socket.connected:"未连接"),this.$root.$emit("xiaohongshu-task-started",{functionType:e.functionType,deviceId:e.deviceId,taskId:e.taskId,logId:e.logId,message:e.message}),console.log("[小红书自动化] 已转发任务开始事件到组件:",{functionType:e.functionType,deviceId:e.deviceId,taskId:e.taskId,logId:e.logId}),this.addLog("系统",`任务开始: ${e.message}`,"info"),setTimeout(()=>{console.log("[小红书自动化] 延迟发送任务开始事件"),this.$root.$emit("xiaohongshu-task-started",{functionType:e.functionType,deviceId:e.deviceId,taskId:e.taskId,logId:e.logId,message:e.message,delayed:!0})},500)}catch(t){console.error("[小红书自动化] 处理后端任务开始事件失败:",t)}},restoreTaskState(e){const t=this.onlineDevices.map(e=>e.device_id),s=e.devices||[],i=s.filter(e=>t.includes(e));console.log("任务设备:",s),console.log("在线设备:",t),console.log("在线的任务设备:",i),0!==i.length?(i.length<s.length&&this.addLog("系统",`任务 ${e.id} 部分设备离线，只恢复在线设备`,"warning"),e.function&&(this.selectedFunction=e.function),this.selectedDevices=[...i],console.log("已恢复在线设备选择:",this.selectedDevices),e.config&&(this.functionConfig={...e.config},console.log("已恢复功能配置:",this.functionConfig)),this.addLog("系统",`已恢复任务状态: ${e.id}`,"success"),this.addLog("系统",`功能: ${e.function}, 设备数: ${e.devices?e.devices.length:0}`,"info")):this.addLog("系统",`任务 ${e.id} 的设备都已离线，不恢复任务状态`,"warning")},selectFunction(e){console.log("=== selectFunction 被调用 ==="),console.log("选择的功能:",e),console.log("当前功能:",this.selectedFunction),this.selectedFunction!==e?(console.log("功能发生变化，开始切换"),this.selectedFunction=e,this.functionConfig={},console.log("已清空 functionConfig:",this.functionConfig),this.selectedDevices=[],this.activeDeviceTab="",this.deviceConfigs={},console.log("[功能切换] 已清除设备选择，等待重新选择设备"),this.$nextTick(()=>{console.log("=== nextTick 中的检查 ==="),console.log("切换到功能:",e),console.log("当前 selectedFunction:",this.selectedFunction),console.log("getComponentName() 返回:",this.getComponentName()),console.log("设备配置:",this.deviceConfigs),console.log("已清空配置，等待用户输入"),this.autoRestoreExecutingDevices()})):console.log("功能未变化，跳过切换")},getCurrentFunction(){return this.functions.find(e=>e.key===this.selectedFunction)||{}},getComponentName(){if(console.log("=== getComponentName 被调用 ==="),console.log("selectedFunction:",this.selectedFunction),!this.selectedFunction)return console.log("selectedFunction 为空，返回 null"),null;const e={profile:"ProfileConfig",searchGroupChat:"SearchGroupChatConfig",groupMessage:"GroupMessageConfigOriginal",articleComment:"ArticleCommentConfig",uidMessage:"UidMessageConfig",uidFileMessage:"UidFileMessageConfig",videoPublish:"VideoPublishConfig"},t=e[this.selectedFunction]||null;return console.log("返回的组件名:",t),t},updateConfig(e){if(console.log("=== Vue主组件 updateConfig 被调用 ==="),console.log("收到的配置:",JSON.stringify(e,null,2)),console.log("当前 functionConfig:",JSON.stringify(this.functionConfig,null,2)),console.log("selectedFunction:",this.selectedFunction),e&&"object"===typeof e){const t=JSON.stringify(e),s=JSON.stringify(this.functionConfig);console.log("配置比较:",{new:t,old:s}),t!==s?(this.functionConfig={...e},console.log("配置已更新:",this.selectedFunction,e),console.log("更新后的 functionConfig:",this.functionConfig)):console.log("配置未改变，跳过更新")}else console.log("收到的配置无效:",e)},handleValidationError(e){console.warn("配置验证错误:",e)},handleValidationSuccess(){console.log("配置验证成功")},async executeFunction(){if(this.selectedFunction&&0!==this.selectedDevices.length){this.executing=!0,this.addLog("系统","开始执行小红书自动化任务...","info");try{console.log("=== Vue前端执行前的配置检查 ==="),console.log("selectedFunction:",this.selectedFunction),console.log("functionConfig:",JSON.stringify(this.functionConfig,null,2)),console.log("scheduleConfig:",this.scheduleConfig),console.log("selectedDevices:",this.selectedDevices),"groupChat"===this.selectedFunction&&(console.log("=== 群聊功能特别检查 ==="),console.log("searchKeyword:",this.functionConfig.searchKeyword),console.log("targetJoinCount:",this.functionConfig.targetJoinCount),console.log("functionConfig是否为空对象:",0===Object.keys(this.functionConfig).length));const e={functionType:this.selectedFunction,config:this.functionConfig,deviceConfigs:this.deviceConfigs,schedule:this.scheduleConfig,deviceIds:this.selectedDevices};console.log("=== Vue前端发送的完整参数 ==="),console.log(JSON.stringify(e,null,2));const t=await this.$http.post("/api/xiaohongshu/execute",e);if(!t.data.success)throw new Error(t.data.message);this.addLog("系统","任务下发成功","success"),await this.$store.dispatch("xiaohongshu/startTask",{functionType:this.selectedFunction,selectedDevices:this.selectedDevices,config:this.functionConfig,taskId:t.data.taskId||Date.now().toString()}),console.log("=== 立即发送任务开始事件，确保配置组件状态更新 ==="),this.$root.$emit("xiaohongshu-task-started",{functionType:this.selectedFunction,deviceId:this.selectedDevices[0],taskId:t.data.taskId||Date.now().toString(),logId:`${t.data.taskId||Date.now().toString()}_${this.selectedDevices[0]}`,message:`开始执行${this.getFunctionTypeName(this.selectedFunction)}任务`,immediate:!0}),console.log("=== 任务开始事件已发送 ==="),console.log("功能类型:",this.selectedFunction),console.log("设备ID:",this.selectedDevices[0]),console.log("配置参数:",this.functionConfig)}catch(e){this.addLog("系统",`执行失败: ${e.message}`,"error"),this.$message.error("执行失败: "+e.message)}finally{this.executing=!1}}else this.$message.warning("请选择功能和执行设备")},async executeForDevice(e){if(!this.selectedFunction||!e)return void this.$message.warning("请选择功能和设备");const t=this.deviceConfigs[e];if(!t||!this.isDeviceConfigValid(e))return void this.$message.warning("请完善设备配置参数");this.executing=!0;const s=this.getDeviceName(e);this.addLog("系统",`开始执行设备 ${s} 的任务...`,"info");try{console.log(`=== 执行单个设备 ${e} ===`),console.log("设备配置:",t);const i=`xiaohongshu_${this.selectedFunction}_${Date.now()}_${e}`,o={functionType:this.selectedFunction,config:t,deviceConfigs:{[e]:t},schedule:this.scheduleConfig,deviceIds:[e],taskId:i};console.log("单设备执行参数:",JSON.stringify(o,null,2));const n=await this.$http.post("/api/xiaohongshu/execute",o);if(!n.data.success)throw new Error(n.data.message);{this.addLog("系统",`设备 ${s} 任务下发成功`,"success");const o=i;await this.$store.dispatch("xiaohongshu/startTask",{functionType:this.selectedFunction,selectedDevices:[e],config:t,taskId:o});const n=`${i}_${e}`;this.$root.$emit("xiaohongshu-task-started",{functionType:this.selectedFunction,deviceId:e,taskId:n,logId:n,config:t,message:`开始执行${this.getCurrentFunction().name}任务`}),console.log(`=== 设备 ${e} 任务开始事件已发送 ===`)}}catch(i){console.error("执行单设备任务失败:",i),this.addLog("系统",`设备 ${s} 执行失败: ${i.message}`,"error"),this.$message.error(`设备 ${s} 执行失败: `+i.message)}finally{this.executing=!1}},async executeAllDevices(){if(!this.selectedFunction||0===this.selectedDevices.length)return void this.$message.warning("请选择功能和执行设备");const e=this.selectedDevices.filter(e=>this.isDeviceConfigValid(e));if(0!==e.length){this.executing=!0,this.addLog("系统",`开始批量执行 ${e.length} 个设备的任务...`,"info");try{if(console.log("=== 批量执行所有设备 ==="),console.log("有效设备:",e),console.log("设备配置:",this.deviceConfigs),"videoPublish"===this.selectedFunction){console.log("🎬 [批量执行] 检测到视频发布功能，为每个设备单独执行");const s=`xiaohongshu_videoPublish_batch_${Date.now()}`;for(const i of e)try{const e=`${s}_${i}`;console.log(`🎯 [批量执行] 设备 ${i} 生成taskId:`,e);const t={functionType:"videoPublish",config:this.deviceConfigs[i],deviceConfigs:{[i]:this.deviceConfigs[i]},schedule:this.scheduleConfig,deviceIds:[i],taskId:s};console.log(`📋 [批量执行] 设备 ${i} 执行参数:`,t);const o=await this.$http.post("/api/xiaohongshu/execute",t);if(!o.data.success)throw new Error(o.data.message);this.addLog("系统",`设备 ${this.getDeviceName(i)} 视频发布任务已启动`,"success"),await this.$store.dispatch("xiaohongshu/startTask",{functionType:"videoPublish",selectedDevices:[i],config:this.deviceConfigs[i],taskId:e,mergeDevices:!0}),this.$root.$emit("xiaohongshu-task-started",{functionType:"videoPublish",deviceId:i,taskId:e,logId:e+"_"+i,config:this.deviceConfigs[i],message:"开始执行视频发布任务"}),console.log(`✅ [批量执行] 设备 ${i} 任务开始事件已发送，taskId: ${e}`)}catch(t){console.error(`❌ [批量执行] 设备 ${i} 执行失败:`,t),this.addLog("系统",`设备 ${this.getDeviceName(i)} 执行失败: ${t.message}`,"error")}this.addLog("系统",`批量视频发布任务已全部下发，共 ${e.length} 个设备`,"success")}else{const t={};e.forEach(e=>{t[e]=this.deviceConfigs[e]});const s=`xiaohongshu_${this.selectedFunction}_batch_${Date.now()}`,i={functionType:this.selectedFunction,config:this.functionConfig,deviceConfigs:t,schedule:this.scheduleConfig,deviceIds:e,taskId:s};console.log("批量执行参数:",JSON.stringify(i,null,2));const o=await this.$http.post("/api/xiaohongshu/execute",i);if(!o.data.success)throw new Error(o.data.message);{this.addLog("系统",`批量任务下发成功，共 ${e.length} 个设备`,"success");const t=s;await this.$store.dispatch("xiaohongshu/startTask",{functionType:this.selectedFunction,selectedDevices:e,config:this.deviceConfigs[e[0]],taskId:t});for(const i of e){const e=`${s}_${i}`;this.$root.$emit("xiaohongshu-task-started",{functionType:this.selectedFunction,deviceId:i,taskId:e,logId:e,config:this.deviceConfigs[i],message:`开始执行${this.getCurrentFunction().name}任务`}),console.log(`=== 设备 ${i} 任务开始事件已发送，taskId: ${e} ===`)}}}}catch(s){console.error("批量执行任务失败:",s),this.addLog("系统",`批量执行失败: ${s.message}`,"error"),this.$message.error("批量执行失败: "+s.message)}finally{this.executing=!1}}else this.$message.warning("没有有效的设备配置，请完善配置参数")},copyConfigToAll(){if(console.log("🔧 [小红书] 开始复制配置到所有设备..."),console.log("🔧 [小红书] 当前活动设备:",this.activeDeviceTab),console.log("🔧 [小红书] 选中的设备:",this.selectedDevices),console.log("🔧 [小红书] 当前功能:",this.selectedFunction),!this.activeDeviceTab||this.selectedDevices.length<=1)this.$message.warning("请选择要复制的设备配置");else if("videoPublish"===this.selectedFunction){console.log("🎬 [小红书] 检测到视频发布功能，使用组件引用方式复制配置");const e=this.$refs[`config_${this.activeDeviceTab}`];if(console.log("🔧 [小红书] 源设备配置组件引用:",e),!e||!e[0])return this.$message.error("无法获取源设备配置组件"),void console.error("❌ [小红书] 无法获取源设备配置组件");const t=e[0].getConfig();if(console.log("📋 [小红书] 源设备配置:",t),!t||0===Object.keys(t).length)return this.$message.warning("源设备配置为空，无法复制"),void console.warn("⚠️ [小红书] 源设备配置为空");this.$confirm(`确定要将 ${this.getDeviceName(this.activeDeviceTab)} 的视频发布配置复制到其他所有设备吗？\n\n将复制：小红书应用、视频标题模板、视频描述、话题标签等参数\n视频选择不会被复制`,"确认复制",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{let e=0,s=[];for(const o of this.selectedDevices)if(o!==this.activeDeviceTab){console.log(`🔧 [小红书] 正在复制配置到设备: ${o}`);const n=this.$refs[`config_${o}`];if(console.log(`🔧 [小红书] 设备 ${o} 配置组件引用:`,n),n&&n[0]&&n[0].setConfig)try{n[0].setConfig(t),e++,console.log(`✅ [小红书] 配置已复制到设备: ${o}`);const s=n[0].getConfig();console.log(`📋 [小红书] 设备 ${o} 复制后的配置:`,s)}catch(i){console.error(`❌ [小红书] 复制配置到设备 ${o} 失败:`,i),s.push(o)}else console.error(`❌ [小红书] 设备 ${o} 的配置组件无效`),s.push(o)}if(console.log(`📊 [小红书] 复制结果: 成功 ${e} 个, 失败 ${s.length} 个`),e>0){let t=`已复制视频发布配置到 ${e} 个设备`;s.length>0&&(t+=`，${s.length} 个设备复制失败`),this.$message.success(t),this.addLog("系统",`视频发布配置复制完成，共复制到 ${e} 个设备`,"info")}else this.$message.error("配置复制失败，请检查设备状态"),this.addLog("系统","视频发布配置复制失败","error")}).catch(()=>{console.log("🔧 [小红书] 用户取消配置复制")})}else{console.log("🔧 [小红书] 使用deviceConfigs方式复制配置");const e=this.deviceConfigs[this.activeDeviceTab];if(!e)return void this.$message.warning("当前设备没有配置参数");this.$confirm(`确定要将 ${this.getDeviceName(this.activeDeviceTab)} 的配置复制到其他所有设备吗？`,"确认复制",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{let t=0;this.selectedDevices.forEach(s=>{s!==this.activeDeviceTab&&(this.$set(this.deviceConfigs,s,{...e}),t++)}),this.$message.success(`已将配置复制到 ${t} 个设备`),this.addLog("系统",`配置复制完成，共复制到 ${t} 个设备`,"info")}).catch(()=>{})}},async handleExecuteScript(e){if(console.log("[XiaohongshuAutomation] 收到脚本执行请求:",e),!this.selectedDevices.length)return void this.$message.warning("请先选择执行设备");const t=e.deviceId||this.activeDeviceTab;t?(console.log("[XiaohongshuAutomation] 执行单个设备脚本:",t),"videoPublish"===e.functionType?await this.executeVideoPublishForDevice(t,e):await this.executeForDevice(t)):this.$message.warning("请选择要执行的设备")},async executeVideoPublishForDevice(e,t){console.log("[XiaohongshuAutomation] 执行视频发布功能:",e,t);const s=t.config||this.deviceConfigs[e];if(!s)return void this.$message.warning("请完善设备配置参数");this.executing=!0;const i=this.getDeviceName(e);this.addLog("系统",`开始执行设备 ${i} 的视频发布任务...`,"info");try{const t=`xiaohongshu_videoPublish_${Date.now()}_${e}`,o={functionType:"videoPublish",config:s,deviceConfigs:{[e]:s},schedule:this.scheduleConfig,deviceIds:[e],taskId:t};console.log("视频发布执行参数:",JSON.stringify(o,null,2));const n=await this.$http.post("/api/xiaohongshu/execute",o);if(!n.data.success)throw new Error(n.data.message);{this.$message.success(`设备 ${i} 视频发布任务已启动`),this.addLog("系统",`设备 ${i} 视频发布任务已启动`,"success");const o=`${t}_${e}`;this.$root.$emit("xiaohongshu-task-started",{functionType:"videoPublish",deviceId:e,taskId:o,logId:o,config:s,message:"开始执行视频发布任务"}),console.log(`=== 设备 ${e} 视频发布任务开始事件已发送 ===`)}}catch(o){console.error("执行视频发布任务失败:",o),this.addLog("系统",`设备 ${i} 视频发布执行失败: ${o.message}`,"error"),this.$message.error(`设备 ${i} 视频发布执行失败: `+o.message)}finally{this.executing=!1}},addLog(e,t,s="info"){this.logs.unshift({time:new Date,device:e,message:t,level:s}),this.logs.length>1e3&&(this.logs=this.logs.slice(0,1e3))},clearLogs(){this.logs=[]},goToLogs(){this.$router.push("/xiaohongshu-logs")},handleTaskStarted(e){console.log("组件内部任务已启动:",e),this.addLog("系统",`组件内部任务已启动: ${e.taskId}`,"success"),console.log("=== 组件内部任务启动，等待后端发送任务开始事件 ===",e)},handleTaskStopped(e){console.log("组件内部任务已停止:",e),this.addLog("系统",`组件内部任务已停止: ${e.taskId}`,"warning"),this.$root.$emit("xiaohongshu-task-stopped",{functionType:this.selectedFunction,taskInfo:e})},handleDeviceSelected(e){if(e){if("busy"===e.status){const t=this.isDeviceExecutingCurrentFunction(e.device_id);if(!t)return this.$message.warning(`设备 ${e.device_name} 正在执行其他脚本，无法选择`),void this.addLog("系统",`尝试选择忙碌设备: ${e.device_name}，已拒绝`,"warning");this.addLog("系统",`设备 ${e.device_name} 正在执行当前功能，允许选择`,"info")}this.selectedDevices.includes(e.device_id)||(this.selectedDevices.push(e.device_id),this.initDeviceConfig(e.device_id),1===this.selectedDevices.length&&(this.activeDeviceTab=e.device_id)),this.addLog("系统",`已选择设备: ${e.device_name}`,"info")}},handleDeviceRemoved(e){e&&(this.removeDevice(e.device_id),this.addLog("系统",`已移除设备: ${e.device_name}`,"info"))},handleDevicesSelectionChanged(e){console.log("[批量选择] 设备选择变化:",e);const t=this.selectedDevices.filter(t=>!e.includes(t));t.forEach(e=>{this.removeDevice(e)});const s=e.filter(e=>!this.selectedDevices.includes(e));s.forEach(e=>{const t=this.getAllDevices().find(t=>t.device_id===e);t&&this.handleDeviceSelected(t)}),console.log("[批量选择] 最终选中设备:",this.selectedDevices)},removeDevice(e){const t=this.selectedDevices.indexOf(e);if(t>-1){this.selectedDevices.splice(t,1),this.$delete(this.deviceConfigs,e),this.activeDeviceTab===e&&this.selectedDevices.length>0?this.activeDeviceTab=this.selectedDevices[0]:0===this.selectedDevices.length&&(this.activeDeviceTab="");const s=this.onlineDevices.find(t=>t.device_id===e);this.addLog("系统",`已移除设备: ${s?s.device_name:e}`,"info")}},getDeviceName(e){const t=this.getAllDevices().find(t=>t.device_id===e);return t?t.device_name:e},initDeviceConfig(e){this.deviceConfigs[e]||(this.$set(this.deviceConfigs,e,{...this.getDefaultConfig()}),console.log(`[多设备配置] 为设备 ${e} 初始化配置:`,this.deviceConfigs[e]))},getDefaultConfig(){const e={profile:{nickname:"",profile:"",modifyOptions:[],operationDelay:2,safetyOptions:[]},searchGroupChat:{searchKeyword:"",targetJoinCount:5,maxScrollAttempts:10,enableDetailedLog:!1},groupMessage:{sendInterval:3600,enableLoop:!1,maxLoopCount:10},articleComment:{searchKeyword:"",commentCount:3,operationDelay:5},uidMessage:{inputMode:"manual",uidList:"",uidStrategy:"sequential",message:"",delay:5,maxCount:10,enableDetailLog:!0,skipUsedUids:!0,autoMarkUsed:!0,advancedOptions:["enableDetailLog","autoMarkUsed"]}};return e[this.selectedFunction]||{}},handleDeviceConfigUpdate(e,t){console.log(`[多设备配置] 设备 ${e} 配置更新:`,t),this.$set(this.deviceConfigs,e,t)},getDeviceTabLabel(e){const t=this.getAllDevices().find(t=>t.device_id===e),s=t?t.device_name:e,i=t?this.getDeviceIP(t):"未知IP",o=this.isDeviceConfigValid(e);return`${s} (${i}) ${o?"✓":"⚠"}`},isDeviceConfigValid(e){const t=this.deviceConfigs[e];if(!t)return!1;switch(this.selectedFunction){case"profile":return t.nickname&&t.nickname.trim()||t.profile&&t.profile.trim();case"searchGroupChat":return t.searchKeyword&&t.searchKeyword.trim();case"groupMessage":return t.sendInterval&&t.sendInterval>0;case"articleComment":return t.searchKeyword&&t.searchKeyword.trim()&&t.commentCount>0;case"uidMessage":let s=!1;if("manual"===t.inputMode)if("string"===typeof t.uidList){const e=t.uidList.split("\n").map(e=>e.trim()).filter(e=>e.length>0);s=e.length>0}else Array.isArray(t.uidList)&&(s=t.uidList.length>0);else"file"===t.inputMode&&(s=!0);const i=t.message&&t.message.trim().length>0,o=t.delay&&t.delay>=3,n=t.maxCount&&t.maxCount>0;return console.log(`[设备配置验证] ${e} UID私信验证:`,{hasValidUids:s,hasValidMessage:i,hasValidDelay:o,hasValidMaxCount:n}),s&&i&&o&&n;default:return!0}},getValidConfigCount(){return this.selectedDevices.filter(e=>this.isDeviceConfigValid(e)).length},hasValidConfigs(){return this.getValidConfigCount()>0},handleTabClick(e){console.log(`[多设备配置] 切换到设备标签: ${e.name}`),this.activeDeviceTab=e.name},getDeviceNameWithStatus(e){const t=this.getAllDevices().find(t=>t.device_id===e);if(t){const e=this.getStatusText(t.status),s=this.getDeviceIP(t);return`${t.device_name} (${e}) - ${s}`}return e},getDeviceIP(e){return e.ip_address||e.deviceIP||e.device_ip||e.device_info&&"object"===typeof e.device_info&&e.device_info.ipAddress||e.device_info&&"object"===typeof e.device_info&&e.device_info.ip||"未知IP"},getDeviceTagType(e){const t=this.getAllDevices().find(t=>t.device_id===e);return t?this.getStatusTagType(t.status):"info"},getAllDevices(){return this.$store.getters["device/devices"]},isDeviceExecutingCurrentFunction(e){if(!this.selectedFunction)return!1;const t=this.$store.getters["xiaohongshu/getDeviceTasks"](e);return!(!t||0===t.length)&&t.some(e=>e.functionType===this.selectedFunction&&"running"===e.status)},autoRestoreExecutingDevices(){if(!this.selectedFunction)return;console.log("[小红书自动化] 检查是否有正在执行当前功能的设备");const e=this.$store.getters["device/devices"];console.log("[小红书自动化] 设备列表:",e);const t=e.filter(e=>{const t="busy"===e.status,s=this.isDeviceExecutingCurrentFunction(e.device_id);return console.log(`[小红书自动化] 设备 ${e.device_name} 检查:`,{status:e.status,isBusy:t,isExecutingCurrentFunction:s,deviceId:e.device_id}),t&&s});t.length>0?(console.log("[小红书自动化] 发现正在执行当前功能的设备:",t),t.forEach(e=>{this.selectedDevices.includes(e.device_id)||(this.selectedDevices.push(e.device_id),this.initDeviceConfig(e.device_id),console.log("[小红书自动化] 自动选择设备:",e.device_name))}),this.selectedDevices.length>0&&!this.activeDeviceTab&&(this.activeDeviceTab=this.selectedDevices[0]),this.addLog("系统",`自动恢复了 ${t.length} 个正在执行当前功能的设备`,"info")):console.log("[小红书自动化] 没有发现正在执行当前功能的设备")},getOnlineDevices(){return this.$store.getters["device/devices"].filter(e=>"online"===e.status||"busy"===e.status)},getStatusTagType(e){switch(e){case"online":return"success";case"busy":return"warning";case"offline":return"danger";default:return"info"}},getStatusText(e){switch(e){case"online":return"在线";case"busy":return"忙碌";case"offline":return"离线";default:return"未知"}},async savePageState(){console.log("[状态管理] 保存页面状态");try{await this.$store.dispatch("xiaohongshu/setPageState",{selectedFunction:this.selectedFunction,selectedDevices:this.selectedDevices}),console.log("[状态管理] 页面状态已保存:",{selectedFunction:this.selectedFunction,selectedDevices:this.selectedDevices})}catch(e){console.error("[状态管理] 保存页面状态失败:",e)}},async restorePageState(){console.log("[状态管理] 恢复页面状态");try{const e=this.$store.getters["xiaohongshu/getPageState"];console.log("[状态管理] 获取到的页面状态:",e),e.selectedFunction&&(this.selectedFunction=e.selectedFunction,console.log("[状态管理] 恢复选中功能:",this.selectedFunction)),e.selectedDevices&&e.selectedDevices.length>0&&(this.selectedDevices=[...e.selectedDevices],console.log("[状态管理] 恢复选中设备:",this.selectedDevices))}catch(e){console.error("[状态管理] 恢复页面状态失败:",e)}},async checkAndRestoreRunningTasks(){console.log("[状态管理] 检查并恢复运行中的任务");try{await this.$store.dispatch("xiaohongshu/checkAndRestoreRunningTasks");const e=["profile","searchGroupChat","groupMessage","articleComment"];let t=!1;for(const i of e){const e=this.$store.getters["xiaohongshu/getFunctionState"](i);e.isScriptRunning&&(console.log(`[状态管理] 发现运行中的任务: ${i}`,e),t=!0,this.selectedFunction=i,e.selectedDevices&&e.selectedDevices.length>0&&(this.selectedDevices=[...e.selectedDevices],console.log("[状态管理] 恢复选中设备:",this.selectedDevices),e.selectedDevices.forEach(t=>{e.config&&Object.keys(e.config).length>0?(this.$set(this.deviceConfigs,t,{...e.config}),console.log(`[状态管理] 恢复设备 ${t} 的配置:`,e.config)):this.initDeviceConfig(t)}),this.selectedDevices.length>0&&(this.activeDeviceTab=this.selectedDevices[0])),this.$root.$emit("xiaohongshu-task-restored",{functionType:i,state:e}),this.addLog("系统",`恢复运行中的任务: ${i}`,"info"))}const s=this.$store.getters["xiaohongshu/isAnyTaskRunning"];if(s){const e=this.$store.getters["xiaohongshu/getRunningTasks"];console.log("[状态管理] 发现运行中的任务:",e),this.addLog("系统",`发现 ${e.length} 个运行中的任务`,"warning")}t&&(console.log("[状态管理] 状态恢复完成"),console.log("恢复后的状态:"),console.log("- selectedFunction:",this.selectedFunction),console.log("- selectedDevices:",this.selectedDevices),console.log("- deviceConfigs:",this.deviceConfigs),console.log("- activeDeviceTab:",this.activeDeviceTab))}catch(e){console.error("[状态管理] 检查运行中任务失败:",e)}},handleRealtimeStatus(e){console.log("🔄 [XiaohongshuAutomation] 收到实时状态更新:",e),e.deviceId&&e.taskId?console.log(`📱 [XiaohongshuAutomation] 设备 ${e.deviceId} 任务 ${e.taskId} 状态更新:`,{currentStatus:e.currentStatus,operationCount:e.operationCount,processedStepCount:e.processedStepCount,message:e.message}):console.warn("⚠️ [XiaohongshuAutomation] 实时状态数据缺少deviceId或taskId:",e)},handleMainRealtimeStatus(e){if(console.log("🔄 [XiaohongshuAutomation] 收到主页面实时状态更新:",e),console.log("🔄 [XiaohongshuAutomation] 当前时间:",(new Date).toISOString()),console.log("🔄 [XiaohongshuAutomation] 数据详情:",JSON.stringify(e,null,2)),!e.deviceId||!e.taskId)return void console.warn("⚠️ [XiaohongshuAutomation] 实时状态数据缺少必要字段");this.showRealtimeStatus||(this.showRealtimeStatus=!0,this.currentExecutionStatus="执行中"),this.$root.$emit("xiaohongshu_realtime_status",e),console.log("📡 [XiaohongshuAutomation] 已转发实时状态给子组件:",e.deviceId);let t=this.executingDevices.findIndex(t=>t.deviceId===e.deviceId);if(-1===t)this.executingDevices.push({deviceId:e.deviceId,deviceName:this.getDeviceName(e.deviceId),status:"executing",realtimeStatus:{currentStep:e.currentStep||"执行中",currentStatus:e.currentStatus||"进行中",message:e.message||"",errorMessage:e.errorMessage||"",processedCount:e.processedCount||0,totalCount:e.totalCount||0,timestamp:e.timestamp}});else{const s=this.executingDevices[t];s.realtimeStatus={currentStep:e.currentStep||s.realtimeStatus.currentStep,currentStatus:e.currentStatus||s.realtimeStatus.currentStatus,message:e.message||s.realtimeStatus.message,errorMessage:e.errorMessage||s.realtimeStatus.errorMessage,processedCount:e.processedCount||s.realtimeStatus.processedCount,totalCount:e.totalCount||s.realtimeStatus.totalCount,timestamp:e.timestamp},e.currentStatus&&e.currentStatus.includes("完成")?s.status="completed":e.currentStatus&&e.currentStatus.includes("错误")?s.status="error":s.status="executing"}console.log("📊 [XiaohongshuAutomation] 实时状态已更新:",{deviceId:e.deviceId,currentStep:e.currentStep,currentStatus:e.currentStatus})},hideRealtimeStatus(){this.showRealtimeStatus=!1,this.executingDevices=[],this.currentExecutionStatus="等待开始"},handleScriptReset(e){if(console.log("🔄 [XiaohongshuAutomation] 收到脚本重置事件:",e),!e.deviceId||!e.functionType)return void console.warn("⚠️ [XiaohongshuAutomation] 脚本重置事件缺少必要字段");this.$root.$emit("xiaohongshu-script-reset",{functionType:e.functionType,deviceId:e.deviceId,taskId:e.taskId,reason:e.reason||"脚本已停止",timestamp:e.timestamp});const t=this.executingDevices.findIndex(t=>t.deviceId===e.deviceId);-1!==t&&(this.executingDevices.splice(t,1),console.log(`✅ [XiaohongshuAutomation] 已从执行列表中移除设备: ${e.deviceId}`)),0===this.executingDevices.length&&(this.hideRealtimeStatus(),console.log("✅ [XiaohongshuAutomation] 所有设备已停止，隐藏实时状态面板")),this.executing=!1,this.currentExecutionStatus="等待开始",console.log(`✅ [XiaohongshuAutomation] 脚本重置完成: ${e.deviceId}`)},handleVuexStateUpdate(e){console.log("🔄 [XiaohongshuAutomation] 收到Vuex状态更新事件:",e),"stopTask"===e.action&&e.functionType&&e.deviceId&&this.$store.dispatch("xiaohongshu/stopTask",{functionType:e.functionType,deviceId:e.deviceId,taskId:e.taskId,reason:e.reason}).then(()=>{console.log(`✅ [XiaohongshuAutomation] Vuex状态已更新: ${e.functionType} - ${e.deviceId}`)}).catch(e=>{console.error("❌ [XiaohongshuAutomation] Vuex状态更新失败:",e)})},handleForceRefreshVuex(e){console.log("🔄 [XiaohongshuAutomation] 收到强制刷新Vuex状态事件:",e),"batchStop"===e.action&&e.functionType&&this.$store.dispatch("xiaohongshu/resetFunctionState",e.functionType).then(()=>(console.log(`✅ [XiaohongshuAutomation] 已强制重置 ${e.functionType} 功能状态`),this.$store.dispatch("xiaohongshu/checkRunningTasks"))).then(()=>{console.log("✅ [XiaohongshuAutomation] 已刷新运行中任务状态")}).catch(e=>{console.error("❌ [XiaohongshuAutomation] 强制刷新Vuex状态失败:",e)})},async refreshVuexState(){console.log("🔄 [XiaohongshuAutomation] 手动刷新Vuex状态");try{const e=this.$loading({lock:!0,text:"正在刷新状态...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});await this.$store.dispatch("xiaohongshu/forceResetAllFunctionStates"),await this.$store.dispatch("xiaohongshu/checkRunningTasks"),await this.loadDevices(),await this.checkAndRestoreRunningTasks(),e.close(),this.$message.success("状态刷新完成！"),console.log("✅ [XiaohongshuAutomation] Vuex状态手动刷新完成")}catch(e){console.error("❌ [XiaohongshuAutomation] 手动刷新Vuex状态失败:",e),this.$message.error("状态刷新失败: "+e.message)}},getCurrentFunctionName(){const e=this.functions.find(e=>e.key===this.selectedFunction);return e?e.name:"未知功能"},getStatusTagType(){return this.currentExecutionStatus.includes("完成")?"success":this.currentExecutionStatus.includes("错误")||this.currentExecutionStatus.includes("失败")?"danger":this.currentExecutionStatus.includes("执行")?"warning":"info"},getDeviceStatusType(e){switch(e){case"completed":return"success";case"error":return"danger";case"executing":return"warning";default:return"info"}},getStatusClass(e){return e&&e.includes("错误")?"status-error":e&&e.includes("完成")?"status-success":e&&e.includes("警告")?"status-warning":"status-normal"},hasProgressInfo(e){return e&&(void 0!==e.processedCount||void 0!==e.totalCount)&&e.totalCount>0},getDeviceProgress(e){if(!this.hasProgressInfo(e))return 0;const t=e.processedCount||0,s=e.totalCount||1;return Math.round(t/s*100)},getProgressStatus(e){return e&&e.errorMessage?"exception":100===this.getDeviceProgress(e)?"success":null}}},$e=ke,xe=(0,u.A)($e,i,o,!1,null,"13b1c8d6",null),De=xe.exports}}]);