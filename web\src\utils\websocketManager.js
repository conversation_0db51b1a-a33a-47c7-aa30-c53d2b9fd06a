import io from 'socket.io-client'
import store from '@/store'
import { getWebSocketUrl } from '@/utils/serverConfig'

// 纯WebSocket通信管理器
class WebSocketManager {
  constructor() {
    this.socket = null
    this.isConnected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 10
    this.reconnectDelay = 1000 // 初始重连延迟1秒
    this.maxReconnectDelay = 30000 // 最大重连延迟30秒
    this.eventHandlers = new Map()
    this.isInitialized = false
    this.reconnectTimer = null
    this.heartbeatTimer = null
    this.heartbeatInterval = 25000 // 25秒心跳间隔（比服务器超时时间短）
    this.connectionPromise = null
    this.lastHeartbeatTime = null
    this.heartbeatTimeout = 10000 // 心跳超时时间10秒
    this.connectionCheckTimer = null
    this.connectionCheckInterval = 60000 // 60秒检查一次连接状态

    // 监听页面可见性变化
    this.setupVisibilityListener()
  }

  // 设置页面可见性监听
  setupVisibilityListener() {
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden && this.isInitialized) {
          console.log('🔧 [WebSocketManager] 页面重新可见，检查连接状态')
          setTimeout(() => {
            this.checkConnectionAfterVisibilityChange()
          }, 1000) // 延迟1秒检查，确保页面完全激活
        }
      })
    }
  }

  // 页面可见性变化后检查连接
  checkConnectionAfterVisibilityChange() {
    if (!this.isConnected || !this.socket || this.socket.disconnected) {
      console.log('🔄 [WebSocketManager] 页面重新可见时发现连接已断开，尝试重连')
      this.handleConnectionError(new Error('页面重新可见时连接已断开'))
    } else {
      console.log('✅ [WebSocketManager] 页面重新可见时连接正常')
      // 发送一个心跳确认连接
      this.socket.emit('heartbeat', {
        timestamp: new Date().toISOString(),
        clientType: 'websocket_manager',
        reason: 'visibility_check'
      })
    }
  }

  // 初始化WebSocket连接
  async init() {
    // 允许未认证用户也能建立连接，用于接收设备事件
    const isAuthenticated = store.getters['auth/isAuthenticated']
    console.log('🔧 [WebSocketManager] 初始化WebSocket连接...', { isAuthenticated })

    if (this.isInitialized && this.isConnected) {
      console.log('🔧 [WebSocketManager] WebSocket管理器已初始化且连接正常')
      return this.connectionPromise
    }

    console.log('🔧 [WebSocketManager] 初始化WebSocket管理器...')
    this.isInitialized = true

    // 如果已有连接Promise在进行中，返回它
    if (this.connectionPromise) {
      console.log('🔧 [WebSocketManager] 连接Promise已存在，返回现有Promise')
      return this.connectionPromise
    }

    this.connectionPromise = this.connect()
    return this.connectionPromise
  }

  // 建立WebSocket连接
  async connect() {
    try {
      console.log('🔧 [WebSocketManager] 建立WebSocket连接...')

      // 清理之前的连接
      this.cleanup()

      // 获取服务器地址
      const serverUrl = getWebSocketUrl()
      console.log('🔧 [WebSocketManager] 连接到WebSocket服务器:', serverUrl || '使用代理模式')

      // 创建Socket连接配置
      const socketOptions = {
        transports: ['websocket'], // 只使用WebSocket，不降级到polling
        timeout: 20000, // 增加超时时间到20秒
        reconnection: false, // 手动控制重连
        forceNew: true,
        upgrade: false, // 禁用传输升级，直接使用WebSocket
        autoConnect: true, // 自动连接
        pingTimeout: 60000, // ping超时时间60秒
        pingInterval: 25000 // ping间隔25秒
      }

      // 根据是否有serverUrl决定连接方式
      if (serverUrl) {
        this.socket = io(serverUrl, socketOptions)
      } else {
        // 使用代理模式（开发环境）
        this.socket = io(socketOptions)
      }

      // 设置连接超时
      const connectTimeout = setTimeout(() => {
        console.error('WebSocket连接超时')
        this.handleConnectionError(new Error('连接超时'))
      }, 15000)

      return new Promise((resolve, reject) => {
        this.socket.on('connect', () => {
          clearTimeout(connectTimeout)
          console.log('✅ [WebSocketManager] WebSocket连接成功')
          this.isConnected = true
          this.reconnectAttempts = 0
          this.reconnectDelay = 1000

          // 设置事件处理器
          this.setupEventHandlers()

          // 启动心跳
          this.startHeartbeat()

          // 启动连接状态检查
          this.startConnectionCheck()

          // 通知store连接成功
          store.dispatch('socket/connect', this.socket)

          // 直接设置连接状态为true，因为socket已经连接
          store.commit('socket/SET_CONNECTED', true)
          store.commit('socket/SET_SOCKET', this.socket)

          // 触发连接成功事件
          this.emitEvent('connection_established', { type: 'websocket' })

          // 注册Web客户端
          this.registerWebClient()

          // 连接成功后立即请求设备状态同步
          setTimeout(() => {
            this.requestDeviceStatusSync()
          }, 1000)

          resolve()
        })

        this.socket.on('connect_error', (error) => {
          clearTimeout(connectTimeout)
          console.error('❌ [WebSocketManager] WebSocket连接失败:', error.message)
          this.handleConnectionError(error)
          reject(error)
        })

        this.socket.on('disconnect', (reason) => {
          console.log('🔌 [WebSocketManager] WebSocket连接断开:', reason)
          this.isConnected = false
          this.stopHeartbeat()
          this.stopConnectionCheck()

          // 通知store连接断开
          store.dispatch('socket/disconnect')
          store.commit('socket/SET_CONNECTED', false)

          // 触发断开连接事件
          this.emitEvent('connection_lost', { reason })

          // 如果不是主动断开，尝试重连
          if (reason !== 'io client disconnect' && reason !== 'client namespace disconnect') {
            console.log('🔄 [WebSocketManager] 非主动断开，准备重连...')
            this.scheduleReconnect()
          } else {
            console.log('🔌 [WebSocketManager] 主动断开连接，不进行重连')
          }
        })
      })

    } catch (error) {
      console.error('❌ [WebSocketManager] WebSocket连接异常:', error)
      this.handleConnectionError(error)
      throw error
    }
  }

  // 设置WebSocket事件处理器
  setupEventHandlers() {
    if (!this.socket) return

    // 设备列表更新
    this.socket.on('devices_list', (devices) => {
      console.log('WebSocket收到设备列表:', devices)
      this.emitEvent('devices_list', devices)
    })

    // 设备状态变化
    this.socket.on('device_status_changed', (data) => {
      console.log('WebSocket收到设备状态变化:', data)
      this.emitEvent('device_status_changed', data)
    })

    // 设备状态更新
    this.socket.on('device_status_update', (data) => {
      console.log('WebSocket收到设备状态更新:', data)
      this.emitEvent('device_status_update', data)

      // 立即更新store中的设备状态，确保状态同步
      if (store && data.deviceId) {
        store.commit('device/UPDATE_DEVICE', {
          deviceId: data.deviceId,
          updates: {
            status: data.status,
            last_seen: data.lastSeen || new Date().toISOString()
          }
        })
        console.log(`已更新store中设备 ${data.deviceId} 状态为: ${data.status}`)
      }
    })

    // 设备状态变化（健康检查等）
    this.socket.on('device_status_changed', (data) => {
      console.log('WebSocket收到设备状态变化:', data)
      this.emitEvent('device_status_changed', data)

      // 立即更新store中的设备状态
      if (store && data.deviceId) {
        const newStatus = data.type === 'device_health_warning' ? 'offline' : 'online'
        store.commit('device/UPDATE_DEVICE', {
          deviceId: data.deviceId,
          updates: {
            status: newStatus,
            last_seen: data.lastSeen || new Date().toISOString()
          }
        })
        console.log(`健康检查：已更新store中设备 ${data.deviceId} 状态为: ${newStatus}`)
      }
    })

    // 小红书实时状态
    this.socket.on('xiaohongshu_realtime_status', (data) => {
      console.log('WebSocket收到小红书实时状态:', data)
      this.emitEvent('xiaohongshu_realtime_status', data)
    })

    // 闲鱼实时状态
    this.socket.on('xianyu_realtime_status', (data) => {
      console.log('WebSocket收到闲鱼实时状态:', data)
      this.emitEvent('xianyu_realtime_status', data)
    })

    // 服务器关闭通知
    this.socket.on('server_shutdown', (data) => {
      console.log('收到服务器关闭通知:', data)
      this.emitEvent('server_shutdown', data)
      this.isConnected = false
      this.isInitialized = false
    })

    // 心跳响应
    this.socket.on('pong', () => {
      console.log('💓 [WebSocketManager] 收到pong心跳响应')
      this.lastHeartbeatTime = Date.now()
    })

    // 服务器心跳响应
    this.socket.on('heartbeat_response', (data) => {
      console.log('💓 [WebSocketManager] 收到服务器心跳响应:', data)
      this.lastHeartbeatTime = Date.now()
    })

    // 连接错误处理
    this.socket.on('connect_error', (error) => {
      console.error('❌ [WebSocketManager] 连接错误:', error)
      this.handleConnectionError(error)
    })

    // 重连错误处理
    this.socket.on('reconnect_error', (error) => {
      console.error('❌ [WebSocketManager] 重连错误:', error)
      this.handleConnectionError(error)
    })

    // 测试事件
    this.socket.on('test_event', (data) => {
      console.log('收到测试事件:', data)
      this.emitEvent('test_event', data)
    })
  }

  // 启动心跳
  startHeartbeat() {
    this.stopHeartbeat()
    console.log('🔧 [WebSocketManager] 启动心跳机制，间隔:', this.heartbeatInterval + 'ms')

    this.heartbeatTimer = setInterval(() => {
      if (this.socket && this.isConnected) {
        this.lastHeartbeatTime = Date.now()
        this.socket.emit('ping')
        this.socket.emit('heartbeat', {
          timestamp: new Date().toISOString(),
          clientType: 'websocket_manager'
        })
        console.log('💓 [WebSocketManager] 发送心跳')
      } else {
        console.warn('⚠️ [WebSocketManager] 心跳检查时发现连接已断开')
        this.stopHeartbeat()
      }
    }, this.heartbeatInterval)
  }

  // 停止心跳
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
      console.log('🔧 [WebSocketManager] 心跳机制已停止')
    }
  }

  // 启动连接状态检查
  startConnectionCheck() {
    this.stopConnectionCheck()
    console.log('🔧 [WebSocketManager] 启动连接状态检查，间隔:', this.connectionCheckInterval + 'ms')

    this.connectionCheckTimer = setInterval(() => {
      this.checkConnectionHealth()
    }, this.connectionCheckInterval)
  }

  // 停止连接状态检查
  stopConnectionCheck() {
    if (this.connectionCheckTimer) {
      clearInterval(this.connectionCheckTimer)
      this.connectionCheckTimer = null
      console.log('🔧 [WebSocketManager] 连接状态检查已停止')
    }
  }

  // 检查连接健康状态
  checkConnectionHealth() {
    if (!this.socket || !this.isConnected) {
      console.warn('⚠️ [WebSocketManager] 连接健康检查：连接已断开')
      return
    }

    // 检查心跳超时
    if (this.lastHeartbeatTime) {
      const timeSinceLastHeartbeat = Date.now() - this.lastHeartbeatTime
      if (timeSinceLastHeartbeat > this.heartbeatTimeout + this.heartbeatInterval) {
        console.error('❌ [WebSocketManager] 心跳超时，强制重连')
        this.handleConnectionError(new Error('心跳超时'))
        return
      }
    }

    // 检查Socket连接状态
    if (this.socket.disconnected) {
      console.error('❌ [WebSocketManager] Socket已断开，但状态未更新，强制重连')
      this.handleConnectionError(new Error('Socket状态不一致'))
      return
    }

    console.log('✅ [WebSocketManager] 连接健康检查通过')
  }

  // 处理连接错误
  handleConnectionError(error) {
    console.error('WebSocket连接错误:', error)
    this.isConnected = false
    this.connectionPromise = null
    this.scheduleReconnect()
  }

  // 安排重连
  scheduleReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ [WebSocketManager] 重连次数已达上限，停止重连')
      this.emitEvent('connection_failed', {
        reason: '重连次数超限',
        attempts: this.reconnectAttempts
      })
      return
    }

    this.reconnectAttempts++

    // 指数退避算法计算延迟
    const delay = Math.min(
      this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
      this.maxReconnectDelay
    )

    console.log(`🔄 [WebSocketManager] 安排重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})，${delay}ms后重试`)

    // 触发重连开始事件
    this.emitEvent('reconnect_attempt', {
      attempt: this.reconnectAttempts,
      maxAttempts: this.maxReconnectAttempts,
      delay: delay
    })

    this.reconnectTimer = setTimeout(async () => {
      try {
        console.log(`🔄 [WebSocketManager] 开始第${this.reconnectAttempts}次重连尝试`)
        this.connectionPromise = this.connect()
        await this.connectionPromise
        console.log('✅ [WebSocketManager] 重连成功')
        this.emitEvent('reconnect_success', {
          attempt: this.reconnectAttempts
        })
      } catch (error) {
        console.error(`❌ [WebSocketManager] 第${this.reconnectAttempts}次重连失败:`, error)
        this.emitEvent('reconnect_failed', {
          attempt: this.reconnectAttempts,
          error: error.message
        })
        // 继续下一次重连尝试
        this.scheduleReconnect()
      }
    }, delay)
  }

  // 清理连接资源
  cleanup() {
    console.log('🔧 [WebSocketManager] 清理连接资源')
    this.stopHeartbeat()
    this.stopConnectionCheck()

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.socket) {
      this.socket.removeAllListeners()
      this.socket.disconnect()
      this.socket = null
    }

    this.isConnected = false
    this.lastHeartbeatTime = null
  }

  // 断开连接
  disconnect() {
    console.log('🔌 [WebSocketManager] 主动断开WebSocket连接')
    this.isInitialized = false
    this.connectionPromise = null
    this.cleanup()

    // 通知store连接断开
    store.dispatch('socket/disconnect')
    store.commit('socket/SET_CONNECTED', false)
  }

  // 发送消息
  emit(event, data) {
    if (this.socket && this.isConnected) {
      this.socket.emit(event, data)
      return true
    } else {
      console.warn('WebSocket未连接，无法发送消息:', event)
      return false
    }
  }

  // 注册事件监听器
  on(event, handler) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set())
    }
    this.eventHandlers.get(event).add(handler)
  }

  // 移除事件监听器
  off(event, handler) {
    if (this.eventHandlers.has(event)) {
      if (handler) {
        this.eventHandlers.get(event).delete(handler)
      } else {
        this.eventHandlers.get(event).clear()
      }
    }
  }

  // 触发事件
  emitEvent(event, data) {
    if (this.eventHandlers.has(event)) {
      this.eventHandlers.get(event).forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`事件处理器执行失败 [${event}]:`, error)
        }
      })
    }
  }

  // 获取连接状态
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      isInitialized: this.isInitialized,
      reconnectAttempts: this.reconnectAttempts,
      type: 'websocket'
    }
  }

  // 强制重连
  forceReconnect() {
    console.log('强制重连WebSocket')
    this.reconnectAttempts = 0
    this.connectionPromise = null
    this.cleanup()
    return this.init()
  }

  // 注册Web客户端
  registerWebClient() {
    if (!this.isConnected || !this.socket) return

    const user = store.getters['auth/user']
    const userId = user ? user.id : 'anonymous'

    this.socket.emit('web_client_connect', {
      userId: userId,
      username: user ? user.username : 'anonymous',
      clientType: 'websocket_manager',
      page: 'global'
    })

    console.log('📡 [WebSocketManager] 已注册为用户', userId, '的全局客户端')
  }

  // 请求设备状态同步
  requestDeviceStatusSync() {
    console.log('🔄 [WebSocketManager] 请求设备状态同步...')
    if (this.isConnected && this.socket) {
      this.socket.emit('request_device_status_sync')
    }
  }

  // 获取连接状态
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      socket: this.socket,
      type: this.isConnected ? 'websocket' : 'disconnected'
    }
  }

  // 发送消息
  send(event, data) {
    if (this.isConnected && this.socket) {
      this.socket.emit(event, data)
    } else {
      console.warn('⚠️ [WebSocketManager] WebSocket未连接，无法发送消息:', event, data)
    }
  }
}

// 创建全局实例
const wsManager = new WebSocketManager()

// 导出函数
export function initWebSocket() {
  return wsManager.init()
}

export function getWebSocketManager() {
  return wsManager
}

export function disconnectWebSocket() {
  wsManager.disconnect()
}

// 确保连接状态
export async function ensureConnection() {
  if (!wsManager.isConnected) {
    console.log('🔧 [WebSocketManager] 连接未建立，尝试初始化...')
    await wsManager.init()
  }
  return wsManager.isConnected
}

// 兼容原有接口
export function initSocket() {
  return wsManager.init()
}

export function disconnectSocket() {
  wsManager.disconnect()
}

export default wsManager
