<template>
  <div class="uid-file-message-config">
    <el-form :model="config" label-width="120px">
      <!-- 功能说明 -->
      <el-form-item>
        <el-alert
          title="文件上传UID私信"
          type="info"
          :closable="false"
          show-icon
        >
          <div slot="description">
            <p>此功能使用上传的UID文件进行批量私信</p>
            <p>请先在上方的"UID文件管理"区域上传UID文件并配置分配参数</p>
            <p>系统会根据配置自动分配UID给各个设备执行</p>
          </div>
        </el-alert>
      </el-form-item>

      <!-- 小红书应用选择 -->
      <el-form-item label="小红书应用">
        <el-select
          v-model="config.selectedApp"
          placeholder="请选择要使用的小红书应用"
          @change="onAppSelectionChange"
          style="width: 100%"
        >
          <el-option
            v-for="app in xiaohongshuApps"
            :key="app.text"
            :label="app.text"
            :value="app.text"
          >
            <span style="float: left">{{ app.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ app.method === 'keyword' ? '关键词' : '正则' }}
            </span>
          </el-option>
        </el-select>
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          选择设备上要使用的小红书应用版本
        </div>
      </el-form-item>

      <!-- 私信内容 -->
      <el-form-item label="私信内容">
        <el-input
          v-model="config.message"
          type="textarea"
          :rows="4"
          placeholder="请输入要发送的私信内容"
          maxlength="500"
          show-word-limit
          @blur="onInputChange"
          @change="onInputChange"
        />
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          建议私信内容简洁友好，避免过于商业化的语言
        </div>
      </el-form-item>

      <!-- 执行间隔 -->
      <el-form-item label="执行间隔">
        <el-input-number
          v-model="config.delay"
          :min="5"
          :max="60"
          @change="onInputChange"
          style="width: 150px"
        />
        <span style="margin-left: 10px; color: #909399;">秒（建议5-30秒）</span>
      </el-form-item>

      <!-- 高级选项 -->
      <el-form-item label="高级选项">
        <el-checkbox-group v-model="config.advancedOptions" @change="onInputChange">
          <el-checkbox label="enableDetailLog">启用详细日志</el-checkbox>
          <el-checkbox label="skipUsedUids">跳过已使用的UID</el-checkbox>
          <el-checkbox label="autoMarkUsed">自动标记UID为已使用</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 实时状态显示区域 -->
      <div class="realtime-status-section" style="margin-bottom: 20px;">
        <el-divider content-position="left">
          <span style="color: #409EFF; font-weight: bold;">实时状态</span>
        </el-divider>

        <div class="status-grid" style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
          <div class="status-item">
            <span class="status-label">已处理UID：</span>
            <span class="status-value" style="color: #67C23A; font-weight: bold;">{{ processedUidCount }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">成功私信：</span>
            <span class="status-value" style="color: #67C23A; font-weight: bold;">{{ successCount }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">失败私信：</span>
            <span class="status-value" style="color: #F56C6C; font-weight: bold;">{{ failedCount }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">已处理步骤：</span>
            <span class="status-value" style="color: #E6A23C; font-weight: bold;">{{ processedStepCount }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">搜索尝试次数：</span>
            <span class="status-value" style="color: #F56C6C; font-weight: bold;">{{ searchAttemptCount }}</span>
          </div>
        </div>

        <div class="current-status" style="margin-bottom: 15px;">
          <span class="status-label">当前状态：</span>
          <span class="status-value" style="color: #409EFF; font-weight: bold;">{{ currentStatus || '等待开始' }}</span>
        </div>
      </div>

      <!-- 脚本控制按钮 -->
      <el-form-item label="脚本控制">
        <el-button
          type="primary"
          size="small"
          @click="startScript"
          :disabled="isScriptRunning || !canExecute"
          :loading="isScriptRunning"
        >
          {{ isScriptRunning ? '脚本执行中...' : '开始执行' }}
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="stopScript"
          :disabled="!isScriptRunning"
        >
          停止脚本
        </el-button>
        <span v-if="isScriptRunning" style="margin-left: 10px; color: #67C23A; font-size: 12px;">
          脚本正在执行中...
        </span>
        <span v-else-if="isScriptCompleted" style="margin-left: 10px; color: #409EFF; font-size: 12px;">
          脚本执行完成，1分钟后可重新执行
        </span>
        <span v-else style="margin-left: 10px; color: #909399; font-size: 12px;">
          脚本未运行
        </span>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import io from 'socket.io-client'
import { mapState } from 'vuex'
import xiaohongshuAppSelector from '@/mixins/xiaohongshuAppSelector'

export default {
  name: 'UidFileMessageConfig',
  mixins: [xiaohongshuAppSelector],
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    deviceId: {
      type: String,
      default: ''
    },
    selectedDevices: {
      type: Array,
      default: () => []
    },
    onlineDevices: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      config: {
        inputMode: 'file', // 固定为文件模式
        message: '',
        delay: 15,
        advancedOptions: ['autoMarkUsed'],
        selectedFileId: null,
        totalUidCount: 10,
        uidsPerDevice: 5,
        selectedApp: '' // 选择的小红书应用
      },
      currentLogId: null,
      currentTaskId: null,
      isInternalUpdate: false, // 标志位，防止循环更新

      // 实时状态变量
      processedUidCount: 0,
      successCount: 0,
      failedCount: 0,
      processedStepCount: 0,
      searchAttemptCount: 0,
      currentStatus: '等待开始',

      // Socket连接
      socket: null
    }
  },
  computed: {
    ...mapState({
      token: state => state.auth.token
    }),

    // 从Vuex获取脚本运行状态
    isScriptRunning() {
      const functionState = this.$store.getters['xiaohongshu/getFunctionState']('uidFileMessage')
      return functionState ? functionState.isScriptRunning : false
    },

    // 从Vuex获取脚本完成状态
    isScriptCompleted() {
      const functionState = this.$store.getters['xiaohongshu/getFunctionState']('uidFileMessage')
      return functionState ? functionState.isScriptCompleted : false
    },

    // 检查是否可以执行
    canExecute() {
      // 验证私信内容
      return this.config.message && this.config.message.trim().length > 0
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.config = { ...this.config, ...newVal }
        }
      },
      deep: true
    }
  },
  mounted() {
    console.log('[UidFileMessageConfig] 组件已挂载')
    console.log('[UidFileMessageConfig] 挂载时的初始状态:', {
      currentTaskId: this.currentTaskId,
      currentLogId: this.currentLogId,
      deviceId: this.deviceId
    })

    this.loadConfig()

    // 如果有初始值，使用初始值
    if (this.value && Object.keys(this.value).length > 0) {
      this.config = { ...this.config, ...this.value }
    }

    // 恢复组件状态（异步）
    console.log('[UidFileMessageConfig] 开始调用状态恢复方法...')
    try {
      this.restoreComponentState().then(() => {
        console.log('[UidFileMessageConfig] 状态恢复完成，开始初始化Socket连接')
        console.log('[UidFileMessageConfig] 恢复后的状态:', {
          currentTaskId: this.currentTaskId,
          currentLogId: this.currentLogId,
          deviceId: this.deviceId
        })
        // 初始化Socket连接
        this.initializeSocket()
      }).catch(error => {
        console.error('[UidFileMessageConfig] 状态恢复失败:', error)
        console.error('[UidFileMessageConfig] 错误堆栈:', error.stack)
        // 即使恢复失败也要初始化Socket连接
        this.initializeSocket()
      })
    } catch (syncError) {
      console.error('[UidFileMessageConfig] 调用状态恢复方法时发生同步错误:', syncError)
      console.error('[UidFileMessageConfig] 同步错误堆栈:', syncError.stack)
      // 发生同步错误时也要初始化Socket连接
      this.initializeSocket()
    }

    // 发出初始配置
    this.onInputChange()

    // 监听任务开始事件
    this.$root.$on('xiaohongshu-task-started', (data) => {
      console.log('[UidFileMessageConfig] 收到任务开始事件:', data)
      if (data.functionType === 'uidFileMessage' && (!this.deviceId || data.deviceId === this.deviceId)) {
        // 保存当前执行的logId和taskId
        if (data.logId) {
          this.currentLogId = data.logId
          console.log('[UidFileMessageConfig] 保存logId:', this.currentLogId)
        }
        if (data.taskId) {
          this.currentTaskId = data.taskId
          console.log('[UidFileMessageConfig] 保存taskId:', this.currentTaskId)
        }

        // 重置实时状态
        this.resetRealtimeStatus()

        console.log('[UidFileMessageConfig] 状态已更新:', {
          isScriptRunning: this.isScriptRunning,
          isScriptCompleted: this.isScriptCompleted,
          currentLogId: this.currentLogId,
          currentTaskId: this.currentTaskId
        })
      }
    })

    // 监听任务停止事件
    this.$root.$on('xiaohongshu-task-stopped', (data) => {
      console.log('[UidFileMessageConfig] 收到任务停止事件:', data)
      const reason = data.reason || 'manual'

      // 处理批量停止或单设备停止
      const shouldStop = data.functionType === 'uidFileMessage' && (
        reason === 'batch_stop' || // 批量停止时停止所有设备
        !this.deviceId || // 没有设备ID时停止
        data.deviceId === this.deviceId // 设备ID匹配时停止
      )

      if (shouldStop) {
        console.log(`[UidFileMessageConfig] 文件UID私信任务停止，原因: ${reason}`)

        // 清空保存的ID
        this.currentLogId = null
        this.currentTaskId = null

        // 重置实时状态
        this.resetRealtimeStatus()

        console.log('[UidFileMessageConfig] 已清空保存的logId和taskId')

        // 如果是批量停止，显示提示信息
        if (reason === 'batch_stop') {
          this.$message.info('文件上传UID私信功能已被批量停止')
        }
      }
    })

    // 监听任务完成事件
    this.$root.$on('xiaohongshu-task-completed', (data) => {
      console.log('[UidFileMessageConfig] 收到任务完成事件:', data)
      if (data.functionType === 'uidFileMessage' && (!this.deviceId || data.deviceId === this.deviceId)) {
        // 清空保存的ID
        this.currentLogId = null
        this.currentTaskId = null
        console.log('[UidFileMessageConfig] 已清空保存的logId和taskId')
      }
    })

    // 监听设备离线事件
    this.$root.$on('device-offline', (data) => {
      if (data.deviceId === this.deviceId) {
        console.log('[UidFileMessageConfig] 当前设备离线，重置状态')
        this.handleDeviceOffline()
      }
    })
  },

  beforeDestroy() {
    // 保存配置
    this.saveConfig()

    // 保存组件状态
    this.saveComponentState()

    // 断开socket连接
    if (this.socket) {
      this.socket.disconnect()
      console.log('[UidFileMessageConfig] Socket连接已断开')
    }

    // 移除事件监听
    this.$root.$off('xiaohongshu-task-started')
    this.$root.$off('xiaohongshu-task-stopped')
    this.$root.$off('xiaohongshu-task-completed')
    this.$root.$off('device-offline')
  },
  methods: {
    onInputChange() {
      console.log('=== 文件UID私信配置变更开始 ===')
      console.log('当前配置:', JSON.stringify(this.config, null, 2))

      const processedConfig = {
        ...this.config,
        inputMode: 'file' // 确保始终为文件模式
      }

      // 验证私信内容
      if (!processedConfig.message || processedConfig.message.trim().length === 0) {
        console.warn('警告：私信内容为空')
        this.$message.warning('请输入私信内容')
        return
      }

      // 处理高级选项
      if (this.config.advancedOptions) {
        processedConfig.enableDetailLog = this.config.advancedOptions.includes('enableDetailLog')
        processedConfig.skipUsedUids = this.config.advancedOptions.includes('skipUsedUids')
        processedConfig.autoMarkUsed = this.config.advancedOptions.includes('autoMarkUsed')
      }

      // 文件模式下UID列表为空，由服务器端处理
      processedConfig.uidList = []

      console.log('最终处理后的配置:', JSON.stringify(processedConfig, null, 2))

      this.$emit('input', processedConfig)
      this.$emit('update', processedConfig)
      this.saveConfig()

      console.log('=== 文件UID私信配置变更结束 ===')
    },

    // 开始执行脚本
    async startScript() {
      if (!this.canExecute) {
        this.$message.warning('请输入私信内容')
        return
      }

      try {
        console.log('[UidFileMessageConfig] 开始执行文件UID私信脚本')

        // 通过父组件的方法执行脚本
        this.$emit('execute-script', {
          functionType: 'uidFileMessage',
          config: this.config,
          deviceId: this.deviceId
        })

        console.log('[UidFileMessageConfig] 脚本执行请求已发送')
      } catch (error) {
        console.error('[UidFileMessageConfig] 执行脚本失败:', error)
        this.$message.error('执行脚本失败: ' + error.message)
      }
    },

    stopScript() {
      console.log('[UidFileMessageConfig] 停止脚本')

      // 发送停止事件
      this.$root.$emit('xiaohongshu-stop-script', {
        functionType: 'uidFileMessage',
        deviceId: this.deviceId,
        logId: this.currentLogId,
        taskId: this.currentTaskId
      })

      // 更新Vuex状态
      this.$store.dispatch('xiaohongshu/setFunctionState', {
        functionType: 'uidFileMessage',
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          config: this.config
        }
      })

      this.$message.success('已发送停止脚本命令')
    },

    // 执行脚本
    executeScript() {
      if (!this.canExecute) {
        this.$message.warning('请填写完整的配置信息')
        return
      }

      console.log('[UidFileMessageConfig] 准备执行脚本')
      this.$emit('execute-script', {
        functionType: 'uidFileMessage',
        config: this.config,
        deviceId: this.deviceId
      })
    },

    saveConfig() {
      const configKey = `uidFileMessageConfig_${this.deviceId}`
      try {
        localStorage.setItem(configKey, JSON.stringify(this.config))
      } catch (error) {
        console.warn('保存配置失败:', error)
      }
    },

    loadConfig() {
      const configKey = `uidFileMessageConfig_${this.deviceId}`
      try {
        const savedConfig = localStorage.getItem(configKey)
        if (savedConfig) {
          const parsed = JSON.parse(savedConfig)
          this.config = { ...this.config, ...parsed }
          console.log('加载已保存的配置:', this.config)
        }
      } catch (error) {
        console.warn('加载配置失败:', error)
      }
    },

    // 旧的restoreComponentState方法已删除，使用新的异步版本

    // 处理实时状态更新
    handleRealtimeStatus(data) {
      console.log('🔄 [UidFileMessageConfig] 收到实时状态数据:', data)
      console.log('📋 [UidFileMessageConfig] 当前组件taskId:', this.currentTaskId)
      console.log('📋 [UidFileMessageConfig] 数据中的taskId:', data.taskId)
      console.log('🔍 [UidFileMessageConfig] taskId匹配:', this.currentTaskId && data.taskId === this.currentTaskId)

      // 调试：检查组件的所有相关状态
      console.log('🔍 [UidFileMessageConfig] 组件状态调试:', {
        currentTaskId: this.currentTaskId,
        currentLogId: this.currentLogId,
        isScriptRunning: this.isScriptRunning,
        isScriptCompleted: this.isScriptCompleted,
        deviceId: this.deviceId
      })

      // 临时解决方案：如果currentTaskId为空，但接收到了taskId，尝试恢复
      if (!this.currentTaskId && data.taskId && data.taskId.includes('uidFileMessage')) {
        console.log('🔄 [UidFileMessageConfig] 检测到taskId为空，尝试从WebSocket数据恢复taskId:', data.taskId)
        this.currentTaskId = data.taskId
        console.log('✅ [UidFileMessageConfig] 已从WebSocket数据恢复taskId:', this.currentTaskId)

        // 保存恢复的状态
        this.saveComponentState()
      }

      if (this.currentTaskId && data.taskId === this.currentTaskId) {
        console.log('✅ [UidFileMessageConfig] taskId匹配，更新实时状态:', data)

        // 更新统计数据
        if (data.processedUidCount !== undefined) {
          this.processedUidCount = data.processedUidCount
          console.log('📊 [UidFileMessageConfig] 更新已处理UID数:', this.processedUidCount)
        }
        if (data.successCount !== undefined) {
          this.successCount = data.successCount
          console.log('📊 [UidFileMessageConfig] 更新成功私信数:', this.successCount)
        }
        if (data.failedCount !== undefined) {
          this.failedCount = data.failedCount
          console.log('📊 [UidFileMessageConfig] 更新失败私信数:', this.failedCount)
        }
        if (data.processedStepCount !== undefined) {
          this.processedStepCount = data.processedStepCount
          console.log('📊 [UidFileMessageConfig] 更新已处理步骤数:', this.processedStepCount)
        }
        if (data.searchAttemptCount !== undefined) {
          this.searchAttemptCount = data.searchAttemptCount
          console.log('📊 [UidFileMessageConfig] 更新搜索尝试次数:', this.searchAttemptCount)
        }
        if (data.currentStatus) {
          this.currentStatus = data.currentStatus
          console.log('📊 [UidFileMessageConfig] 更新当前状态:', this.currentStatus)
        }

        console.log('✅ [UidFileMessageConfig] 实时状态已更新:', {
          processedUidCount: this.processedUidCount,
          successCount: this.successCount,
          failedCount: this.failedCount,
          processedStepCount: this.processedStepCount,
          searchAttemptCount: this.searchAttemptCount,
          currentStatus: this.currentStatus
        })

        // 强制更新视图
        this.$forceUpdate()
        console.log('🔄 [UidFileMessageConfig] 已强制更新视图')
      } else {
        console.log('❌ [UidFileMessageConfig] taskId不匹配或currentTaskId为空，忽略实时状态更新')
      }
    },

    // 重置实时状态
    resetRealtimeStatus() {
      this.processedUidCount = 0
      this.successCount = 0
      this.failedCount = 0
      this.processedStepCount = 0
      this.searchAttemptCount = 0
      this.currentStatus = '等待开始'
      console.log('[UidFileMessageConfig] 实时状态已重置')
    },

    // 初始化Socket连接 - 使用统一的WebSocket管理器
    async initializeSocket() {
      try {
        console.log('🔧 [UidFileMessageConfig] 使用统一WebSocket管理器')
        // 使用全局WebSocket管理器，不创建独立连接
        const { getWebSocketManager } = await import('@/utils/websocketManager')
        this.wsManager = getWebSocketManager()

        // 确保连接
        await this.wsManager.init()

        // 获取socket实例用于事件监听
        this.socket = this.wsManager.socket

        console.log('✅ [UidFileMessageConfig] 已连接到统一WebSocket管理器')
      } catch (error) {
        console.error('❌ [UidFileMessageConfig] WebSocket连接失败:', error)
      }

      this.socket.on('disconnect', () => {
        console.log('❌ [UidFileMessageConfig] Socket连接断开')
      })

      // 监听实时状态更新
      this.socket.on('xiaohongshu_realtime_status', (data) => {
        console.log('🎯 [UidFileMessageConfig] 收到WebSocket实时状态事件:', data)
        this.handleRealtimeStatus(data)
      })

      console.log('[UidFileMessageConfig] Socket初始化完成')
    },

    // 保存组件状态
    async saveComponentState() {
      const { saveComponentState } = await import('@/utils/stateManager')

      const stateData = {
        config: this.config,
        currentLogId: this.currentLogId,
        currentTaskId: this.currentTaskId,
        isScriptRunning: this.isScriptRunning,
        isScriptCompleted: this.isScriptCompleted,
        // 保存实时状态数据
        realtimeData: {
          processedUidCount: this.processedUidCount,
          successCount: this.successCount,
          failedCount: this.failedCount,
          processedStepCount: this.processedStepCount,
          searchAttemptCount: this.searchAttemptCount,
          currentStatus: this.currentStatus
        }
      }

      await saveComponentState(this, 'uidFileMessage', stateData)
      console.log('[UidFileMessageConfig] 组件状态已保存，currentTaskId:', this.currentTaskId)
    },

    // 恢复组件状态
    async restoreComponentState() {
      console.log('🚀 [UidFileMessageConfig] restoreComponentState方法被调用')
      try {
        console.log('[UidFileMessageConfig] 开始恢复组件状态...')
        console.log('[UidFileMessageConfig] 当前deviceId:', this.deviceId)

        // 先检查Vuex中的状态
        const vuexState = this.$store.getters['xiaohongshu/getFunctionState']('uidFileMessage')
        console.log('[UidFileMessageConfig] Vuex中的状态:', vuexState)

        // 检查localStorage备份
        const backupKey = `uidFileMessage_backup_${this.deviceId || 'default'}`
        const backupData = localStorage.getItem(backupKey)
        console.log('[UidFileMessageConfig] localStorage备份键:', backupKey)
        console.log('[UidFileMessageConfig] localStorage备份数据:', backupData)

        const { restoreComponentState } = await import('@/utils/stateManager')
        const functionState = await restoreComponentState(this, 'uidFileMessage')

        console.log('[UidFileMessageConfig] 状态管理工具返回的状态:', functionState)

        if (functionState && Object.keys(functionState).length > 0) {
          console.log('[UidFileMessageConfig] 恢复组件状态:', functionState)

          // 恢复配置
          if (functionState.config && Object.keys(functionState.config).length > 0) {
            this.config = { ...this.config, ...functionState.config }
          }

          // 恢复任务信息
          this.currentLogId = functionState.currentLogId || null
          this.currentTaskId = functionState.currentTaskId || null
          this.isScriptRunning = functionState.isScriptRunning || false
          this.isScriptCompleted = functionState.isScriptCompleted || false

          // 恢复实时状态数据
          if (functionState.realtimeData) {
            this.processedUidCount = functionState.realtimeData.processedUidCount || 0
            this.successCount = functionState.realtimeData.successCount || 0
            this.failedCount = functionState.realtimeData.failedCount || 0
            this.processedStepCount = functionState.realtimeData.processedStepCount || 0
            this.searchAttemptCount = functionState.realtimeData.searchAttemptCount || 0
            this.currentStatus = functionState.realtimeData.currentStatus || '等待开始'
            console.log('[UidFileMessageConfig] 实时状态已恢复:', functionState.realtimeData)
          }

          console.log('[UidFileMessageConfig] 组件状态已恢复，currentTaskId:', this.currentTaskId)
        } else {
          console.log('[UidFileMessageConfig] 没有找到保存的状态，使用默认值')
          // 确保初始化默认值
          this.currentLogId = null
          this.currentTaskId = null
          this.isScriptRunning = false
          this.isScriptCompleted = false
        }
      } catch (error) {
        console.error('[UidFileMessageConfig] 恢复组件状态失败:', error)
      }
    },

    // 处理设备离线事件
    handleDeviceOffline() {
      console.log('[UidFileMessageConfig] 处理设备离线，重置状态')

      // 重置脚本执行状态
      this.$store.dispatch('xiaohongshu/setFunctionState', {
        functionType: 'uidFileMessage',
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          config: this.config
        }
      })

      // 重置实时状态
      this.resetRealtimeStatus()

      // 清除任务ID
      this.currentTaskId = null
      this.currentLogId = null

      // 保存状态
      this.saveComponentState()

      console.log('[UidFileMessageConfig] 设备离线处理完成')
    }
  }
}
</script>

<style scoped>
.uid-file-message-config {
  padding: 20px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-textarea {
  font-family: inherit;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.el-checkbox {
  margin-right: 0;
}
</style>
