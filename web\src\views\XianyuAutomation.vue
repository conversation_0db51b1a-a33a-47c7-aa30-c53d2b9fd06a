<template>
  <div class="xianyu-automation">
    <div class="page-header">
      <div class="header-content">
        <div>
          <h2>闲鱼自动化工具</h2>
          <p>专业的闲鱼运营自动化工具，支持关键词搜索私信等功能</p>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            icon="el-icon-notebook-1"
            @click="goToLogs"
          >
            查看执行日志
          </el-button>
        </div>
      </div>
    </div>

    <!-- 功能选择卡片 -->
    <el-row :gutter="20" class="function-cards">
      <el-col :span="6" v-for="func in functions" :key="func.key">
        <el-card
          class="function-card"
          :class="{ active: selectedFunction === func.key }"
          @click.native="selectFunction(func.key)"
          shadow="hover"
        >
          <div class="card-content">
            <i :class="func.icon" class="function-icon"></i>
            <h3>{{ func.name }}</h3>
            <p>{{ func.description }}</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 配置面板 -->
    <el-row :gutter="20" v-if="selectedFunction">
      <!-- 左侧：设备信息 -->
      <el-col :span="8">
        <DeviceInfo
          :enableBatchSelect="true"
          :selectedDeviceIds="selectedDevices"
          :currentFunction="selectedFunction"
          @device-selected="handleDeviceSelected"
          @device-removed="handleDeviceRemoved"
          @devices-selection-changed="handleDevicesSelectionChanged"
        />
      </el-col>

      <!-- 右侧：功能配置 -->
      <el-col :span="16">
        <el-card class="config-panel">
          <div slot="header" class="clearfix">
            <span>{{ getCurrentFunctionName() }}配置</span>
            <el-button
              type="warning"
              size="small"
              @click="resetAllStates"
              style="float: right; margin-top: -3px; margin-left: 10px;">
              <i class="el-icon-refresh-left"></i> 重置状态
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="cleanupTestData"
              style="float: right; margin-top: -3px;">
              <i class="el-icon-delete"></i> 清理测试数据
            </el-button>
          </div>

          <!-- 设备选择提示 -->
          <div class="config-section" v-if="selectedDevices.length === 0">
            <el-alert
              title="请先选择执行设备"
              type="warning"
              :closable="false"
              show-icon
            >
              请在左侧设备信息中选择要执行脚本的设备
            </el-alert>
          </div>

          <!-- 已选设备显示 -->
          <div class="config-section" v-else>
            <h4>已选设备 ({{ selectedDevices.length }}个)</h4>
            <el-tag
              v-for="deviceId in selectedDevices"
              :key="deviceId"
              closable
              @close="removeDevice(deviceId)"
              :type="getDeviceTagType(deviceId)"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ getDeviceNameWithStatus(deviceId) }}
            </el-tag>
          </div>

          <!-- 多设备配置标签页 -->
          <div class="config-section" v-if="selectedDevices.length > 0">
            <div v-if="!getComponentName()" class="no-component">
              <el-alert
                title="组件加载错误"
                type="warning"
                :closable="false"
                show-icon
              >
                未找到对应的配置组件：{{ selectedFunction }}
              </el-alert>
            </div>

            <div v-else>
              <el-tabs
                v-model="activeDeviceTab"
                type="card"
                @tab-click="handleTabClick"
                class="device-config-tabs"
              >
                <el-tab-pane
                  v-for="deviceId in selectedDevices"
                  :key="deviceId"
                  :label="getDeviceTabLabel(deviceId)"
                  :name="deviceId"
                >
                  <div class="device-config-content">
                    <div class="device-config-header">
                      <h5>{{ getDeviceNameWithStatus(deviceId) }} - 配置参数</h5>
                      <el-button
                        type="primary"
                        size="small"
                        @click="executeForDevice(deviceId)"
                        :loading="executing"
                        :disabled="!isDeviceConfigValid(deviceId)"
                      >
                        执行此设备
                      </el-button>
                    </div>

                    <!-- 关键词私信配置 -->
                    <XianyuKeywordMessageConfig
                      v-if="selectedFunction === 'keywordMessage'"
                      :ref="`config_${deviceId}`"
                      :device-id="deviceId"
                      @config-change="handleConfigChange"
                      @stop-script="handleStopScript"
                    />
                  </div>
                </el-tab-pane>
              </el-tabs>

              <!-- 批量执行按钮 -->
              <div class="batch-execute-section">
                <el-button
                  type="success"
                  size="medium"
                  @click="executeAllDevices"
                  :loading="executing"
                  :disabled="!hasValidConfigs()"
                  icon="el-icon-s-promotion"
                >
                  批量执行所有设备 ({{ getValidConfigCount() }}/{{ selectedDevices.length }})
                </el-button>
                <el-button
                  type="info"
                  size="medium"
                  @click="copyConfigToAll"
                  :disabled="selectedDevices.length <= 1 || !activeDeviceTab"
                  icon="el-icon-document-copy"
                >
                  复制当前配置到所有设备
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      </el-row>

      <!-- 设备信息统计 -->
      <div class="device-stats">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-mobile-phone"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ connectedDevicesCount }}</div>
              <div class="stat-label">连接设备</div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-card">
            <div class="stat-icon online">
              <i class="el-icon-success"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ onlineDevicesCount }}</div>
              <div class="stat-label">在线设备</div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-card">
            <div class="stat-icon busy">
              <i class="el-icon-loading"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ busyDevicesCount }}</div>
              <div class="stat-label">忙碌设备</div>
            </div>
          </div>
        </el-col>
      </el-row>
      </div>
    </div>
</template>

<script>
// 导入配置组件
import XianyuKeywordMessageConfig from '@/components/xianyu/XianyuKeywordMessageConfig.vue'
import DeviceInfo from '@/components/xiaohongshu/DeviceInfo.vue'

export default {
  name: 'XianyuAutomation',
  components: {
    XianyuKeywordMessageConfig,
    DeviceInfo
  },
  data() {
    return {
      selectedFunction: '',
      selectedDevices: [],
      executing: false,
      deviceConfigs: {}, // 每个设备的独立配置
      functions: [
        {
          key: 'keywordMessage',
          name: '关键词私信',
          description: '搜索关键词商品并自动私信卖家',
          icon: 'el-icon-chat-dot-round'
        }
        // 后续可以添加更多功能
      ],
      activeDeviceTab: '',
      refreshTimer: null
    }
  },
  computed: {
    devices() {
      return this.$store.getters['device/devices'] || []
    },

    allDevices() {
      return this.$store.getters['device/devices'] || []
    },
    onlineDevices() {
      return this.allDevices.filter(device => device.status === 'online' || device.status === 'busy')
    },
    connectedDevicesCount() {
      return this.onlineDevices.length
    },
    onlineDevicesCount() {
      return this.onlineDevices.filter(device => device.status === 'online').length
    },
    busyDevicesCount() {
      return this.onlineDevices.filter(device => device.status === 'busy').length
    },
    canExecute() {
      return this.selectedDevices.length > 0 &&
             this.selectedFunction &&
             !this.executing &&
             this.hasValidConfigs()
    }
  },
  async mounted() {
    console.log('闲鱼自动化页面: mounted生命周期开始')

    // 检查认证状态
    const isAuthenticated = this.$store.getters['auth/isAuthenticated']
    console.log('认证状态:', isAuthenticated)

    if (!isAuthenticated) {
      console.warn('用户未登录，这可能导致设备选择没反应')
      this.$message.warning('请先登录以使用设备选择功能')
    }

    // 先加载设备列表
    await this.loadDevices()

    // 初始化Socket监听器
    this.initSocketListeners()

    // 恢复脚本执行状态（必须在恢复页面状态之前）
    await this.restoreExecutionState()

    // 等待一小段时间确保状态恢复完成
    await new Promise(resolve => setTimeout(resolve, 500))

    // 恢复页面状态（包括设备选择）
    await this.restorePageState()

    // 启动定时刷新
    this.startAutoRefresh()

    // 添加全局事件监听器
    this.$root.$on('xianyu-task-stopped', this.handleGlobalTaskStopped)
    this.$root.$on('xianyu-reset-all-states', this.handleGlobalResetStates)
  },
  beforeDestroy() {
    this.savePageState()
    this.cleanupSocketListeners()
    this.stopAutoRefresh()
    
    // 清理全局事件监听器
    this.$root.$off('xianyu-task-stopped', this.handleGlobalTaskStopped)
    this.$root.$off('xianyu-reset-all-states', this.handleGlobalResetStates)
  },
  methods: {
    async loadDevices() {
      try {
        console.log('闲鱼自动化: 开始加载设备列表')
        await this.$store.dispatch('device/fetchDevices')

        const devices = this.$store.getters['device/devices']
        console.log('闲鱼自动化: 设备列表加载完成', devices.length, '个设备')

        const onlineDevices = devices.filter(d => d.status === 'online')
        const busyDevices = devices.filter(d => d.status === 'busy')
        const availableDevices = devices.filter(d => d.status === 'online' || d.status === 'busy')

        console.log('设备状态统计:', {
          total: devices.length,
          online: onlineDevices.length,
          busy: busyDevices.length,
          available: availableDevices.length
        })

        if (availableDevices.length === 0) {
          console.warn('没有可用设备，这可能导致设备选择没反应')
        }
      } catch (error) {
        console.error('闲鱼自动化: 加载设备列表失败', error)
      }
    },

    selectFunction(functionKey) {
      console.log('闲鱼自动化: 选择功能:', functionKey)

      this.selectedFunction = functionKey
      this.selectedDevices = []
      this.deviceConfigs = {}
      this.activeDeviceTab = ''

      // 检查是否有正在执行当前功能的设备，如果有则自动选择
      this.$nextTick(async () => {
        // 确保设备列表已加载
        if (!this.allDevices || this.allDevices.length === 0) {
          console.log('闲鱼自动化: 设备列表未加载，重新加载...')
          await this.loadDevices()
        }

        // 等待一小段时间确保Vuex状态已更新
        await new Promise(resolve => setTimeout(resolve, 200))

        // 自动恢复正在执行的设备
        this.autoRestoreExecutingDevices()
      })

      this.savePageState()
    },

    getCurrentFunctionName() {
      const func = this.functions.find(f => f.key === this.selectedFunction)
      return func ? func.name : '未选择功能'
    },

    getComponentName() {
      // 根据选择的功能返回对应的组件名
      const componentMap = {
        'keywordMessage': 'XianyuKeywordMessageConfig'
      }
      return componentMap[this.selectedFunction] || null
    },

    getDeviceName(deviceId) {
      // 确保allDevices数组存在且不为空
      if (!this.allDevices || !Array.isArray(this.allDevices) || this.allDevices.length === 0) {
        return deviceId
      }

      const device = this.allDevices.find(d => d.device_id === deviceId)
      return device ? (device.device_name || deviceId) : deviceId
    },

    getDeviceNameWithStatus(deviceId) {
      // 确保allDevices数组存在且不为空
      if (!this.allDevices || !Array.isArray(this.allDevices) || this.allDevices.length === 0) {
        return deviceId
      }

      const device = this.allDevices.find(d => d.device_id === deviceId)
      if (!device) return deviceId

      const name = device.device_name || deviceId
      const status = device.status === 'busy' ? '忙碌' : '在线'
      return `${name} (${status})`
    },

    getDeviceTagType(deviceId) {
      // 确保allDevices数组存在且不为空
      if (!this.allDevices || !Array.isArray(this.allDevices) || this.allDevices.length === 0) {
        return 'info'
      }

      const device = this.allDevices.find(d => d.device_id === deviceId)
      if (!device) return 'info'
      return device.status === 'busy' ? 'warning' : 'success'
    },

    // 检查设备是否正在执行当前功能
    isDeviceExecutingCurrentFunction(deviceId) {
      if (!this.selectedFunction) return false
      
      console.log(`闲鱼自动化: 检查设备 ${deviceId} 是否正在执行功能 ${this.selectedFunction}`)
      
      // 从Vuex store获取该设备的运行状态
      const deviceTasks = this.$store.getters['xianyu/getDeviceTasks'](deviceId)
      console.log(`闲鱼自动化: 设备 ${deviceId} 的任务列表:`, deviceTasks)
      
      if (!deviceTasks || deviceTasks.length === 0) {
        console.log(`闲鱼自动化: 设备 ${deviceId} 没有任务`)
        return false
      }
      
      // 检查是否有当前功能的运行任务
      const hasCurrentFunctionTask = deviceTasks.some(task => {
        const matches = task.functionType === this.selectedFunction && task.status === 'running'
        console.log(`闲鱼自动化: 任务检查:`, {
          taskFunctionType: task.functionType,
          currentFunction: this.selectedFunction,
          taskStatus: task.status,
          matches: matches
        })
        return matches
      })
      
      console.log(`闲鱼自动化: 设备 ${deviceId} 是否正在执行当前功能:`, hasCurrentFunctionTask)
      return hasCurrentFunctionTask
    },

    // 自动恢复正在执行当前功能的设备
    autoRestoreExecutingDevices() {
      if (!this.selectedFunction) {
        console.log('闲鱼自动化: 没有选择功能，跳过自动恢复')
        return
      }

      console.log('闲鱼自动化: 检查是否有正在执行当前功能的设备')
      console.log('闲鱼自动化: 当前功能:', this.selectedFunction)

      // 获取所有设备 - 优先使用页面本地的设备列表
      const allDevices = this.allDevices && this.allDevices.length > 0 ?
                        this.allDevices : this.$store.getters['device/devices']

      console.log('闲鱼自动化: 设备列表:', allDevices)
      console.log('闲鱼自动化: 当前Vuex状态:', {
        currentTasks: this.$store.getters['xianyu/getCurrentTasks'],
        functionStates: this.$store.state.xianyu.functionStates
      })

      // 查找正在执行当前功能的设备
      const executingDevices = allDevices.filter(device => {
        const isBusy = device.status === 'busy'

        // 检查设备是否正在执行当前功能
        let isExecutingCurrentFunction = false

        // 方法1：通过Vuex store检查
        const deviceTasks = this.$store.getters['xianyu/getDeviceTasks'](device.device_id)
        if (deviceTasks && deviceTasks.length > 0) {
          isExecutingCurrentFunction = deviceTasks.some(task =>
            task.functionType === this.selectedFunction && task.status === 'running'
          )
        }

        // 方法2：如果Vuex检查失败，通过功能状态检查
        if (!isExecutingCurrentFunction && isBusy) {
          const functionState = this.$store.state.xianyu.functionStates[this.selectedFunction]
          if (functionState && functionState.isScriptRunning) {
            // 检查当前任务中是否有该设备
            const currentTasks = this.$store.getters['xianyu/getCurrentTasks']
            isExecutingCurrentFunction = !!currentTasks[device.device_id]
          }
        }

        console.log(`闲鱼自动化: 设备 ${device.device_name} 检查:`, {
          status: device.status,
          isBusy,
          isExecutingCurrentFunction,
          deviceId: device.device_id,
          currentFunction: this.selectedFunction,
          deviceTasks: deviceTasks
        })

        return isBusy && isExecutingCurrentFunction
      })

      if (executingDevices.length > 0) {
        console.log('闲鱼自动化: 发现正在执行当前功能的设备:', executingDevices)

        // 自动选择这些设备
        executingDevices.forEach(device => {
          if (!this.selectedDevices.includes(device.device_id)) {
            this.selectedDevices.push(device.device_id)
            console.log('闲鱼自动化: 自动选择设备:', device.device_name)
          }
        })

        // 设置活跃标签
        if (this.selectedDevices.length > 0 && !this.activeDeviceTab) {
          this.activeDeviceTab = this.selectedDevices[0]
        }

        this.savePageState()

        // 显示提示信息
        this.$message.success(`已自动选择 ${executingDevices.length} 个正在执行的设备`)
      } else {
        console.log('闲鱼自动化: 没有发现正在执行当前功能的设备')
      }
    },

    removeDevice(deviceId) {
      const index = this.selectedDevices.indexOf(deviceId)
      if (index > -1) {
        this.selectedDevices.splice(index, 1)

        // 如果移除的是当前活跃标签，切换到第一个设备
        if (this.activeDeviceTab === deviceId && this.selectedDevices.length > 0) {
          this.activeDeviceTab = this.selectedDevices[0]
        } else if (this.selectedDevices.length === 0) {
          this.activeDeviceTab = ''
        }

        this.savePageState()
      }
    },

    // DeviceInfo组件事件处理
    handleDeviceSelected(device) {
      // 检查设备是否忙碌
      if (device && device.status === 'busy') {
        // 检查设备是否正在执行当前功能
        const isExecutingCurrentFunction = this.isDeviceExecutingCurrentFunction(device.device_id)
        
        if (!isExecutingCurrentFunction) {
          this.$message.warning(`设备 ${device.device_name} 正在执行其他脚本，无法选择`)
          console.log('闲鱼自动化: 尝试选择忙碌设备，已拒绝', device)
          return
        } else {
          // 设备正在执行当前功能，允许选择
          console.log('闲鱼自动化: 设备正在执行当前功能，允许选择', device)
        }
      }
      
      // 添加设备到选中列表（如果还没有选中）
      if (device && !this.selectedDevices.includes(device.device_id)) {
        this.selectedDevices.push(device.device_id)
        
        // 如果是第一个设备，设置为活跃标签
        if (this.selectedDevices.length === 1) {
          this.activeDeviceTab = device.device_id
        }
        
        console.log('闲鱼自动化: 已选择设备', device.device_name)
        this.savePageState()
      }
    },

    handleDeviceRemoved(deviceId) {
      console.log('闲鱼自动化: 设备移除事件', deviceId)
      this.removeDevice(deviceId)
    },

    handleDevicesSelectionChanged(selectedDeviceIds) {
      console.log('闲鱼自动化: 设备选择变化事件', selectedDeviceIds)
      this.selectedDevices = [...selectedDeviceIds]

      // 设置活跃标签
      if (this.selectedDevices.length > 0 && !this.activeDeviceTab) {
        this.activeDeviceTab = this.selectedDevices[0]
        console.log('闲鱼自动化: 设置活跃标签', this.activeDeviceTab)
      }

      // 如果当前活跃标签的设备被取消选择，切换到第一个选中的设备
      if (!this.selectedDevices.includes(this.activeDeviceTab) && this.selectedDevices.length > 0) {
        this.activeDeviceTab = this.selectedDevices[0]
        console.log('闲鱼自动化: 切换活跃标签', this.activeDeviceTab)
      } else if (this.selectedDevices.length === 0) {
        this.activeDeviceTab = ''
        console.log('闲鱼自动化: 清空活跃标签')
      }

      console.log('闲鱼自动化: 当前选中设备', this.selectedDevices)
      this.savePageState()
    },

    handleTabClick(tab) {
      this.activeDeviceTab = tab.name
    },

    handleConfigChange(data) {
      this.deviceConfigs[data.deviceId] = data.config
      console.log('设备配置更新:', data)
    },

    async handleStopScript(data) {
      console.log('停止脚本请求:', data)

      try {
        // 直接调用闲鱼停止API
        const response = await this.$http.post('/api/xianyu/stop', {
          deviceId: data.deviceId,
          taskId: data.taskId || null
        })

        if (response.data.success) {
          this.$message.success('停止命令已发送')
          console.log('闲鱼脚本停止成功:', response.data)

          // 立即清理前端状态
          this.$store.dispatch('xianyu/clearDeviceExecutionState', data.deviceId)

          // 立即更新设备状态为在线
          this.$store.dispatch('device/updateDeviceStatus', {
            deviceId: data.deviceId,
            status: 'online',
            lastSeen: new Date()
          })

          // 刷新设备列表
          this.loadDevices()

          // 通知配置组件任务已停止
          this.$root.$emit('xianyu-task-stopped', {
            functionType: data.functionType || 'keywordMessage',
            deviceId: data.deviceId,
            taskId: data.taskId,
            message: '任务已停止'
          })

        } else {
          this.$message.error('停止失败: ' + response.data.message)
          console.error('闲鱼脚本停止失败:', response.data)
        }
      } catch (error) {
        console.error('停止闲鱼脚本失败:', error)
        this.$message.error('停止失败: ' + (error.response?.data?.message || error.message))
      }
    },

    // 检查设备配置是否有效
    isDeviceConfigValid(deviceId) {
      const config = this.deviceConfigs[deviceId]
      if (!config) return false

      // 根据功能类型验证配置
      switch (this.selectedFunction) {
        case 'keywordMessage':
          return config.keyword && config.keyword.trim() !== '' &&
                 config.message && config.message.trim() !== '' &&
                 config.targetCount > 0
        default:
          return false
      }
    },

    // 获取有效配置的数量
    getValidConfigCount() {
      return this.selectedDevices.filter(deviceId => this.isDeviceConfigValid(deviceId)).length
    },

    // 检查是否有有效配置
    hasValidConfigs() {
      return this.getValidConfigCount() > 0
    },

    async executeFunction() {
      if (!this.canExecute) return

      this.executing = true

      try {
        console.log('开始执行闲鱼自动化任务')

        // 收集所有设备的配置
        const deviceConfigs = {}
        for (const deviceId of this.selectedDevices) {
          const configRef = this.$refs[`config_${deviceId}`]
          if (configRef && configRef[0]) {
            deviceConfigs[deviceId] = configRef[0].getConfig()
          } else {
            console.error(`无法获取设备 ${deviceId} 的配置`)
            this.$message.error(`无法获取设备 ${deviceId} 的配置`)
            return
          }
        }

        // 构建执行参数
        const params = {
          functionType: this.selectedFunction,
          deviceConfigs: deviceConfigs,
          deviceIds: this.selectedDevices
        }

        console.log('闲鱼自动化执行参数:', params)

        // 发送执行请求
        const response = await this.$http.post('/api/xianyu/execute', params)

        if (response.data.success) {
          this.$message.success('任务开始执行')

          // 更新Vuex状态
          this.selectedDevices.forEach(deviceId => {
            this.$store.dispatch('xianyu/taskStarted', {
              functionType: this.selectedFunction,
              taskId: response.data.taskId || Date.now().toString(),
              logId: `${response.data.taskId || Date.now().toString()}_${deviceId}`,
              config: deviceConfigs[deviceId],
              deviceId: deviceId
            })
          })

          // 发送任务开始事件
          this.selectedDevices.forEach(deviceId => {
            this.$root.$emit('xianyu-task-started', {
              functionType: this.selectedFunction,
              deviceId: deviceId,
              config: deviceConfigs[deviceId]
            })
          })
        } else {
          this.$message.error('执行失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('执行闲鱼自动化任务失败:', error)
        this.$message.error('执行失败: ' + error.message)
      } finally {
        this.executing = false
      }
    },

    goToLogs() {
      this.$router.push('/xianyu-logs')
    },

    initSocketListeners() {
      // 初始化Socket事件监听
      const socket = this.$socket
      if (socket && socket.connected) {
        console.log('闲鱼自动化页面: 初始化Socket事件监听')

        // 监听设备状态更新
        socket.on('device_status_update', this.handleDeviceStatusUpdate)
        // 监听设备状态变化事件（包括离线）
        socket.on('device_status_changed', this.handleDeviceStatusChanged)
        // 监听设备离线事件（兼容旧版本）
        socket.on('device_offline', this.handleDeviceOffline)
        // 监听任务开始事件
        socket.on('xianyu_task_started', this.handleTaskStartedFromBackend)
        // 监听脚本状态更新事件
        socket.on('xianyu_status_update', this.handleStatusUpdate)
        // 监听任务更新事件（设备离线等情况）
        socket.on('xianyu_task_update', this.handleStatusUpdate)
        // 监听任务完成事件
        socket.on('xianyu_task_completed', this.handleTaskCompleted)
        // 监听任务停止事件
        socket.on('xianyu_task_stopped', this.handleTaskStopped)
        // 监听服务器关闭事件
        socket.on('server_shutdown', this.handleServerShutdown)
        // 监听Socket连接断开事件
        socket.on('disconnect', this.handleSocketDisconnect)
        socket.on('xianyu_task_stopped', this.handleTaskStopped)
        // 监听脚本执行完成事件（用于重置状态）
        socket.on('xianyu_execution_completed', this.handleExecutionCompleted)

        // 不需要重复注册客户端，WebSocketManager已经注册了全局客户端
        console.log('📡 [XianyuAutomation] 使用WebSocketManager的全局客户端连接')
      }
    },

    cleanupSocketListeners() {
      const socket = this.$socket
      if (socket) {
        socket.off('device_status_update', this.handleDeviceStatusUpdate)
        socket.off('device_status_changed', this.handleDeviceStatusChanged)
        socket.off('device_offline', this.handleDeviceOffline)
        socket.off('xianyu_task_started', this.handleTaskStartedFromBackend)
        socket.off('xianyu_status_update', this.handleStatusUpdate)
        socket.off('xianyu_task_update', this.handleStatusUpdate)
        socket.off('xianyu_task_completed', this.handleTaskCompleted)
        socket.off('xianyu_task_stopped', this.handleTaskStopped)
        socket.off('xianyu_execution_completed', this.handleExecutionCompleted)
        socket.off('server_shutdown', this.handleServerShutdown)
        socket.off('disconnect', this.handleSocketDisconnect)
      }
    },

    handleDeviceStatusUpdate(data) {
      console.log('闲鱼自动化: 收到设备状态更新事件:', data)

      // 立即更新Vuex中的设备状态
      this.$store.dispatch('device/updateDeviceStatus', {
        deviceId: data.deviceId,
        status: data.status,
        lastSeen: data.lastSeen || new Date()
      })

      // 如果设备状态变为在线，清理该设备的执行状态
      if (data.status === 'online') {
        console.log(`闲鱼自动化: 设备 ${data.deviceId} 状态变为在线，清理执行状态`)
        this.clearDeviceExecutionState(data.deviceId)

        // 通知配置组件设备状态已更新
        this.$root.$emit('device-status-updated', {
          deviceId: data.deviceId,
          status: data.status
        })
      }

      // 刷新设备列表以确保UI同步
      this.loadDevices()
    },

    // 处理设备状态变化事件（新版本）
    handleDeviceStatusChanged(data) {
      console.log('闲鱼自动化: 收到设备状态变化事件:', data)
      const { type, deviceId } = data

      if (type === 'device_disconnected') {
        this.handleDeviceDisconnected(deviceId)
      }
    },

    // 处理设备离线事件（兼容旧版本）
    handleDeviceOffline(data) {
      const { deviceId } = data
      this.handleDeviceDisconnected(deviceId)
    },

    // 统一处理设备断开连接
    handleDeviceDisconnected(deviceId) {
      console.log(`🔴 [XianyuAutomation] 处理设备断开连接: ${deviceId}`)
      console.log(`🔴 [XianyuAutomation] 当前选中设备列表:`, this.selectedDevices)
      console.log(`🔴 [XianyuAutomation] 当前设备配置键:`, Object.keys(this.deviceConfigs))

      // 尝试多种设备ID格式匹配
      let targetDeviceId = deviceId
      let selectedIndex = this.selectedDevices.indexOf(deviceId)

      // 如果直接匹配失败，尝试其他可能的格式
      if (selectedIndex === -1) {
        console.log(`🔴 [XianyuAutomation] 直接匹配失败，尝试其他格式`)

        // 尝试查找所有设备，看看是否有匹配的
        const allDevices = this.$store.getters['device/devices'] || []
        console.log(`🔴 [XianyuAutomation] 所有设备:`, allDevices.map(d => ({ id: d.device_id, name: d.device_name })))

        // 尝试通过设备名称或IP地址匹配
        const matchingDevice = allDevices.find(device => {
          return device.device_id === deviceId ||
                 device.device_name === deviceId ||
                 (device.ip_address && deviceId.includes(device.ip_address.replace(/\./g, '_')))
        })

        if (matchingDevice) {
          targetDeviceId = matchingDevice.device_id
          selectedIndex = this.selectedDevices.indexOf(targetDeviceId)
          console.log(`🔴 [XianyuAutomation] 通过匹配找到设备: ${targetDeviceId}`)
        }
      }

      console.log(`🔴 [XianyuAutomation] 最终使用的设备ID: ${targetDeviceId}`)
      console.log(`🔴 [XianyuAutomation] 设备在选中列表中的索引:`, selectedIndex)

      // 1. 从选中设备列表中移除该设备
      if (selectedIndex !== -1) {
        this.selectedDevices.splice(selectedIndex, 1)
        console.log(`🔴 [XianyuAutomation] 已从选中列表移除设备，新列表:`, this.selectedDevices)
        this.$message.warning(`设备 ${targetDeviceId} 已断开连接，已从选中列表移除`)
      } else {
        console.log(`🔴 [XianyuAutomation] 设备 ${deviceId} 不在选中列表中`)
      }

      // 2. 清理该设备的配置参数
      console.log(`🔴 [XianyuAutomation] 检查设备配置，deviceId: ${targetDeviceId}`)
      if (this.deviceConfigs[targetDeviceId]) {
        this.$delete(this.deviceConfigs, targetDeviceId)
        console.log(`🔴 [XianyuAutomation] 已清理设备 ${targetDeviceId} 的配置参数`)
      }

      // 3. 如果移除的是当前活跃标签，切换到第一个设备
      if (this.activeDeviceTab === targetDeviceId) {
        if (this.selectedDevices.length > 0) {
          this.activeDeviceTab = this.selectedDevices[0]
          console.log(`🔴 [XianyuAutomation] 活跃标签页已切换到: ${this.activeDeviceTab}`)
        } else {
          this.activeDeviceTab = ''
          console.log(`🔴 [XianyuAutomation] 已清空活跃标签页`)
        }
      }

      // 4. 清理该设备的执行状态（通过Vuex）
      this.clearDeviceExecutionState(targetDeviceId)

      // 5. 清理该设备的脚本状态（如果正在执行脚本）
      if (this.executing) {
        // 检查是否还有其他设备在执行
        const hasOtherExecutingDevices = this.selectedDevices.length > 0
        if (!hasOtherExecutingDevices) {
          console.log(`🔴 [XianyuAutomation] 没有其他设备在执行，重置执行状态`)
          this.executing = false
        }
      }

      // 6. 通知配置组件设备离线
      this.$root.$emit('device-offline', {
        deviceId: targetDeviceId
      })

      // 7. 通知所有配置组件清理该设备的状态
      this.$root.$emit('device-disconnected', {
        deviceId: targetDeviceId,
        timestamp: new Date()
      })

      // 8. 保存页面状态
      this.savePageState()

      // 9. 刷新设备列表
      this.loadDevices()

      console.log(`🔴 [XianyuAutomation] 设备断开连接处理完成，当前状态:`)
      console.log(`🔴 [XianyuAutomation] - 选中设备:`, this.selectedDevices)
      console.log(`🔴 [XianyuAutomation] - 设备配置:`, Object.keys(this.deviceConfigs))
      console.log(`🔴 [XianyuAutomation] - 活跃标签:`, this.activeDeviceTab)
      console.log(`🔴 [XianyuAutomation] - 执行状态:`, this.executing)
    },

    // 处理服务器关闭事件
    handleServerShutdown(data) {
      console.log('🔴 [XianyuAutomation] 收到服务器关闭事件:', data)

      // 清理所有设备选择
      this.selectedDevices = []

      // 清理所有设备配置
      this.deviceConfigs = {}

      // 清空活跃标签页
      this.activeDeviceTab = ''

      // 重置全局执行状态
      this.$store.dispatch('xianyu/resetGlobalExecutionState')

      // 通知所有配置组件服务器关闭
      this.$root.$emit('server-shutdown', data)

      // 显示提示信息
      this.$message.warning('服务器已关闭，所有设备连接已断开')

      console.log('✅ [XianyuAutomation] 服务器关闭处理完成')
    },

    // 处理Socket连接断开事件
    handleSocketDisconnect(reason) {
      console.log('🔌 [XianyuAutomation] Socket连接断开:', reason)

      // 如果是服务器关闭导致的断开，清理所有设备状态
      if (reason === 'io server disconnect' || reason === 'transport close') {
        console.log('🔌 [XianyuAutomation] 检测到服务器关闭，清理所有设备状态')

        // 获取当前选中的设备列表副本
        const devicesToClean = [...this.selectedDevices]

        // 清理所有选中的设备
        devicesToClean.forEach(deviceId => {
          console.log(`🔌 [XianyuAutomation] 清理设备: ${deviceId}`)
          this.handleDeviceDisconnected(deviceId)
        })

        // 如果还有剩余的设备选择，强制清空
        if (this.selectedDevices.length > 0) {
          console.log('🔌 [XianyuAutomation] 强制清空剩余设备选择')
          this.selectedDevices = []
          this.deviceConfigs = {}
          this.activeDeviceTab = ''
        }

        // 重置全局执行状态
        this.$store.dispatch('xianyu/resetGlobalExecutionState')

        // 显示提示信息
        this.$message.warning('服务器连接已断开，所有设备状态已清理')
      }
    },

    // 清理指定设备的执行状态
    clearDeviceExecutionState(deviceId) {
      console.log(`闲鱼自动化: 清理设备 ${deviceId} 的执行状态`)

      // 通过Vuex清理该设备相关的执行状态
      this.$store.dispatch('xianyu/clearDeviceExecutionState', deviceId)

      // 如果当前没有其他设备在执行，重置执行状态
      if (this.selectedDevices.length === 0) {
        console.log('闲鱼自动化: 没有其他设备，重置执行状态')
        this.executing = false
        this.executionProgress = 0
        this.executionStatus = ''
        this.executionLogs = []
        this.executionResult = null
      }
    },

    handleTaskStartedFromBackend(data) {
      console.log('闲鱼自动化: 收到后端任务开始事件', data)

      // 转发事件给配置组件（使用连字符命名）
      this.$root.$emit('xianyu-task-started', {
        functionType: data.functionType,
        deviceId: data.deviceId,
        taskId: data.taskId,
        config: data.config,
        message: data.message || '任务开始执行'
      })

      console.log('闲鱼自动化: 已转发任务开始事件给配置组件')
    },

    handleStatusUpdate(data) {
      console.log('闲鱼自动化: 收到脚本状态更新', data)
      console.log('闲鱼自动化: 状态更新详情 - stage:', data.stage, 'status:', data.status, 'progress:', data.progress)

      // 根据状态阶段发送相应的事件给配置组件
      if (data.stage === 'executing' || data.stage === 'running') {
        // 脚本正在执行，发送开始事件
        this.$root.$emit('xianyu-task-started', {
          functionType: 'keywordMessage',
          deviceId: data.deviceId,
          taskId: data.taskId,
          message: data.message || '脚本正在执行'
        })
      } else if (data.stage === 'completed') {
        // 脚本执行完成
        this.$root.$emit('xianyu-task-completed', {
          functionType: 'keywordMessage',
          deviceId: data.deviceId,
          taskId: data.taskId,
          message: data.message || '脚本执行完成'
        })
      } else if (data.stage === 'error') {
        // 脚本执行出错
        this.$root.$emit('xianyu-task-stopped', {
          functionType: 'keywordMessage',
          deviceId: data.deviceId,
          taskId: data.taskId,
          message: data.message || '脚本执行出错'
        })
      }

      // 同时更新Vuex状态
      if (data.stage === 'executing' || data.stage === 'running') {
        // 使用taskStarted方法确保设备信息被正确保存
        this.$store.dispatch('xianyu/taskStarted', {
          functionType: 'keywordMessage',
          taskId: data.taskId,
          logId: data.logId,
          deviceId: data.deviceId,
          config: data.config || {}
        })
      } else if (data.stage === 'completed' || data.stage === 'error' || data.stage === 'failed') {
        console.log('闲鱼自动化: 脚本执行结束，重置状态 - stage:', data.stage)

        this.$store.dispatch('xianyu/setScriptRunning', {
          functionType: 'keywordMessage',
          isRunning: false
        })

        if (data.stage === 'completed') {
          this.$store.dispatch('xianyu/setScriptCompleted', {
            functionType: 'keywordMessage',
            isCompleted: true
          })
        }

        // 清理设备任务状态
        if (data.deviceId) {
          this.$store.dispatch('xianyu/clearDeviceExecutionState', data.deviceId)
        }

        // 发送任务完成事件给配置组件，无论是完成、错误还是失败
        this.$root.$emit('xianyu-task-completed', {
          functionType: 'keywordMessage',
          deviceId: data.deviceId,
          taskId: data.taskId,
          status: data.stage,
          message: data.message || '脚本执行结束'
        })
      }

      console.log('闲鱼自动化: 已处理状态更新事件', data.stage)
    },

    handleTaskCompleted(data) {
      console.log('闲鱼任务完成:', data)
      console.log('闲鱼自动化: 处理任务完成事件，发送给配置组件')
      this.$root.$emit('xianyu-task-completed', data)
    },

    handleTaskStopped(data) {
      console.log('闲鱼任务停止:', data)

      // 清理Vuex状态
      if (data.deviceId) {
        console.log('闲鱼自动化: 清理设备执行状态', data.deviceId)
        this.$store.dispatch('xianyu/clearDeviceExecutionState', data.deviceId)

        // 强制更新设备状态为在线
        this.$store.dispatch('device/updateDeviceStatus', {
          deviceId: data.deviceId,
          status: 'online',
          lastSeen: new Date()
        })

        // 刷新设备列表确保状态同步
        this.loadDevices()
      }

      // 转发事件给配置组件
      this.$root.$emit('xianyu-task-stopped', data)
    },

    // 处理全局任务停止事件（来自其他页面）
    handleGlobalTaskStopped(data) {
      console.log('闲鱼自动化: 收到全局任务停止事件', data)
      
      // 清理Vuex状态
      if (data.deviceId) {
        console.log('闲鱼自动化: 清理设备执行状态', data.deviceId)
        this.$store.dispatch('xianyu/clearDeviceExecutionState', data.deviceId)
      }
      
      // 清空设备选择
      if (data.deviceId && this.selectedDevices.includes(data.deviceId)) {
        const index = this.selectedDevices.indexOf(data.deviceId)
        this.selectedDevices.splice(index, 1)
        console.log('闲鱼自动化: 从选中列表中移除已停止的设备', data.deviceId)
      }
      
      // 刷新设备列表
      this.loadDevices()
    },

    // 处理全局重置状态事件
    handleGlobalResetStates() {
      console.log('闲鱼自动化: 收到全局重置状态事件')
      
      // 重置Vuex状态
      this.$store.dispatch('xianyu/resetAllStates')
      
      // 清空设备选择
      this.selectedDevices = []
      this.activeDeviceTab = ''
      
      // 重置执行状态
      this.executing = false
      
      // 刷新设备列表
      this.loadDevices()
      
      // 通知配置组件重置状态
      this.$root.$emit('xianyu-reset-all-states')
    },

    handleExecutionCompleted(data) {
      console.log('闲鱼脚本执行完成:', data)
      console.log('闲鱼自动化: 处理脚本执行完成事件，重置状态')

      // 通知配置组件重置状态
      this.$root.$emit('xianyu-task-completed', {
        functionType: 'keywordMessage', // 默认功能类型
        deviceId: data.deviceId,
        taskId: data.taskId,
        status: data.status,
        message: data.message,
        timestamp: data.timestamp
      })

      // 如果有设备ID，清理该设备的执行状态
      if (data.deviceId) {
        this.clearDeviceExecutionState(data.deviceId)
      }
    },

    savePageState() {
      const state = {
        selectedFunction: this.selectedFunction,
        selectedDevices: this.selectedDevices,
        activeDeviceTab: this.activeDeviceTab,
        deviceConfigs: this.deviceConfigs
      }
      localStorage.setItem('xianyu_automation_state', JSON.stringify(state))
    },

    async restorePageState() {
      try {
        const saved = localStorage.getItem('xianyu_automation_state')
        if (saved) {
          const state = JSON.parse(saved)
          this.selectedFunction = state.selectedFunction || ''

          // 恢复设备选择时，需要智能处理设备状态
          const savedDevices = state.selectedDevices || []
          if (savedDevices.length > 0) {
            // 等待设备列表加载完成
            await this.loadDevices()

            // 获取当前可用的设备（在线设备 + 正在执行当前功能的忙碌设备）
            const availableDeviceIds = this.devices
              .filter(device => {
                if (device.status === 'online') return true
                if (device.status === 'busy' && this.selectedFunction) {
                  // 检查设备是否正在执行当前功能
                  return this.isDeviceExecutingCurrentFunction(device.device_id)
                }
                return false
              })
              .map(device => device.device_id)

            console.log('闲鱼自动化: 恢复页面状态 - 可用设备:', availableDeviceIds)
            console.log('闲鱼自动化: 恢复页面状态 - 保存的设备:', savedDevices)

            // 只保留仍然可用的设备
            this.selectedDevices = savedDevices.filter(deviceId =>
              availableDeviceIds.includes(deviceId)
            )

            // 如果有设备被过滤掉，显示提示
            const filteredCount = savedDevices.length - this.selectedDevices.length
            if (filteredCount > 0) {
              console.log(`闲鱼自动化: 已过滤 ${filteredCount} 个不可用设备`)
              this.$message.warning(`已过滤 ${filteredCount} 个不可用设备`)
            }

            console.log('闲鱼自动化: 恢复页面状态 - 最终选中设备:', this.selectedDevices)
          } else {
            this.selectedDevices = []
          }

          this.activeDeviceTab = state.activeDeviceTab || ''
          this.deviceConfigs = state.deviceConfigs || {}

          // 如果活跃标签对应的设备不在选中列表中，清空标签
          if (this.activeDeviceTab && !this.selectedDevices.includes(this.activeDeviceTab)) {
            this.activeDeviceTab = ''
          }

          // 如果选择了功能但没有选中设备，尝试自动恢复正在执行的设备
          if (this.selectedFunction && this.selectedDevices.length === 0) {
            console.log('闲鱼自动化: 尝试自动恢复正在执行的设备')
            this.autoRestoreExecutingDevices()
          }
        }
      } catch (e) {
        console.warn('恢复页面状态失败:', e)
      }
    },

    // 恢复脚本执行状态
    async restoreExecutionState() {
      try {
        console.log('闲鱼自动化: 开始恢复脚本执行状态...')

        // 调用Vuex action恢复状态
        await this.$store.dispatch('xianyu/restoreExecutionState')

        // 通知配置组件恢复状态
        this.$root.$emit('xianyu-restore-state')

        console.log('闲鱼自动化: 脚本执行状态恢复完成')
      } catch (error) {
        console.error('恢复脚本执行状态失败:', error)
      }
    },

    startAutoRefresh() {
      // 每6秒刷新一次设备信息
      this.refreshTimer = setInterval(() => {
        this.loadDevices()
      }, 6000)
    },

    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    // 获取设备标签页标签（包含状态）
    getDeviceTabLabel(deviceId) {
      // 确保devices数组存在且不为空
      if (!this.devices || !Array.isArray(this.devices) || this.devices.length === 0) {
        return deviceId
      }

      const device = this.devices.find(d => d.device_id === deviceId)
      if (!device) return deviceId

      const statusIcon = device.status === 'online' ? '🟢' :
                        device.status === 'busy' ? '🟡' : '🔴'
      return `${statusIcon} ${device.device_name}`
    },

    // 执行单个设备
    async executeForDevice(deviceId) {
      if (!this.isDeviceConfigValid(deviceId)) {
        this.$message.warning('请先完善设备配置')
        return
      }

      this.executing = true

      try {
        // 获取设备配置
        const configRef = this.$refs[`config_${deviceId}`]
        if (!configRef || !configRef[0]) {
          this.$message.error('无法获取设备配置')
          return
        }

        const deviceConfig = configRef[0].getConfig()

        // 生成前端taskId，确保与服务器同步
        const frontendTaskId = `xianyu_${this.selectedFunction}_${Date.now()}_${deviceId}`

        // 构建执行参数
        const params = {
          functionType: this.selectedFunction,
          deviceConfigs: { [deviceId]: deviceConfig },
          deviceIds: [deviceId],
          taskId: frontendTaskId  // 添加前端生成的taskId
        }

        console.log('执行单个设备:', deviceId, params)

        // 调用执行API
        const response = await this.$http.post('/api/xianyu/execute', params)

        if (response.data.success) {
          this.$message.success(`设备 ${this.getDeviceName(deviceId)} 开始执行`)
          console.log('单设备执行成功:', response.data)

          // 立即发送任务开始事件，确保配置组件状态更新
          // 使用与服务器端一致的taskId格式
          const deviceTaskId = `${frontendTaskId}_${deviceId}`
          console.log('=== 立即发送单设备任务开始事件 ===')
          this.$root.$emit('xianyu-task-started', {
            functionType: this.selectedFunction,
            deviceId: deviceId,
            taskId: deviceTaskId,
            config: deviceConfig,
            message: `设备 ${this.getDeviceName(deviceId)} 开始执行闲鱼自动化任务`
          })
        } else {
          this.$message.error('执行失败: ' + response.data.message)
          console.error('单设备执行失败:', response.data)
        }
      } catch (error) {
        console.error('执行单设备失败:', error)
        this.$message.error('执行失败: ' + (error.response?.data?.message || error.message))
      } finally {
        this.executing = false
      }
    },

    // 批量执行所有设备
    async executeAllDevices() {
      if (!this.hasValidConfigs()) {
        this.$message.warning('请先完善设备配置')
        return
      }

      this.executing = true

      try {
        // 收集所有有效设备的配置
        const deviceConfigs = {}
        const validDeviceIds = []

        for (const deviceId of this.selectedDevices) {
          if (this.isDeviceConfigValid(deviceId)) {
            const configRef = this.$refs[`config_${deviceId}`]
            if (configRef && configRef[0]) {
              deviceConfigs[deviceId] = configRef[0].getConfig()
              validDeviceIds.push(deviceId)
            }
          }
        }

        // 生成批量执行的taskId
        const batchTaskId = `xianyu_${this.selectedFunction}_batch_${Date.now()}`

        // 构建执行参数
        const params = {
          functionType: this.selectedFunction,
          deviceConfigs: deviceConfigs,
          deviceIds: validDeviceIds,
          taskId: batchTaskId  // 添加批量taskId
        }

        console.log('批量执行所有设备:', params)

        // 调用执行API
        const response = await this.$http.post('/api/xianyu/execute', params)

        if (response.data.success) {
          this.$message.success(`已开始批量执行 ${validDeviceIds.length} 个设备`)
          console.log('批量执行成功:', response.data)

          // 为每个设备立即发送任务开始事件
          console.log('=== 立即发送批量任务开始事件 ===')
          validDeviceIds.forEach(deviceId => {
            // 使用与服务器端一致的taskId格式
            const deviceTaskId = `${batchTaskId}_${deviceId}`
            this.$root.$emit('xianyu-task-started', {
              functionType: this.selectedFunction,
              deviceId: deviceId,
              taskId: deviceTaskId,
              config: deviceConfigs[deviceId],
              message: `设备 ${this.getDeviceName(deviceId)} 开始执行闲鱼自动化任务`
            })
          })
        } else {
          this.$message.error('批量执行失败: ' + response.data.message)
          console.error('批量执行失败:', response.data)
        }
      } catch (error) {
        console.error('批量执行失败:', error)
        this.$message.error('批量执行失败: ' + (error.response?.data?.message || error.message))
      } finally {
        this.executing = false
      }
    },

    // 复制当前配置到所有设备
    copyConfigToAll() {
      console.log('🔧 开始复制配置到所有设备...')
      console.log('当前活动设备:', this.activeDeviceTab)
      console.log('选中的设备:', this.selectedDevices)

      if (!this.activeDeviceTab || this.selectedDevices.length <= 1) {
        this.$message.warning('请先选择要复制的设备配置')
        return
      }

      const sourceConfigRef = this.$refs[`config_${this.activeDeviceTab}`]
      console.log('源设备配置组件引用:', sourceConfigRef)

      if (!sourceConfigRef || !sourceConfigRef[0]) {
        this.$message.error('无法获取源设备配置')
        console.error('❌ 无法获取源设备配置组件')
        return
      }

      const sourceConfig = sourceConfigRef[0].getConfig()
      console.log('📋 源设备配置:', sourceConfig)

      if (!sourceConfig || Object.keys(sourceConfig).length === 0) {
        this.$message.warning('源设备配置为空，无法复制')
        console.warn('⚠️ 源设备配置为空')
        return
      }

      // 复制到其他设备
      let copiedCount = 0
      let failedDevices = []

      for (const deviceId of this.selectedDevices) {
        if (deviceId !== this.activeDeviceTab) {
          console.log(`🔧 正在复制配置到设备: ${deviceId}`)

          const targetConfigRef = this.$refs[`config_${deviceId}`]
          console.log(`设备 ${deviceId} 配置组件引用:`, targetConfigRef)

          if (targetConfigRef && targetConfigRef[0] && targetConfigRef[0].setConfig) {
            try {
              targetConfigRef[0].setConfig(sourceConfig)
              copiedCount++
              console.log(`✅ 配置已复制到设备: ${deviceId}`)

              // 验证复制结果
              const copiedConfig = targetConfigRef[0].getConfig()
              console.log(`设备 ${deviceId} 复制后的配置:`, copiedConfig)
            } catch (error) {
              console.error(`❌ 复制配置到设备 ${deviceId} 失败:`, error)
              failedDevices.push(deviceId)
            }
          } else {
            console.error(`❌ 设备 ${deviceId} 的配置组件无效`)
            failedDevices.push(deviceId)
          }
        }
      }

      console.log(`📊 复制结果: 成功 ${copiedCount} 个, 失败 ${failedDevices.length} 个`)

      if (copiedCount > 0) {
        let message = `已复制配置到 ${copiedCount} 个设备`
        if (failedDevices.length > 0) {
          message += `，${failedDevices.length} 个设备复制失败`
        }
        this.$message.success(message)
      } else {
        this.$message.warning('没有可复制的目标设备或复制失败')
      }
    },

    // 重置所有状态
    resetAllStates() {
      this.$confirm('确定要重置所有脚本执行状态吗？', '确认重置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重置Vuex状态
        this.$store.dispatch('xianyu/resetAllStates')

        // 发送重置事件给所有配置组件
        this.$root.$emit('xianyu-reset-all-states')

        this.$message.success('所有状态已重置')
      }).catch(() => {
        // 用户取消
      })
    },

    // 清理测试数据
    cleanupTestData() {
      this.$confirm('确定要清理所有测试数据吗？这将终止所有任务并清空执行日志！', '确认清理', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 1. 先终止所有任务
          const stopResponse = await this.$http.post('/api/xianyu/stop-all')
          console.log('终止所有任务结果:', stopResponse.data)

          // 2. 等待1秒让任务状态更新
          await new Promise(resolve => setTimeout(resolve, 1000))

          // 3. 清空所有日志
          const clearResponse = await this.$http.delete('/api/xianyu/logs')
          console.log('清空日志结果:', clearResponse.data)

          // 4. 重置前端状态
          this.$store.dispatch('xianyu/resetAllStates')
          this.$root.$emit('xianyu-reset-all-states')

          this.$message.success('测试数据清理完成！')
        } catch (error) {
          console.error('清理测试数据失败:', error)
          this.$message.error('清理失败: ' + (error.response?.data?.message || error.message))
        }
      }).catch(() => {
        // 用户取消
      })
    }
  }
}
</script>

<style scoped>
.xianyu-automation {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-actions .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.function-cards {
  margin-bottom: 30px;
}

.function-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.function-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.function-card.active {
  border-color: #409EFF;
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
}

.card-content {
  text-align: center;
  padding: 20px;
}

.function-icon {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 15px;
  display: block;
}

.card-content h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #303133;
}

.card-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.config-panel {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.config-section {
  margin-bottom: 20px;
}

.config-section h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #606266;
  font-weight: 600;
}

.no-component {
  margin-bottom: 20px;
}

.execution-controls {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
  text-align: center;
}

.execution-tip {
  margin-left: 10px;
  color: #E6A23C;
  font-size: 12px;
}

.device-stats {
  margin-top: 30px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  background: #409EFF;
  color: white;
  font-size: 24px;
}

.stat-icon.online {
  background: #67C23A;
}

.stat-icon.busy {
  background: #E6A23C;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .xianyu-automation {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .header-actions {
    margin-top: 20px;
  }

  .function-cards .el-col {
    margin-bottom: 20px;
  }

  .config-section .el-col {
    margin-bottom: 20px;
  }
}

/* 多设备配置样式 */
.device-config-tabs {
  margin-top: 20px;
}

.device-config-content {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 4px;
  margin-top: 10px;
}

.device-config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.device-config-header h5 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.batch-execute-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  text-align: center;
}

.batch-execute-section .el-button {
  margin: 0 10px;
}

/* 设备标签页样式优化 */
.device-config-tabs .el-tabs__header {
  margin-bottom: 0;
}

.device-config-tabs .el-tabs__item {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
}

.device-config-tabs .el-tabs__content {
  padding: 0;
}
</style>
